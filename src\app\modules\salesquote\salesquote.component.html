
  
<div class="headSection">
  <h3 class="headtitle">Sales Quote</h3>
  <hr>
</div>
<div class="container">
  <div class="header"  >
    <div class="buttons">
      <button (click)="view()">View</button>
      <button (click)="edit()">Edit</button>
      <button (click)="convertToInvoice()">Convert to Invoice</button>
    </div>
  </div>
  <div class="details">
    <div>
    <p style="font-weight:bold;">Customer Details</p>
    <p>MyDent Dental Clinic</p>
    <p>Abc road, Colombo 12</p>
    <p>011 2 700 700</p>
    </div>
    <div style="margin-left: 60%;">
    <p>Order Number<p style="font-weight:600; font-size: smaller; margin-left: 20%;"> OD12455478</p>
    </div>
  </div>
  <br><br>
  <div class="form-group">
    <label for="purchaseRequest">Select Purchase Request</label><br>
    <select id="purchaseRequest">
      <option>Item 01</option>
    </select>
    <br>
  </div>
  <div style="background-color: rgb(255, 255, 255); padding: 3%; box-shadow: rgb(177, 177, 177) 0px 0px 10px; border-radius: 10px;">
  <table>
    <thead style="background-color: rgb(207, 207, 207);">
      <tr>
        <th>Item</th>
        <th>Item Category</th>
        <th>Qty</th>
        <th>Price</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Clinic Item</td>
        <td>Clinic Item</td>
       <td style="width: 10%;">
    <input type="number" value="2" style="width: 100%; border-radius: 5px; text-align: center;" />
</td>

        <td>
          <select style="padding: 3px; border-radius: 5px;">
            <option >LKR</option>
          </select>
          <input type="number" value="2200" style="margin-left: 10px; border-radius: 5px;" />
        </td>
      </tr>
      <tr>
        <td>Clinic Item</td>
        <td>Clinic Item</td>
        <td style="width: 10%;">
    <input type="number" value="2" style="width: 100%; border-radius: 5px; text-align: center;" />
</td>

        <td>
          <select style="padding: 3px; border-radius: 5px;">
            <option>LKR</option>
          </select>
          <input type="number" value="2200" style="margin-left: 10px; border-radius: 5px;" />
        </td>
      </tr>
    </tbody>
  </table>
  </div>
  <br>
  <div style="display: flex;">
  <div class="remarks">
    <label for="remarks">Remarks</label>
    <br>
    <textarea id="remarks"></textarea>
  </div>
  <div style="margin-left: 20%;">
  <div class="totals">
    <br>
    <div>
      <label for="itemDiscount" style="font-size: 15px; margin-bottom: 3px;">Item Discount</label><br>
      <input class="inputs" type="number" id="itemDiscount" value="220" />
    </div>
     <br>
    <div>
      <label for="grossTotal " style="font-size: 15px; margin-bottom: 3px;">Gross Total</label><br>
      <input class="inputs" style="background-color: rgb(215, 208, 208);" type="number" id="grossTotal" value="220" />
    </div>
     <br>
    <div>
      <label for="discountOnTotal" style="font-size: 15px; margin-bottom: 3px;">Discount on Total</label><br>
      <input class="inputs" type="number" id="discountOnTotal" value="220" />
    </div>
     <br>
    <div>
      <label for="netTotal" style="font-size: 15px; margin-bottom: 3px;">Net Total</label>
      <br>
      <input class="inputs" style="background-color: rgb(215, 208, 208);" type="number" id="netTotal" value="220" />
    </div>
    </div>
  </div>
  </div>
  <button class="send-button" >Send</button>
</div>
