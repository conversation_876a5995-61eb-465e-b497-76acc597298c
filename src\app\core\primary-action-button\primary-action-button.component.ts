import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';

@Component({
  selector: 'app-primary-action-button',
  templateUrl: './primary-action-button.component.html',
  styleUrls: ['./primary-action-button.component.css']
})
export class PrimaryActionButtonComponent implements OnInit,AfterViewInit{

  @Input({required:true}) buttonText:String = 'buttonText';
  @Input({required:true}) buttonUI:'primary' | 'secondary' | 'custom' = 'primary'
  @Input({required:true}) buttonType:'submit' | 'button' | 'reset' |'menu' = 'button'
  @Input({required:false}) buttonBackground:string = 'black';
  @Input({required:false}) disabled:boolean | null = false;
  @Input({required:false}) buttonForeground:String = 'white';
  @Input({required:false}) buttonHeight:number = 40;
  @Output() buttonClicked = new EventEmitter<void>();

  protected buttonClass:string = '';
  @ViewChild("button") buttonInstance!:ElementRef<HTMLButtonElement>

  onButtonClick() {
    this.buttonClicked.emit();
  }

  ngOnInit(): void {
    if (this.buttonUI == 'primary') {
      this.buttonClass = 'primary-button'

    }
    else if(this.buttonUI == 'secondary') {
      this.buttonClass = 'secondary-button'
    }
    else{
      this.buttonClass=''
    }
  }

  ngAfterViewInit(): void {
      this.buttonInstance.nativeElement.style.height = this.buttonHeight+"px"
  }

}
