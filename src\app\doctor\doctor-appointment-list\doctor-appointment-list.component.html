<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12">
        <header>
          <h1 class="header-text px-1 pb-2">My Appointments</h1>
          <hr class="my-4 header-break" />
        </header>
      </div>
      <div class="col-12">
        <div class="row">
          <div class="col-12 g-0">
            <div class="card-body">
              <br />

              <section
                class="example-container"
                tabindex="0"
                style="
                  border-radius: 18px;
                  box-shadow: 0px 3px 11.9px -1px rgba(0, 0, 0, 0.25);
                "
              >
                <table
                  mat-table
                  [dataSource]="dataSource"
                  matSort
                  style="
                    background-color: transparent;
                    margin-bottom: 50px !important;
                  "
                >
                  <!-- Patient Name Column -->
                  <ng-container matColumnDef="patientName">
                    <th
                      class="cell-header"
                      mat-header-cell
                      style="border-start-start-radius: 18px"
                      *matHeaderCellDef
                      mat-sort-header
                    >
                      Patient Name
                    </th>
                    <td
                      style="font-weight: 500"
                      mat-cell
                      *matCellDef="let element"
                    >
                      {{ element.patientName }}
                    </td>
                  </ng-container>

                  <!-- Clinic Address Column -->
                  <ng-container matColumnDef="clinicAddress">
                    <th
                      class="cell-header"
                      mat-header-cell
                      *matHeaderCellDef
                      mat-sort-header
                    >
                      Clinic Address
                    </th>
                    <td mat-cell *matCellDef="let element">
                      {{ element.clinicAddress }}
                    </td>
                  </ng-container>

                  <!-- Time Appointment Number Column -->
                  <ng-container matColumnDef="time">
                    <th
                      class="cell-header"
                      mat-header-cell
                      *matHeaderCellDef
                      mat-sort-header
                    >
                      Time
                    </th>
                    <td mat-cell *matCellDef="let element">
                      {{ element.time }}
                    </td>
                  </ng-container>

                  <!-- Time Appointment Number Column -->
                  <ng-container matColumnDef="date">
                    <th
                      class="cell-header"
                      mat-header-cell
                      *matHeaderCellDef
                      mat-sort-header
                    >
                      Date
                    </th>
                    <td mat-cell *matCellDef="let element">
                      {{ element.time }}
                    </td>
                  </ng-container>

                  <!-- App No Number Column -->
                  <ng-container matColumnDef="appNo">
                    <th
                      class="cell-header"
                      mat-header-cell
                      *matHeaderCellDef
                      mat-sort-header
                    >
                      App No
                    </th>
                    <td mat-cell *matCellDef="let element">
                      {{ element.appNo }}
                    </td>
                  </ng-container>

                  <!-- Status Column -->
                  <ng-container matColumnDef="status">
                    <th
                      class="cell-header"
                      mat-header-cell
                      style="border-start-end-radius: 18px"
                      *matHeaderCellDef
                      mat-sort-header
                    ></th>
                    <td mat-cell *matCellDef="let element">
                      <span [ngSwitch]="element.status">
                        <p
                          style="
                            font-style: italic;
                            font-weight: 500;
                            text-align: center;
                          "
                          *ngSwitchCase="'Pending'"
                        >
                          <span
                            ><i
                              class="bi bi-check-circle fs-5"
                              style="
                                color: rgba(0, 200, 32, 1);
                                cursor: pointer;
                              "
                              (click)="onConfirm(element.appNo)"
                            ></i
                          ></span>
                          &nbsp;&nbsp;&nbsp;
                          <span
                            ><i
                              class="bi bi-x-circle fs-5"
                              style="
                                color: rgba(185, 52, 38, 1);
                                cursor: pointer;
                              "
                              (click)="onRejection(element.appNo)"
                            ></i
                          ></span>
                        </p>
                        <p
                          style="
                            font-style: italic;
                            font-weight: 500;
                            text-align: center;
                            color: rgba(0, 200, 32, 1);
                          "
                          *ngSwitchCase="'Confirmed'"
                        >
                          {{ element.status }}
                        </p>
                        <p
                          style="
                            font-style: italic;
                            font-weight: 500;
                            text-align: center;
                            color: rgba(185, 52, 38, 1);
                          "
                          *ngSwitchCase="'Rejected'"
                        >
                          {{ element.status }}
                        </p>
                        <p
                          style="
                            font-style: italic;
                            font-weight: 500;
                            text-align: center;
                            color: gray;
                          "
                          *ngSwitchDefault
                        >
                          {{ element.status }}
                        </p>
                      </span>
                    </td>
                  </ng-container>

                  <!-- Header and Row Definitions -->
                  <tr
                    mat-header-row
                    *matHeaderRowDef="displayedColumns; sticky: false"
                  ></tr>
                  <tr
                    mat-row
                    *matRowDef="let row; columns: displayedColumns"
                  ></tr>
                </table>

                <!-- Loading Spinner -->
                <div *ngIf="isLoading == 1" class="spinner-container">
                  <mat-progress-spinner
                    color="primary"
                    mode="indeterminate"
                  ></mat-progress-spinner>
                </div>

              </section>

              <mat-paginator
                [pageSizeOptions]="[25, 50, 100]"
                [showFirstLastButtons]="dataSource.data.length > 8"
              ></mat-paginator>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
