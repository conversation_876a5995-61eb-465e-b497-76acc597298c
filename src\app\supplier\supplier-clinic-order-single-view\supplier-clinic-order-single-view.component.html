<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12" *ngIf="supplierOrderHeader && supplierOrderDetailsList">
        <!-- Header -->
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-6">
                <div class="row">
                  <div class="col-12 d-flex">
                    <label class="fs-5 my-auto" style="font-weight: 600">Single Order View </label>&nbsp;&nbsp;<label class="single-order-view-button px-2 py-1 my-auto"> {{supplierOrderHeaderId}}</label>
                  </div>
                  <div class="col-12">
                    <p class="text-black-50" style="font-size: 12px;">Load All Item Requests By Clinics</p>
                  </div>
                </div>
              </div>
              <div class="col-6 position-relative">
                <input type="text" placeholder="Search from Here" class="search-input w-50 position-absolute text-start" style="right: 0;" name="" id="">
              </div>
            </div>
            <div class="row my-4">
              <hr class="border-secondary">
            </div>
          </div>
        </div>
        <!-- Header -->

        <!-- Requested Clinic Details -->
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-12 gx-3">
                <p class="text-black-50" style="font-weight: 500;font-size: 14px;">Order Head</p>
              </div>
              <div class="col-12 p-4 py-3 mt-3" style="border-radius: 5px;background-color: rgb(252,252,252);border: 1px solid rgb(240,240,240);">
                <div class="row my-1">
                  <div class="col-6 my-auto">
                    <p class="text-black-50" style="font-size: 12px;">Clinic Details</p>
                    <h6 class="text-dark" style="font-weight: 500;font-size: 14px;">{{supplierOrderHeader.clinic.name +' - '+supplierOrderHeader.clinic.city}}</h6>
                  </div>
                  <div class="col-4 my-auto text-start px-5" style="border-inline: 1px solid  rgb(200,200,200);">
                    <p class="text-black-50" style="font-size: 12px;">Date & Time</p>
                    <h6 class="text-dark" style="font-weight: 500;font-size: 14px;" [innerHTML]="this.formattedDate"></h6>
                  </div>
                  <div class="col-2 text-center my-auto">
                    <p class="text-black-50" style="font-size: 12px;">Status</p>
                    <h6 class="text-dark text-uppercase" style="font-weight: 600;font-size: 14px;">{{orderStatus}}</h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Requested Clinic Details -->

        <!-- Requested Clinic Order Details -->
        <div class="row mt-5">
          <div class="col-12">
            <div class="row">
              <div class="col-12 gx-3">
                <p class="text-black-50" style="font-weight: 500;font-size: 14px;">Order Details</p>
              </div>
              <div class="col-12 p-3 py-2 card-table-header mt-3" style="border-radius: 5px;">
                <div class="row  my-1">
                  <div class="col-6 d-flex my-auto">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Item Details - ( Item Code )</h6>
                  </div>
                  <div class="col-2 my-auto text-center" style="border-inline: 1px solid  white;">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Item Qty</h6>
                  </div>
                  <div class="col-4 text-center my-auto">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Selling Price</h6>
                  </div>
                </div>
              </div>
              <form *ngIf="supplierOrderDetailsList" [formGroup]="orderForm" (ngSubmit)="onSubmit()" class="g-0">
                <div *ngFor="let supplierOrderDetail of supplierOrderDetailsList" class="col-12 p-4 py-3 mt-3" style="border-radius: 5px; background-color: rgb(252,252,252); border: 1px solid rgb(240,240,240);">
                  <div class="row my-1">
                    <div class="col-6 my-auto">
                      <p class="my-auto text-black-50" style="font-weight: 500; font-size: 12px;">
                        {{ supplierOrderDetail.supplierInventory.category }}
                      </p>
                      <h6 class="my-auto text-dark" style="font-weight: 500; font-size: 14px;">
                        {{ supplierOrderDetail.supplierInventory.description + " ( " + supplierOrderDetail.supplierInventory.supplierInventoryId + " )" }}
                      </h6>
                    </div>

                    <div class="col-2 m-auto text-center px-5" style="border-inline: 1px solid rgb(220,220,220);">
                      <div class="row position-relative ">
                        <div class="col-12 g-0 text-end">
                          <p class="text-black-50 px-1 py-2" style="font-size: 11px;">
                            MQT : <strong>{{ supplierOrderDetail.supplierInventory.quantity }}</strong>
                          </p>
                          <input type="number" readonly formControlName="{{ 'qty-' + supplierOrderDetail.supplierOrderDetailsId }}" class="search-input position-relative bg-white w-100"/>
                        </div>
                      </div>
                    </div>

                    <div class="col-4 m-auto text-center px-5">
                      <div class="row position-relative">
                        <div class="col-12 g-0 text-end">
                          <p class="text-black-50 px-1 py-2" style="font-size: 11px;">
                            MAT : <strong>{{ supplierOrderDetail.supplierInventory.price | number }}.00</strong>
                          </p>
                          <input type="number" formControlName="price-{{supplierOrderDetail.supplierOrderDetailsId}}" class="search-input w-100 bg-white position-relative"
                            [ngClass]="{ 'is-invalid': submitted && orderForm.get('price-' + supplierOrderDetail.supplierOrderDetailsId)?.invalid }" />
                          <div *ngIf="submitted && orderForm.get('price-' + supplierOrderDetail.supplierOrderDetailsId)?.errors" class="invalid-feedback text-start">
                            <div style="font-size: 13px;font-weight: 500;" *ngIf="orderForm.get('price-' + supplierOrderDetail.supplierOrderDetailsId)?.errors?.['invalidPrice']">
                              Price must be more than 0 and less than the actual value.
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 text-end mt-5">
                  <app-primary-action-button class="me-4" buttonText="Clear" buttonType="reset" buttonUI='secondary'></app-primary-action-button>
                  <app-primary-action-button buttonText="Print Pdf" buttonType="submit" buttonUI='primary'></app-primary-action-button>
                </div>
              </form>
            </div>
          </div>
        </div>
        <!-- Requested Clinic Order Details -->

      </div>
      <div class="col-12 py-5" *ngIf="!supplierOrderHeader && !supplierOrderDetailsList">
        <h5 style="font-weight: 500;">Order Unavailable</h5>
        <p class="text-black-50" style="font-size: 12px;">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Labore mollitia ipsum quae.
          Inventore vitae, obcaecati esse voluptatem tenetur unde aperiam.
        </p>
      </div>
    </div>
  </div>
</div>
