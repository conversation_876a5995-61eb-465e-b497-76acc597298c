<div class="container-fluid">
    <div class="row">
        <div class="sidebar-container">
        </div>
        <div class="main-content">
            <div class="send-orders-container">
                <div class="header-row">
                    <div class="header-row-h1">
                        Purchasing
                    </div>
                    <!-- <button class="btn add-doctor-btn" (click)="openAddDoctorModal()">Add Doctor</button> -->
                    <div class="search-bar">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" placeholder="Search" [(ngModel)]="searchTerm" id="search-input" />
                    </div>
                </div>
                <div class="header-bottom-line"></div>
                <br>
                <div>
                    <div class="send-orders-list-row-new">
                        Send Orders
                    </div>
                    <div>
                        <!-- <button class="see-all-button"><a>See All></a></button> -->
                        <button (click)="showAll = !showAll" class="see-all-button">See All></button>
                    </div>
                </div>
                <table class="send-order-table">
                    <thead>
                        <tr>
                            <th> OrderID</th>
                            <th> Date</th>
                            <th> Time</th>
                            <th> Supplier</th>
                            <th> Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let purchasingDashboard of filteredPurchasingDashboard()">
                            <td>
                                <input type="checkbox"> {{ purchasingDashboard.orderID }}
                            </td>
                            <td>{{ purchasingDashboard.date}}</td>
                            <td>{{ purchasingDashboard.time }}</td>
                            <td>{{ purchasingDashboard.supplier}}</td>
                            <td class="status-cell">
                                <button [ngClass]="{'status pending': purchasingDashboard.status === 'Pending', 'status completed': purchasingDashboard.status === 'Completed'}">{{ purchasingDashboard.status }}</button>
                            </td>
                            <!-- <td class="action-cell">
                    <button class="btn action-btn edit-btn">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn action-btn delete-btn" (click)="deleteDoctor(doctor)">
                        <i class="bi bi-trash"></i>
                    </button>
                </td> -->
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
