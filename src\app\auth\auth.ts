export class UserTemp {
  userTempId: number =0;
  mainName: string = '';
  additionalName?: string;
  userEmail: string = '';
  userPassword: string = '';
  userTempType?: UserTempType
  userTempStatus?: UserTempStatus = UserTempStatus.CREATED;
  userTitle?: string;
  contactNumber?: string;
  contactPerson?: string;
  contactPersonDesignation?: string;
  registrationNumber?: string;
  address?: string;
  district?: string;
  city?: string;
  state?: string;
  createDateTime?: Date = new Date();
  verificationToken?:string = "0"
}

export enum UserTempType {
  DOCTOR,
  CLINIC,
  LABORATORY,
  SUPPLIER,
  FUTURE_DENTIST,
  ADMIN,
  CUSTOMER
}

export enum UserTempStatus {
  CREATED,
  SENT_USER_VERIFICATION,
  USER_VERIFIED,
  ADMIN_APPROVED,
  ADMIN_REJECTED
}
