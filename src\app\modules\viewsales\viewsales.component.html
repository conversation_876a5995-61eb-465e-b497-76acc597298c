
  
<div class="headSection">
  <h3 class="headtitle">Quotation</h3>
  <hr style="margin-top: 15px;">
</div>
<div class="container">

 <div style="text-align: center; padding: 5px;margin-left: -2%; width: 50rem; margin-top: -16px; border-top-left-radius:15px; border-top-right-radius: 15px;  background-image: linear-gradient(to right, rgb(255, 174, 0) , rgb(27, 35, 89)); color: azure;">
  <div>
    <h1 style="font-size: 30px; margin-top: 15px;">ABC Medi Supply Pvt.Ltd</h1>
    <p>N0.23/1, Galle Road, Colombo.</p>
    <p>0777 700 700 | 0117 700 700</p>
    <p><EMAIL></p>
  </div>
</div>
  <div class="details">
 
    <div>
         <br>
    <p style="font-weight:bold;">Order Date :<span style="font-weight: lighter; color: #959595;"> Date</span></p>
    <br>
    <p style="font-weight:bold;">Reference Details :<span style="font-weight: lighter; color: #959595;"> Ref details</span></p>
    </div>
  </div>
  <br> <br> <br>
  <div style="background-color: rgb(255, 255, 255); padding: 3%; box-shadow: rgb(177, 177, 177) 0px 0px 10px; border-radius: 10px;">
  <table>
    <thead style="background-color: rgb(207, 207, 207);">
      <tr>
        <th>Item</th>
        <th>Discription</th>
        <th>Qty</th>
        <th>Unit Price</th>
        <th>Disc.Amount</th>
        <th>Tax Rate</th>
        <th>Amount</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Clinic Item</td>
        <td>Clinic Item</td>
       <td>Clinic Item</td>
        <td>Clinic Item</td>
        <td>Clinic Item</td>
       <td>Clinic Item</td>
        <td>Clinic Item</td>
      </tr>
      <tr>
         <td>Clinic Item</td>
        <td>Clinic Item</td>
       <td>Clinic Item</td>
        <td>Clinic Item</td>
        <td>Clinic Item</td>
       <td>Clinic Item</td>
        <td>Clinic Item</td>
      </tr>
    </tbody>
  </table>
  </div>
  <br>
  <div style="display: flex;">
  <div class="remarks">
    <label for="remarks">Notes</label>
    <br>
    <textarea id="remarks"></textarea>
  </div>
  <div style="margin-left: 20%;">
  <div class="totals">
    <br>
    <div>
      <label for="itemDiscount" style="font-size: 15px; margin-bottom: 3px;">Item Discount</label><br>
      <input class="inputs" type="number" id="itemDiscount" value="220" />
    </div>
     <br>
    <div>
      <label for="grossTotal " style="font-size: 15px; margin-bottom: 3px;">Gross Total</label><br>
      <input class="inputs" style="background-color: rgb(215, 208, 208);" type="number" id="grossTotal" value="220" />
    </div>
     <br>
    <div>
      <label for="discountOnTotal" style="font-size: 15px; margin-bottom: 3px;">Discount on Total</label><br>
      <input class="inputs" type="number" id="discountOnTotal" value="220" />
    </div>
     <br>
    <div>
      <label for="netTotal" style="font-size: 15px; margin-bottom: 3px;">Net Total</label>
      <br>
      <input class="inputs" style="background-color: rgb(215, 208, 208);" type="number" id="netTotal" value="220" />
    </div>
    </div>
  </div>
  </div>
  <button class="send-button" >Check Out</button>
</div>
