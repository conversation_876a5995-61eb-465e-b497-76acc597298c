<div class=" mt-3 container" style="width: 100%;" >
    <!-- 1st Row -->
    <div class="mb-3  first-row" >

        <div class="d-flex flex-column  first-row-right">
            <div>
                <p class="set-date-time" id="text"><b>Set Date and Time</b></p>
            </div>
            
            <div id="date-time-container" class="d-flex ">
                <div class="d-flex">
                    <p>Days</p>
                </div>

                <div class="day-col  d-flex flex-row flex-wrap overflow-auto" style="gap: 15px; justify-content: center; align-items: center;">
                    <button *ngFor="let date of dates" 
                        [class.active]="selectedDay === date" 
                        (click)="selectDay(date)" 
                        id="day-btn">
                        {{ date }}
                    </button>
                </div>
                    
              
                <div class="d-flex mb-2 mark-holidays" style="margin-top: 50px; justify-content: space-between; ">
                    <label class="" style="gap: 4px; display: flex; cursor: pointer;" >
                        <input type="checkbox" id="holidayDate" style="color: #FB751E; background-color: #FB751E; cursor: pointer;" [(ngModel)]="holidayDate">
                        <a href="#" id="markAsHolidayLink" class="disabled-link">Mark Holidays <i class="bi bi-calendar-event"></i></a>
                    </label>
                    <div class=" checkbox" style="display: flex;">
                        <label><input type="checkbox" name="closedStatus" value="closed" [(ngModel)]="isClosed"> Mark as Closed</label>
                    </div>
                </div>
            </div>
        </div>


        <div class=" d-flex flex-column first-row-left">
            <p class="open-hours" id="text"><b>Open Hours</b></p>
            <div id="open-hours" class="flex-grow-1 d-flex flex-column">
                <!-- From Time -->
                <div class=" mb-5">
                    <p>From</p>
                    <div class=" d-flex flex-row align-items-center">
                        <input class="time" type="time" [(ngModel)]="fromTime" />
                    </div>
                </div>
                <!-- To Time -->
                <div class="mb-5">
                    <p>To</p>
                    <div class="col-12 d-flex flex-row align-items-center">
                        <input class="time" type="time" [(ngModel)]="toTime" />
                    </div>
                </div>
            </div>
            <div class="col-12">
                <button class="btn btn-primary" id="save-btn" (click)="saveSchedule()">Save</button>
            </div>
        </div>
    </div>
    
    
    <div class="row mb-10"   >
        <div class="col-md-12 ">
            <h4>Schedule List</h4>
            <div class="table-responsive">
                <table class="table table-striped" >
                    <thead>
                        <tr>
                            <!-- <th>Clinic Id</th> -->
                            <th>Day</th>
                            <th>From</th>
                            <th>To</th>
                            <th>Status</th>
                            <!-- <th>Holiday Date</th> -->
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let schedule of schedules">
                            <td hidden data-label="Clinic Id">{{ schedule.clinics.clinicId }}</td>
                            <td data-label="Day">{{ schedule.date }}</td>
                            <td data-label="From"><ng-container *ngIf="!schedule.holidayDate; else holidayPlaceholder">{{ schedule.fromTime }}</ng-container>
                                <ng-template #holidayPlaceholder></ng-template>
                            </td>
                              
                            <td data-label="From"><ng-container *ngIf="!schedule.holidayDate; else holidayPlaceholder">{{ schedule.toTime }}</ng-container>
                                <ng-template #holidayPlaceholder></ng-template>
                            </td>
                            
                            <td data-label="Holiday Date">{{ schedule.holidayDate || schedule.isClosed ? 'Closed' : 'Open' }}</td>
                           
                            <td data-label="Actions">
                                <button class="btn btn-warning btn-sm" (click)="updateSchedule(schedule)" style="margin-right: 10px;">Edit</button>
                                <button class="btn btn-danger btn-sm"
                                    (click)="schedule.scheduleId ? deleteSchedule(schedule.scheduleId) : null">
                                    Delete
                                </button>
                            </td>
                            
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    

    <div class="container rounded-container p-4 mt-3 col-12">
        <h3 class="text-dark">Add services</h3>
        <div class="col-12 align-items">
            <div class="form-group row d-flex col-12">
                
                    <div class=" mb-4 col-12" style="width: 600px;">
                        <label for="serviceIds" class="form-label">Select Services:</label>
                        <select id="serviceIds" name="serviceIds" class="form-select " [(ngModel)]="selectedServiceId" >
                            <option value="1">Dental Bonding</option>
                            <option value="2">Cosmetic Fillings</option>
                            <option value="3">Invisalign</option>
                            <option value="4">Teeth Cleanings</option>
                            <option value="5">Root Canal Therapy</option>
                            <option value="6">Dental Sealants</option>
                        </select>
                    </div>
             
               
                    <div class="form-group mb-4 col-12">
                        <button class="btn btn-custom" id="save-btn-service" (click)="saveService()">Save</button>
                    </div>
               
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>Added Services</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let service of existingServices">
                        <td data-label="Added Services">{{ service.services.clinicServiceCategoryName }}</td>
                        <td data-label="Action">
                            <button class="btn btn-danger btn-rounded" (click)="deleteService(service.clinicServiceId)">Delete</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    

    <div>
        <br>
        <br>
    </div>





    <!-- 3rd Row -->
    <!-- 3rd Row -->
    <div class="mb-3" style="width: 100%;">
        <div class=" d-flex flex-column">
            <div id="set-date-time" class="flex-grow-1 d-flex flex-column">
                <div class="mb-2">
                    <div class=" mb-11 d-flex" style="flex-wrap: wrap; align-items: center; justify-content: space-between; ">
                        <div class="" style="padding-right: 40px;">
                            <p class="set-date-time" id="text">Add Doctors</p>
                        </div>
                        <div class="" style="margin-top: -10px;">
                            <a href="#" id="markAsHolidayLink" >View All Doctors</a>
                        </div>
                    </div>
                    <div class="" style="margin-top: 10px; margin-bottom: 10px; width: 100%;">
                        <a class="btn btn-primary" id="add-Doctors-btn" routerLink="/clinic/list-doctor">
                            <i class="fa fa-arrow-circle-right icon"></i> Add Doctors
                        </a>
                    </div>
                    <!-- Doctor List Section -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Doctor List</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Doctor ID</th>
                                            <th>Name</th>
                                            <th>Reg No</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Loop through doctors and display them in the table -->
                                        <tr *ngFor="let doctor of doctors">
                                            <td data-label="Doctor ID">{{ doctor.doctorId }}</td>
                                            <td data-label="Name">{{ doctor.firstName }}</td>
                                            <td data-label="Reg No">{{ doctor.regNo }}</td>
                                            <td data-label="Actions">
                                                <button class="btn btn-danger btn-sm" (click)="deleteClinicDoctor(doctor.doctorId)">
                                                    <i class="fa fa-trash"></i> Delete
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- End Doctor List Section -->
                </div>
            </div>
        </div>
    </div>
    

</div>