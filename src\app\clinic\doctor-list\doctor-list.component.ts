import { Component, OnInit } from '@angular/core';
import { DoctorService } from '../../doctor/doctor.service';
import { DoctorList } from './doctorList';
import Swal from 'sweetalert2'; // Import SweetAlert

@Component({
  selector: 'app-doctor-list',
  templateUrl: './doctor-list.component.html',
  styleUrls: ['./doctor-list.component.css'],
})
export class DoctorListComponent implements OnInit {
  doctors: DoctorList[] = []; // Initialize as an empty array
  filteredDoctors: DoctorList[] = []; // Filtered list for display
  searchTerm: string = '';
  showModal: boolean = false;
  newDoctor: DoctorList = {
    doctorId: 0,
    regNo: '',
    firstName: '',
    lastName: '',
    specialty: '',
    telephone: '',
    email: '',
    availability: 'Available',
  };

  constructor(private doctorService: DoctorService) {}

  ngOnInit(): void {
    this.fetchDoctors();
  }

  // Fetch doctors from API
  fetchDoctors() {
    this.doctorService.getDoctorList().subscribe(
      (data: DoctorList[]) => {
        console.log('Doctors:', data);
        this.doctors = data;
        this.filteredDoctors = data; // Initialize filteredDoctors with all doctors
      },
      (error) => {
        console.error('Error fetching doctors:', error);
      }
    );
  }

  // Filter doctors based on the search term
  searchDoctors(): void {
    const searchTermLower = this.searchTerm.toLowerCase(); // Convert search term to lowercase
    if (searchTermLower === '') {
      this.filteredDoctors = [...this.doctors]; // Show all doctors when search term is empty
    } else {
      this.filteredDoctors = this.doctors.filter(doctor =>
        doctor.firstName.toLowerCase().includes(searchTermLower) // Case-insensitive search
      );
    }
  }

  // Show Add Doctor Modal
  openAddDoctorModal() {
    this.showModal = true;
  }

  // Close Add Doctor Modal
  closeModal() {
    this.showModal = false;
  }

  // Add a new doctor (locally)
  addDoctor() {
    this.doctors.push({ ...this.newDoctor });
    this.filteredDoctors.push({ ...this.newDoctor });
    this.closeModal();
  }

  // Assign doctor to clinic
  assignDoctor(doctor: DoctorList) {
    const userId = window.localStorage.getItem('userid');
    console.log('User ID:', userId);
    if (!userId) {
      console.error('User ID not found in local storage');
      return;
    }

    this.doctorService.assignDoctorToClinic(userId, doctor.doctorId).subscribe(
      (response) => {
        console.log('Doctor assigned to clinic successfully:', response);
        // Show success alert with SweetAlert
        Swal.fire({
          title: 'Success!',
          text: 'Doctor assigned to clinic successfully',
          icon: 'success',
          confirmButtonText: 'OK',
        });
      },
      (error) => {
        console.error('Error assigning doctor to clinic:', error);
        // Show error alert with SweetAlert
        Swal.fire({
          title: 'Error!',
          text: 'There was an error assigning the doctor. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
        });
      }
    );
  }

  // Delete doctor locally
  deleteDoctor(doctor: DoctorList) {
    const index = this.doctors.indexOf(doctor);
    if (index > -1) {
      this.doctors.splice(index, 1);
      this.filteredDoctors = this.filteredDoctors.filter(d => d.doctorId !== doctor.doctorId); // Remove from filtered list
    }
  }
}
