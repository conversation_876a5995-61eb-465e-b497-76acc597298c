        .sidebar-container {
            height: calc(100vh - 80px);
            width: 20%;
            overflow-y: auto;
            display: inline-block;
        }

        .main-content {
            height: calc(100vh - 80px);
            width: 80%;
            overflow-y: auto;
            padding: 16px;
            display: inline-block;
        }

        .container {
            padding: 20px;
        }

        .header-row {
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-row-h1 {
            color: black;
            width: 390px;
            top: 150px;
            font-size: 24px;
            left: 386px;
            font-family: Inter;
            font-size: 32px;
            font-weight: 600;
            line-height: 38.73px;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .header-bottom-line {
            border-bottom: 2px solid #ccc;
            margin-top: 15px;
        }

        .rectangle {
            width: 100%;
            max-width: 1292px;
            min-height: auto;
            border-radius: 15px;
            background: #FFFFFF;
            box-shadow: 0px 3px 11.9px -1px #00000040;
            margin-top: 20px;
            padding-bottom: 20px;
            
        }

        #header1 {
            height: 64px;
            border-radius: 15px 15px 0 0;
            background: linear-gradient(90deg, #FB751E 49.4%, #DBB800 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: Inter, sans-serif;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 0;
        }

        h2 {
            margin: 0;
        }

        .bill-p {
            font-family: Inter;
            font-size: 20px;
            font-weight: 600;
            line-height: 24.2px;
            text-align: left;
            color: #333333;
            padding-left: 40px;
            padding-top: 40px;
            margin: 0;
        }

        .lab-p {
            font-family: Inter;
            font-size: 20px;
            font-weight: 600;
            line-height: 24.2px;
            text-align: right;
            color: #333333;
            padding-right: 40px;
            padding-top: 40px;
            margin: 0;
        }

        #name,
        #address,
        #email-l,
        #email-r,
        #tel-l,
        #tel-r {
            font-family: Inter;
            font-size: 16px;
            font-weight: 500;
            line-height: 19.36px;
            color: #666666;
        }

        #name,
        #email-l,
        #tel-l {
            text-align: left;
            padding-left: 40px;
        }

        #address,
        #email-r,
        #tel-r {
            text-align: right;
            padding-right: 40px;
        }

        #name,
        #address {
            padding-top: 20px;
        }

        #email-l,
        #email-r,
        #tel-l,
        #tel-r {
            padding-top: 5px;
        }

        .align-items-end {
            align-items: flex-end !important;
        }

        #note-i {
            font-family: Inter;
            font-size: 20px;
            font-weight: 600;
            line-height: 24.2px;
            color: #333333;
            padding-left: 40px;
            padding-top: 20px;
        }

        #number-i,
        #date-i,
        #text-i {
            font-family: Inter;
            font-size: 16px;
            color: #333333;
            width: 80%;
            padding: 8px;
            margin-top: 2px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-left: 40px;
        }

        /* Base styling for the table */
.my-appointments-table {
    width: 94%;
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
  }
  
  .my-appointments-table th,
  .my-appointments-table td {
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
  }
  
  .my-appointments-table .section-header {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
  }
  
  .service-input {
    width: 100%;
    box-sizing: border-box;
    padding: 5px;
    border: none;
    border-bottom: 2px solid #ccc;
    outline: none;
    font-size: 14px;
  }
  
  textarea.service-input {
    resize: none;
    height: auto;
  }
  
  /* Responsive styles */
  @media (max-width: 1000px) {
    .my-appointments-table {
      font-size: 12px;
    }
  
    .my-appointments-table thead {
      display: none;
    }
  
    .my-appointments-table tbody,
    .my-appointments-table tr,
    .my-appointments-table td {
      display: block;
      width: 100%;
    }
  
    .my-appointments-table td {
      position: relative;
      
      text-align: left;
      border: none;
      border-bottom: 1px solid #ddd;
    }
  
    .my-appointments-table td:before {
      content: attr(data-label);
      position: absolute;
      left: 10px;
      top: 10px;
      font-weight: bold;
      white-space: nowrap;
    }
  
    .service-input {
      font-size: 12px;
    }
  }
  
  /* Small screen (extra narrow devices) */
  @media (max-width: 480px) {
    .my-appointments-table {
      font-size: 10px;
    }
  
    .service-input {
      font-size: 10px;
    }
  }
  

        .totals-row {
            margin-top: 20px;
        }

        .totals-container {
            width: 100%;
            max-width: 550px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            padding-right: 40px;
        }

        .totals-container p {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 320px;
            margin: 5px 0;
            font-family: Inter, sans-serif;
            font-size: 16px;
            color: #333333;
        }

        .totals-container p button {
            width: 158px;
            height: 44px;
            border-radius: 10px;
            border: 1px solid #CEC9C980;
            background: #D9D9D94D;
            opacity: 1;
        }

        #textarea {
            
            height: 100px;
            top: 1331px;
           width: 94%;
            justify-content: center;
            border-radius: 12px;
            border: 1px;
          
          
            
            border: 1px solid #B3B3B3;
        }

        .service-input:focus{
            border: none;
            outline: none;
        }

        

        #note {
            padding-left: 3%;
          
            
            font-family: Inter;
            font-size: 20px;
            font-weight: 600;
            line-height: 24.2px;
            text-align: left;
            color: #000;
        }
        /* Ensure the container has position relative */

        .container {
            position: relative;
        }

        #addProduct {
            width: 167px;
            height: 35px;
            border-radius: 18px;
            font-family: Inter;
            font-size: 16px;
            font-weight: 600;
            line-height: 19.36px;
            text-align: center;
            margin-top: 60px;
            float: right;
            color: #FFFFFF;
            border: 1px solid #FB751E;
            background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);
            right: 0;
            bottom: 0;
        }
