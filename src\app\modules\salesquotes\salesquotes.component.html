<div class="container-fluid">
    <div class="row">
        <div class="sidebar-container">
        </div>

    </div>
</div>


<div class="container">
    <header>
        <div class="send-orders-container">
            <div class="header-row">
                <div class="header-row-h1">
                    Sales Quotes
                </div>

                <div class="search-bar">
                    <i class="mx-auto bi bi-search search-icon"></i>
                    <input type="text" placeholder="  Search"[(ngModel)]="searchTerm" id="search-input" />
                </div>
                <button class="btn btn-outline-warning" id="printButton">Create Sales Quote</button>
            </div>
            <div class="header-bottom-line"></div>
            <br>
            <div>

                <div>

                    <button class="see-all-button">See All></button>
                </div>
            </div>

        </div>
    </header>

    <main>
        <section class="order-requests">

            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th><input type="checkbox"></th>
                        <th>Clinic Name</th>
                        <th>Address</th>
                        <th>Status</th>

                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let salesquotes of filteredItems">
                        <td><input type="checkbox"></td>
                        <td>{{ salesquotes.supplierName}}</td>
                        <td>{{ salesquotes.address}}</td>
                        <td>
                            <button class="view-request-button">View Quote</button>
                            <img src="assets/images/3dot.png" class="dot">
                        </td>
                    </tr>
                </tbody>

            </table>

        </section>
    </main>
</div>
