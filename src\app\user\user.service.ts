import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { User, UserCategory } from './user';

@Injectable({
  providedIn: 'root',
})
export class UserService {

  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.localStorage.getItem('auth_token');
  }

  setAuthToken(token: string | null): void {
    if (token !== null) {
      window.localStorage.setItem('auth_token', token);
    } else {
      window.localStorage.removeItem('auth_token');
    }
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any,
    responseType: 'json' | 'text' = 'json'
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options: any = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
      responseType: responseType as 'json' | 'text',
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  register(user: User): Observable<any> {
    return this.request('POST', `/register`, user);
  }

  login(user: User): Observable<any> {
    return this.request('POST', `/login`, user);
  }

  checkUser(username: string): Observable<any> {
    const params = { username };
    return this.request('GET', `/check-username`, null, params);
  }

  //User Categories
  getUserCategoryList(): Observable<UserCategory[]> {
    return this.request('GET', '/userCategoryList', {});
  }

  getUserCategoryById(id: number): Observable<UserCategory> {
    return this.request('GET', `/getUserCategoryById/${id}`, {});
  }

  verifyEmail(token: string): Observable<string> {
    return this.request('GET', `/verify`, null, { token });
  }
}
