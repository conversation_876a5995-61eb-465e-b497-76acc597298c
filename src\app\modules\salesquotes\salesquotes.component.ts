import { Component } from '@angular/core';
interface Salesquotes {
  id: number;
  supplierName: string;
  address: string;
 
}

@Component({
  selector: 'app-salesquotes',
  templateUrl: './salesquotes.component.html',
  styleUrls: ['./salesquotes.component.css']
})
export class SalesquotesComponent {
  salesquotes: Salesquotes[] = [
    { id: 1,supplierName: 'Medi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' },
    { id: 2,supplierName: 'sedi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' },
    { id: 3,supplierName: 'vedi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' },
    { id: 4,supplierName: 'Medi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' },
    { id: 1,supplierName: 'Medi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' },
    { id: 2,supplierName: 'sedi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' },
    { id: 3,supplierName: 'vedi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' },
    { id: 4,supplierName: 'Medi Lab Pvt.Ltd', address: 'Medi lab pvt. ltd. No.12, abc road, colombo' }
  ];

  searchTerm: string = '';
  get filteredItems() {
    return this.salesquotes.filter(salesquotes =>
      salesquotes.supplierName.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }
  toggleSidebar() {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
    }
  }
}
