import { Injectable } from '@angular/core';
import { Appointment } from './appointment';

@Injectable({
  providedIn: 'root'
})

export class UserAppointmentService {
  getCurrentAppointments(): Appointment[] {
    return [
      { id: 1, date: '2024-06-15', time: '10:30 - 11:00 a.m', doctor<PERSON><PERSON>: 'Dr. <PERSON>', specialty: 'Dentist', clinic: 'Dental Clinic', status: 'Pending Acceptance' }
    ];
  }

  getCompletedAppointments(): Appointment[] {
    return [
      { id: 2, date: '2024-05-10', time: '11:00 - 11:30 a.m', doctor<PERSON>ame: 'Dr. <PERSON>', specialty: 'Cardiologist', clinic: 'Health Clinic', status: 'Completed' },
      { id: 3, date: '2024-04-20', time: '09:00 - 09:30 a.m', doctorName: 'Dr. <PERSON>', specialty: 'Dermatologist', clinic: 'Skin Clinic', status: 'Completed' }
    ];
  }

  getReminder(): Appointment {
    return { id: 4, date: '2024-06-20', time: '3:30 p.m', doctor<PERSON>ame: 'Dr. <PERSON>', specialty: 'Dentist', clinic: 'Dental Clinic', status: 'Reminder' };
  }
}
