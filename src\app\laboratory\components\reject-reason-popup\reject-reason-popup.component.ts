import { Component, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-reject-reason-popup',
  templateUrl: './reject-reason-popup.component.html',
  styleUrls: ['./reject-reason-popup.component.css']
})
export class RejectReasonPopupComponent {
  rejectionReason: string = ''; // Variable to store the entered reason

  @Output() closePopup = new EventEmitter<void>(); // Emit event to close the popup
  @Output() reasonSubmitted = new EventEmitter<string>(); // Emit the rejection reason when submitted

  // Function to close the popup
  onClose() {
    this.closePopup.emit();
  }

  // Function to submit the rejection reason
  submitReason() {
    this.reasonSubmitted.emit(this.rejectionReason);
    this.onClose(); // Close the popup after submission
  }
}
