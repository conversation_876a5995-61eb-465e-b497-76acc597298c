import { Component, OnInit, ViewChild } from '@angular/core';
import { LaboratoryService } from '../laboratory.service';


@Component({
  selector: 'app-laboratory-invoice',
  templateUrl: './laboratory-invoice.component.html',
  styleUrls: ['./laboratory-invoice.component.css']
})
export class LaboratoryInvoiceComponent {

  
    name :string = '';
    invoices: any[] = []; // Array to hold fetched invoices
  

  constructor(private laboratoryService: LaboratoryService){}
submitInvoice() {

  // const invoiceData ={
  //   name : this.name,

  // }
  // this.laboratoryService.saveLaboratory(invoiceData).subscribe(
  //   (response) => {
  //     console.log('Invoice saved successfully', response);
  //     alert('Invoice saved successfully!');
  //   },
  //   (error) => {
  //     console.error('Error saving invoice', error);
  //     alert('Error saving invoice!');
  //   }
  // );
}

// ngOnInit() {
//   this.getLabData(); // Fetch invoices when the component initializes
// }


// getLabData(id: number) : void{
//   const LabIdString = localStorage.getItem('LabId');
  
//   if(LabId)// Retrieve the clinicId from localStorage
//   this.laboratoryService.getLaboratoryById(id).subscribe(
//     (data) => {
//       this.invoices = data; // Set the fetched data to invoices array
//       console.log('Invoices loaded:', this.invoices); // You can remove this line later
//     },
//     (error) => {
//       console.error('Error fetching invoices:', error);
//     }
//   );
// }
  
 
}
