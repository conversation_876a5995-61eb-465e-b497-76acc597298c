<app-default-navbar loggedUser="Hello Future Dentist" />
<div class="registration-page">
  <div class="registration-card">
    <h3>Future Dentist Registration</h3>
    <button class="backtoselection-button" (click)="navigateUserSelection()">Selection Menu</button>
    <form (ngSubmit)="registrationDentist()" #registrationForm="ngForm">
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input type="text" id="firstName" name="firstName" [(ngModel)]="newDentist.firstName" #firstName="ngModel" required />
          <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="text-danger">
            <div *ngIf="firstName.errors?.['required']">First Name is required.</div>
          </div>
        </div>
        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input type="text" id="lastName" name="lastName" [(ngModel)]="newDentist.lastName" #lastName="ngModel" required />
          <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="text-danger">
            <div *ngIf="lastName.errors?.['required']">Last Name is required.</div>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="contactNumber">Contact Number</label>
          <input type="text" id="contactNumber" name="contactNumber" [(ngModel)]="newDentist.contactNumber" #contactNumber="ngModel" required />
          <div *ngIf="contactNumber.invalid && (contactNumber.dirty || contactNumber.touched)" class="text-danger">
            <div *ngIf="contactNumber.errors?.['required']">Contact Number is required.</div>
          </div>
        </div>
        <div class="form-group">
          <label for="contactPerson">Contact Person</label>
          <input type="text" id="contactPerson" name="contactPerson" [(ngModel)]="newDentist.contactPerson" #contactPerson="ngModel" required />
          <div *ngIf="contactPerson.invalid && (contactPerson.dirty || contactPerson.touched)" class="text-danger">
            <div *ngIf="contactPerson.errors?.['required']">Contact Person is required.</div>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" name="email" [(ngModel)]="newDentist.email" #email="ngModel" required />
          <div *ngIf="email.invalid && (email.dirty || email.touched)" class="text-danger">
            <div *ngIf="email.errors?.['required']">Email Address is required.</div>
            <div *ngIf="email.errors?.['email']">Invalid email format.</div>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" [(ngModel)]="newDentist.password" #password="ngModel" required minlength="6" />
          <div *ngIf="password.invalid && (password.dirty || password.touched)" class="text-danger">
            <div *ngIf="password.errors?.['required']">Password is required.</div>
            <div *ngIf="password.errors?.['minlength']">Password must be at least 6 characters long.</div>
          </div>
        </div>
        <div class="form-group">
          <label for="confirmPassword">Re-enter Password</label>
          <input type="password" id="confirmPassword" name="confirmPassword" [(ngModel)]="newDentist.confirmPassword" #confirmPassword="ngModel" required />
          <div *ngIf="confirmPassword.invalid && (confirmPassword.dirty || confirmPassword.touched)" class="text-danger">
            <div *ngIf="confirmPassword.errors?.['required']">Confirm Password is required.</div>
            <div *ngIf="confirmPassword.errors?.['pattern']">Passwords do not match.</div>
          </div>
        </div>
      </div>

      <button type="submit" class="register-button" [disabled]="registrationForm.invalid">Register</button>
    </form>
  </div>
</div>
