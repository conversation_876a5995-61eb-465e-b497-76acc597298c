import { Component } from '@angular/core';
import { SupplierOrderHeader } from 'src/app/supplier/supplier';
import { ClinicService } from '../clinic.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-clinic-supplier-order-list',
  templateUrl: './clinic-supplier-order-list.component.html',
  styleUrls: ['./clinic-supplier-order-list.component.css']
})
export class ClinicSupplierOrderListComponent {
  protected clinicOrderHeaderList: SupplierOrderHeader[] = [];
  protected filteredOrderHeaderList: SupplierOrderHeader[] = [];
  protected filteredPaginatedData: SupplierOrderHeader[] = [];
  protected searchTerm: string = '';
  protected currentPage: number = 1;
  protected itemsPerPage: number = 6;
  protected totalPages: number = 1;
  protected visiblePages: number[] = [];

  constructor(
    private clinicServices: ClinicService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.onloadOrderList();
  }

  onloadOrderList() {
    const clinicUserId: number = Number(localStorage.getItem('userid'));
    if (clinicUserId) {
      this.clinicServices.getOrderRequestByClinicUserId(clinicUserId).subscribe((response) => {
        if (response) {
          this.clinicOrderHeaderList = response.map((order: SupplierOrderHeader) => ({
            ...order,
            createdDateTime: this.formatDateTime(order.createdDateTime.toString())
          }));
          this.filteredOrderHeaderList = this.clinicOrderHeaderList;
          this.updatePagination();
        }
      });
    }
  }

  private formatDateTime(dateTime: string): string {
    const [year, month, day, hour, minute, second] = dateTime
      .split(',')
      .map(Number);
    const formattedDate = `${String(day).padStart(2, '0')}-${String(month).padStart(2, '0')}-${year}`;
    const formattedTime = `${String(hour).padStart(2, '0')} : ${String(
      minute
    ).padStart(2, '0')} : ${String(second).padStart(2, '0')}`;
    return `${formattedDate}&nbsp;&nbsp;&nbsp; | &nbsp;&nbsp;&nbsp;${formattedTime}`;
  }

  filterOrders() {
    const searchTermLower = this.searchTerm.toLowerCase();

    this.filteredOrderHeaderList = this.clinicOrderHeaderList.filter(order =>
      order.clinic.name.toLowerCase().includes(searchTermLower) ||
      order.clinic.city.toLowerCase().includes(searchTermLower)
    );
    this.currentPage = 1;
    this.updatePagination();
  }


  // public viewOrderDetails(orderrderHeaderId: number) {
  //   this.router.navigate(['supplier/clinic-orders/single-clinic-order'], {
  //     queryParams: { id: supplierOrderHeaderId },
  //   });
  // }

  goToCreateSupplierOrderView(){
    this.router.navigate(['clinic/supplier-orders/new-order']);
  }


  // Pagination methods
  updatePagination() {
    this.totalPages = Math.ceil(this.filteredOrderHeaderList.length / this.itemsPerPage);
    this.filteredPaginatedData = this.paginatedData();
    this.visiblePages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  paginatedData() {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredOrderHeaderList.slice(start, end);
  }

  goToPage(page: number) {
    this.currentPage = page;
    this.filteredPaginatedData = this.paginatedData();
  }
}
