import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CustomerRoutingModule } from './customer-routing.module';
import { CustomerDashboardComponent } from './customer-dashboard/customer-dashboard.component';
import { CustomerSideBarComponent } from './components/customer-side-bar/customer-side-bar.component';
import { CustomerNavBarComponent } from './components/customer-nav-bar/customer-nav-bar.component';
import { CustomerLayoutComponent } from './customer-layout/customer-layout.component';
import { CustomerNewAppoinmentComponent } from './customer-new-appointment/customer-new-appointment.component';


@NgModule({
  declarations: [
    CustomerDashboardComponent,
    CustomerSideBarComponent,
    CustomerNavBarComponent,
    CustomerLayoutComponent,
    CustomerNewAppoinmentComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CustomerRoutingModule
  ]
})
export class CustomerModule { }
