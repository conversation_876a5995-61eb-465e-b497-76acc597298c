import { Component, ViewChild } from '@angular/core';



interface  MyAppointments{
    patientName: string;
    clinicAddress:string;
    date:string;
    time: string;
   appNo: string;

}
@Component({
  selector: 'app-my-appointments',
  templateUrl: './my-appointments.component.html',
  styleUrls: ['./my-appointments.component.css']
})
export class MyAppointmentsComponent {

  searchTerm: string = '';
  MyAppointments: MyAppointments[] = [
        { patientName: '<PERSON>',clinicAddress: 'MyDent Clinic, Colombo 12',date:'24/07/2024', time: '12.00 pm',   appNo: '01' },
        { patientName: '<PERSON>', clinicAddress: 'MyDent Clinic, Colombo 12',date:'24/07/2024',time: '12.00 pm',   appNo: '01' },
        // Add more dummy data as needed
    ];


    // showModal: boolean = false;
    newMyAppointments: MyAppointments = { patientName: '',clinicAddress:'',date:'', time: '', appNo:''};


    filteredMyAppointments() {
         return this.MyAppointments.filter(MyAppointments =>
             MyAppointments.appNo.toLowerCase().includes(this.searchTerm.toLowerCase())
         );
    }
}
