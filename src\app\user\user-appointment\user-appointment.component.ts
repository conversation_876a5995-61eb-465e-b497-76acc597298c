import { Component, ElementRef, ViewChild } from '@angular/core';
// import * as bootstrap from 'bootstrap'; // Import the 'bootstrap' module
import * as bootstrap from 'bootstrap';
@Component({
  selector: 'app-user-appointment',
  templateUrl: './user-appointment.component.html',
  styleUrls: ['./user-appointment.component.css']
})
export class UserAppointmentComponent {
  acceptanceButtonsVisible = false;
  paymentConfirmed = false;
  acceptanceButtonsDisabled = false;

  @ViewChild('paymentModal') paymentModalElement!: ElementRef;

  showAcceptanceButtons() {
    this.acceptanceButtonsVisible = true;
    this.acceptanceButtonsDisabled = true;
  }

  showPaymentModal() {
    const modalElement = this.paymentModalElement.nativeElement;
    const paymentModal = new bootstrap.Modal(modalElement);
    paymentModal.show();
  }

  confirmPayment() {
    const modalElement = this.paymentModalElement.nativeElement;
    const paymentModal = bootstrap.Modal.getInstance(modalElement);
    if (paymentModal) {
      paymentModal.hide();
    }
    this.paymentConfirmed = true;
    this.acceptanceButtonsVisible = false;
  }

  closeModal() {
    const modalElement = this.paymentModalElement.nativeElement;
    const paymentModal = bootstrap.Modal.getInstance(modalElement);
    if (paymentModal) {
      paymentModal.hide();
    }
  }
}
