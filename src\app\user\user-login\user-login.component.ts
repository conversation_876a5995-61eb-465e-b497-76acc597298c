import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../user.service';
import { User } from '../user';
import Swal from 'sweetalert2'; // Import SweetAlert

@Component({
  selector: 'app-user-login',
  templateUrl: './user-login.component.html',
  styleUrls: ['./user-login.component.css'],
})
export class UserLoginComponent {
  username: string = '';
  password: string = '';
  user: User = new User();
  remember: boolean = false;
  passwordVisible: boolean = false;

  constructor(private router: Router, private userService: UserService) {}

  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
    const passwordField = document.getElementById(
      'password'
    ) as HTMLInputElement;
    passwordField.type = this.passwordVisible ? 'text' : 'password';
  }

  onSubmitLogin() {
    localStorage.clear();
    this.onLogin();
  }

  onLogin() {
    this.userService.login(this.user).subscribe(
      (response) => {
        // Set the auth token after successful login
        this.userService.setAuthToken(response.token);

        // Log the complete response to check for clinicId
        console.log('API Response:', response);

        // Store the full user object and other relevant data
        localStorage.setItem('userid', response.id.toString());
        localStorage.setItem('firstName', response.firstName || '');
        localStorage.setItem('lastName', response.lastName || '');
        localStorage.setItem('companyId', response.companyId || '');

        console.table(response);

        if (
          response.userCategoryId != null &&
          response.userCategoryId.routerPath != null
        ) {
          this.router.navigate(['/' + response.userCategoryId.routerPath]);
        }
      },
      (error) => {
        // Handle login failure
        this.userService.setAuthToken(null);

        // Display a user-friendly error message based on the error response
        const errorMessage =
          error.error?.message || 'Login Failed: Invalid username or password.';

        // Use SweetAlert instead of alert
        Swal.fire({
          icon: 'error',
          title: 'Login Failed',
          text: errorMessage,
          confirmButtonText: 'OK',
        });

        console.log('Login Failed', error); // Log the full error details for further debugging
      }
    );
  }

  public logout(): void {
    // Remove user authentication token from storage (localStorage/sessionStorage)
    localStorage.removeItem('authToken');

    localStorage.clear();
    this.router.navigate(['/home-page']);
  }

  navigateToUserSelection() {
    this.router.navigate(['/user-selection']);
  }
}
