export interface Schedule {
  scheduleId?: number | null; // Add scheduleId property
  clinics: { clinicId: number }, // Wrap the clinic ID in an object  
  date: string;      // Full name of the day (e.g., 'Monday')
  fromTime: string; // Time in 'hh:mm AM/PM' format
  toTime: string;   // Time in 'hh:mm AM/PM' format
  isClosed?: boolean; // Optional flag to indicate if the clinic is closed on that day
  holidayDate: string | boolean; // Optional holiday date if marked as a holiday
}
export interface ClinicServices {
    clinicServiceId?: number | null; // Assuming this is the primary key
    clinics: {
        clinicId: number; // Reference to the Clinic
    };
    services: {
      clinicServiceCategoryId: number; // Reference to the Services
    };

}