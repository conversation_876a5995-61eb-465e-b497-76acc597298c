<div class="row bg-white">
  <div class="col-12">
    <!-- Header -->
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div class="col-6">
            <h3 class="fs-5" style="font-weight: 600">Clinic Odrers</h3>
            <p class="text-black-50" style="font-size: 12px;">Load All Item Requests By Clinics</p>
          </div>
          <div class="col-6 position-relative">
            <input type="text" placeholder="Search from Here" [(ngModel)]="searchTerm" (input)="filterOrders()" class="search-input w-50 position-absolute" name="" id="">
          </div>
        </div>
        <div class="row my-4">
          <hr class="border-secondary">
        </div>
      </div>
    </div>
    <!-- Header -->

    <!-- Body -->
     <div class="row">
      <div class="col-12">
        <div class="row">
          <div class="col-12">
            <div class="row gy-3" *ngIf="filteredOrderHeaderList !=null && filteredOrderHeaderList.length >0">
              <!-- table-header -->
              <div class="col-12 p-3 py-2 card-table-header" style="border-radius: 5px;">
                <div class="row card-table-header my-1">
                  <div class="col-7 d-flex my-auto">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Clinic Name & Address - Status</h6>
                  </div>
                  <div class="col-3 my-auto text-center" style="border-inline: 1px solid  white;">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Date & Time</h6>
                  </div>
                  <div class="col-2 text-center my-auto">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Actions</h6>
                  </div>
                </div>
              </div>
              <!-- table-header -->

              <!-- table-content -->
              <div *ngFor="let supplierOrderHeader of filteredPaginatedData" class="col-12 p-3" style="border: 1px solid rgb(230,230,230); background-color: rgb(254, 254, 254); border-radius: 5px;">
                <div class="row">
                  <div class="col-7 d-grid d-xl-flex my-auto">
                    <h6 class="my-auto pe-0 pe-xl-3" style="font-weight: 500; font-size: 14px;">{{supplierOrderHeader.clinic.name +" - "+supplierOrderHeader.clinic.city}}</h6>
                    <div class="mt-2 mt-xl-0" [ngSwitch]="supplierOrderHeader.orderStatus.toString()">
                      <span *ngSwitchCase="'CLINIC_CREATED'" class="alert py-1 my-auto alert-info"
                            style="border-radius: 5px; font-size: 11px; font-weight: 500;">
                        Received
                      </span>

                      <span *ngSwitchCase="'SUPPLIER_APPROVED'" class="alert py-1 my-auto alert-warning"
                            style="border-radius: 5px; font-size: 11px; font-weight: 500;">
                        Approved By You
                      </span>

                      <span *ngSwitchCase="'SUPPLIER_REJECTED'" class="alert py-1 my-auto alert-danger"
                            style="border-radius: 5px; font-size: 11px; font-weight: 500;">
                        Rejected By You
                      </span>

                      <span *ngSwitchCase="'CLINIC_REJECTED'" class="alert py-1 my-auto alert-danger"
                            style="border-radius: 5px; font-size: 11px; font-weight: 500;">
                        Cancelled By Clinic
                      </span>

                      <span *ngSwitchCase="'SUPPLIER_COMPLETED'" class="alert py-1 my-auto alert-success"
                            style="border-radius: 5px; font-size: 11px; font-weight: 500;">
                        Completed
                      </span>

                      <span *ngSwitchCase="'SUPPLIER_PROCESSING'" class="alert py-1 my-auto alert-warning"
                            style="border-radius: 5px; font-size: 11px; font-weight: 500;">
                        Ongoing
                      </span>

                      <span *ngSwitchDefault class="alert py-1 my-auto alert-secondary"
                            style="border-radius: 5px; font-size: 11px; font-weight: 500;">
                        {{supplierOrderHeader.orderStatus.toString()}}
                      </span>
                    </div>
                  </div>
                  <div class="col-3 my-auto text-center" style="border-inline: 1px solid  rgb(230,230,230);">
                    <p class="text-balck-50" style="font-size: 13px;font-weight: 500;" [innerHTML]="supplierOrderHeader.createdDateTime"></p>
                  </div>
                  <div class="col-2 text-center my-auto">
                    <label class="view-odrer-button py-1 my-auto px-3" (click)="viewOrderDetails(supplierOrderHeader.supplierOrderHeaderId)"> View Order</label>
                  </div>
                </div>
              </div>

              <!-- table-content -->

              <!-- Pagination -->
              <div *ngIf="filteredOrderHeaderList.length > itemsPerPage" class="row mt-4 position-relative">
                <div class="col-12 d-flex justify-content-start g-0">
                  <button (click)="previousPage()" [disabled]="currentPage === 1" class="alert bg-light me-2" style="font-size: 13px; padding: 10px 15px;">
                    <i class="bi-chevron-left"></i>
                  </button>
                  <div *ngFor="let page of pagesArray" (click)="goToPage(page)"
                      [class.active]="currentPage === page"
                      class="alert pagination-button fw-bold me-2"
                      style="font-size: 13px; padding: 10px 15px;">
                    {{ page }}
                  </div>
                  <button (click)="nextPage()" [disabled]="currentPage === totalPages" class="alert bg-light" style="font-size: 13px; padding: 10px 15px;">
                    <i class="bi-chevron-right"></i>
                  </button>
                </div>
              </div>
              <!-- Pagination -->

            </div>
            <div *ngIf="filteredOrderHeaderList == null || filteredOrderHeaderList.length == 0" class="row" style="height: 550px">
              <div class="col-6 mx-auto no-clinics-assgined-root text-center my-auto">
                <h3 class="fw-bold">No Orders Received Yet</h3>
                <p class="text-black-50 mt-2" style="font-size: 14px;">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit. Vero
                  minus velit necessitatibus assumenda quas est. Facilis sapiente
                  expedita earum beatae?
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
     </div>
    <!-- Body -->
  </div>
</div>
