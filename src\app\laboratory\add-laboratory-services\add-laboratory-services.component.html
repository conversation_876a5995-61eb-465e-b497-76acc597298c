<div class="row bg-white">
  <div class="col-12">
    <div class="row g-0 w-100 bg-white">
      <div class="container g-0">
        <div class="header-row">
          <div class="header-row-h1">Add Laboratory Services</div>
        </div>
        <div class="header-bottom-line"></div>
        <br />

        <div class="row ">
          <div class="col d-flex">
            <div class="add-service">Add Service</div>
          </div>
          <div class="col d-flex justify-content-end">
            <i class="bi bi-pencil-square"></i>
          </div>
        </div>
        <br />

        <div class="rectangle">
          <form [formGroup]="laboratorySetupForm" (ngSubmit)="saveLaboratoryService()">
            <div class="row">
              <div class="col-md-6 d-flex flex-column">
                <label for="category">Select Laboratory Category</label>
                <select id="category" name="category" formControlName="category" (change)="onCategoryChange()">
                  <option *ngFor="let laboratoryCategory of laboratoryCategories" [ngValue]="laboratoryCategory">
                    {{ laboratoryCategory.laboratoryCategoryName }}
                  </option>
                </select>
                <div *ngIf="laboratorySetupForm.get('category')?.invalid && (laboratorySetupForm.get('category')?.dirty || laboratorySetupForm.get('category')?.touched)">
                  <small class="text-danger" *ngIf="laboratorySetupForm.get('category')?.errors?.['required']">
                    Category is required.
                  </small>
                </div>
              </div>

              <div class="col-md-6 d-flex flex-column">
                <label for="subCategory">Select Laboratory Sub Category</label>
                <select id="subCategory" name="subCategory" formControlName="subCategory">
                  <option *ngFor="let laboratorySubCategory of laboratorySubCategories" [ngValue]="laboratorySubCategory">
                    {{ laboratorySubCategory.laboratorySubCategoryName }}
                  </option>
                </select>
                <div *ngIf="laboratorySetupForm.get('subCategory')?.invalid && (laboratorySetupForm.get('subCategory')?.dirty || laboratorySetupForm.get('subCategory')?.touched)">
                  <small class="text-danger" *ngIf="laboratorySetupForm.get('subCategory')?.errors?.['required']">
                    Sub category is required.
                  </small>
                </div>
              </div>
            </div>

            <label for="description" class="mt-2">Description</label>
            <textarea id="description" name="description" placeholder="Item 01" formControlName="description" class="description-textarea"></textarea>
            <div *ngIf="laboratorySetupForm.get('description')?.invalid && (laboratorySetupForm.get('description')?.dirty || laboratorySetupForm.get('description')?.touched)">
              <small class="text-danger" *ngIf="laboratorySetupForm.get('description')?.errors?.['required']">
                Description is required.
              </small>
            </div>

            <label for="price" class="mt-2">Price</label>
            <input type="text" id="price" name="price" placeholder="LKR" formControlName="price" class="price-input" />
            <div *ngIf="laboratorySetupForm.get('price')?.invalid && (laboratorySetupForm.get('price')?.dirty || laboratorySetupForm.get('price')?.touched)">
              <small class="text-danger" *ngIf="laboratorySetupForm.get('price')?.errors?.['required']">Price is required.</small>
              <small class="text-danger" *ngIf="laboratorySetupForm.get('price')?.errors?.['pattern']">Please enter a valid price.</small>
            </div>

            <!-- Submit Button Row -->
            <div class="row mt-4">
              <div class="d-flex justify-content-end gap-3 ms-auto mt-2">
                <button class="btn btn-secondary" type="button">Schedule</button>
                <button class="btn btn-primary" type="submit" [disabled]="isLoading">
                  <span *ngIf="isLoading">Saving...</span>
                  <span *ngIf="!isLoading">Add Service</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
