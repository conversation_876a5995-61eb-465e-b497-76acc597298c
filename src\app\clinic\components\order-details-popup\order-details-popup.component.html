<div class="popup-overlay">
    <div class="popup-box">
        <div style="width: 100%; display: flex; flex-direction: row; justify-content: flex-end;">
            <button style="background: none; border: none; font-size: 20px; cursor: pointer;" (click)="close()">
                &#10005;
            </button>
        </div>

        <div style="display: flex; flex-direction: column; margin-bottom: 80px;">
            <div style="display: flex; flex-direction: row; font-weight: 600; font-size: 25px;">View Order</div>
            <div style="display: flex; flex-direction: row; font-weight: 500; font-size: 15px; padding-top: 10px;">
                <div style="width: 140px; display: flex; flex-direction: row;  justify-content: flex-start; font-weight: 600;">Patient Name</div>
                <div>ravindra gunasekara</div>
            </div>
            <div style="display: flex; flex-direction: row; font-weight: 500; font-size: 15px; padding-top: 10px;">
                <div style="width: 140px; display: flex; flex-direction: row;  justify-content: flex-start; font-weight: 600;">
                    Address</div>
                <div style="display: flex; flex-direction: row;  justify-content: flex-start;">MyDent Dental Clinic,Colombo 12</div>
            </div>
            <div style="display: flex; flex-direction: row; font-weight: 500; font-size: 15px; padding-top: 10px;">
                <div style="width: 140px; display: flex; flex-direction: row;  justify-content: flex-start; font-weight: 600;">
                    Contact</div>
                <div>0112 700 700</div>
            </div>
        </div>

        <div style="display: flex; flex-direction: column;">
            <div style="display: flex; width: 100%; flex-direction: row; height: 40px; background-color: #FFB07D3B; border-style: solid; border-top: 20px; border-left: 20px; border-right: 20px; border-color: #FE9652; text-decoration: white; align-items: center; padding-inline: 10px;">
                <div style="display: flex; flex-direction: row; justify-items: start; width: 25%;">
                    Service
                </div>
                <div style="display: flex; flex-direction: row; justify-items: start; width: 40%;">
                    Description
                </div>
                <div style="display: flex; flex-direction: row; justify-items: start; width: 10%;">
                    Qty
                </div>
                <div style="display: flex; flex-direction: row; justify-items: start; width: 25%;">
                    Price
                </div>
            </div>

            <div
                style="display: flex; width: 100%; flex-direction: row; height: 40px; text-decoration: white; align-items: center; margin-top: 20px; padding-inline: 10px;">
                <div style="display: flex; flex-direction: row; justify-items: start; width: 25%;">
                    Dental Crowns
                </div>
                <div style="display: flex; flex-direction: row; justify-items: start; width: 40%;">
                    porcelains and ceramics
                </div>
                <div style="display: flex; flex-direction: row; justify-items: start; width: 10%;">
                    01
                </div>
                <div style="display: flex; flex-direction: row; justify-items: start; width: 25%;">
                    LKR. 52 000
                </div>
            </div>

            <div style="display: flex; width: 100%; justify-content: end; flex-direction: row; margin-top: 100px; gap: 20px;">
                <button
                    style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;">Reject</button>
                <button style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;"  (click)="openAcceptPopup(orders)">Accept</button>
            </div>
        </div>
    </div>
</div>

<app-accept-popup *ngIf="showAcceptPopup" [order]="selectedOrder" (closePopup)="closeAcceptPopup()"
    (orderAccepted)="handleOrderAccepted($event)">
</app-accept-popup>
