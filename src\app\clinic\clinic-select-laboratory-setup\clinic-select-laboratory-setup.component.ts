import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormGroup, FormControl } from '@angular/forms';
import {
  LaboratorySetup,
  LaboratoryCategory,
  LaboratorySubCategory,
} from '../../laboratory/laboratory';
import { LaboratoryService } from '../../laboratory/laboratory.service';

@Component({
  selector: 'app-clinic-select-laboratory-setup',
  templateUrl: './clinic-select-laboratory-setup.component.html',
  styleUrls: ['./clinic-select-laboratory-setup.component.css'],
})
export class ClinicSelectLaboratorySetupComponent implements OnInit {
  laboratoryCategories: LaboratoryCategory[] = [];
  laboratorySubCategories: LaboratorySubCategory[] = [];
  laboratorySetups: LaboratorySetup[] = [];
  filteredLaboratorySetups: LaboratorySetup[] = [];
  selectedCategory: number | null = null;
  selectedSubCategory: number | null = null;
  selectedItem: LaboratorySetup | null = null;
  searchTerm: string = '';

  currentPage: number = 1;
  itemsPerPage: number = 4;
  visiblePages: number[] = [];
  totalPages: number = 0;

  filterForm = new FormGroup({
    category: new FormControl(''),
    subCategory: new FormControl(''),
  });

  constructor(
    private laboratoryService: LaboratoryService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getLaboratoryCategories();
    this.getLaboratorySetupsList();
  }

  getLaboratoryCategories(): void {
    this.laboratoryService
      .getLaboratoryCategoriesList()
      .subscribe((categories: LaboratoryCategory[]) => {
        this.laboratoryCategories = categories;
      });
  }

  getLaboratorySetupsList(): void {
    this.laboratoryService.getLaboratorySetupsList().subscribe(
      (data: LaboratorySetup[]) => {
        this.laboratorySetups = data;
        this.applyFilters();
      },
      (error) => {
        console.error('Error fetching laboratory setups:', error);
      }
    );
  }

  onCategoryChange(event: any): void {
    this.selectedCategory = event.target.value;
    this.getSubCategories(this.selectedCategory);
    this.applyFilters();
  }

  onSubCategoryChange(event: any): void {
    this.selectedSubCategory = event.target.value;
    this.applyFilters();
  }

  getSubCategories(categoryId: number | null): void {
    if (categoryId) {
      this.laboratoryService
        .getLaboratorySubCategoriesList(categoryId)
        .subscribe((subCategories: LaboratorySubCategory[]) => {
          this.laboratorySubCategories = subCategories;
        });
    } else {
      this.laboratorySubCategories = [];
    }
  }

  applyFilters(): void {
    this.filteredLaboratorySetups = this.laboratorySetups.filter((setup) => {
      const matchesCategory = this.selectedCategory
        ? setup.laboratoryCategoryId.laboratoryCategoryId ==
          this.selectedCategory
        : true;

      const matchesSubCategory = this.selectedSubCategory
        ? setup.laboratorySubCategoryId.laboratorySubCategoryId ==
          this.selectedSubCategory
        : true;

      const matchesSearchTerm = this.searchTerm
        ? setup.description
            ?.toLowerCase()
            .includes(this.searchTerm.toLowerCase()) ||
          setup.status?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          setup.laboratoryCategoryId.laboratoryCategoryName
            ?.toLowerCase()
            .includes(this.searchTerm.toLowerCase()) ||
          setup.laboratorySubCategoryId.laboratorySubCategoryName
            ?.toLowerCase()
            .includes(this.searchTerm.toLowerCase()) ||
          setup.price
            ?.toString()
            .toLowerCase()
            .includes(this.searchTerm.toLowerCase())
        : true;
      return matchesCategory && matchesSubCategory && matchesSearchTerm;
    });

    this.updatePagination();
  }

  updatePagination(): void {
    this.totalPages = Math.ceil(
      this.filteredLaboratorySetups.length / this.itemsPerPage
    );
    this.visiblePages = Array.from(
      { length: this.totalPages },
      (_, i) => i + 1
    );
    this.currentPage = 1;
  }

  viewMoreDetails(item: LaboratorySetup): void {
    console.log('Working....');
    console.log(JSON.stringify(this.selectedItem));

    this.selectedItem = item;
  }

  clearSelectedItem(): void {
    this.selectedItem = null;
  }

  get paginatedItems(): LaboratorySetup[] {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredLaboratorySetups.slice(start, end);
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  goToPage(page: number): void {
    if (page > 0 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  navigateToOrder(laboratorySetup: LaboratorySetup): void {
    localStorage.setItem('laboratorySetup', JSON.stringify(laboratorySetup));
    this.router.navigate(['/clinic/add-order']);
  }

  backToOrderListView(){
    this.router.navigate(['/clinic/laboratory-orders']);
  }
}
