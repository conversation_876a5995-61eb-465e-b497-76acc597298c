<div class="row">
  <div class="col-12">
    <div class="send-orders-container">
      <div class="header-row">
        <div class="header-row-h1">Laboratory Setups</div>
        <div class="search-bar">
          <i class="bi bi-search search-icon"></i>
          <input
            type="text"
            placeholder="Search Laboratory Setup"
            [(ngModel)]="searchTerm"
            id="search-input"
          />
        </div>
      </div>
      <div class="header-bottom-line"></div>
      <br />
      <div class="laboratory-setups-container">
        <div class="button-container">
          <button
            class="add-setup-button"
            (click)="navigateAddLaboratorySetup()"
          >
            Add New Setup
          </button>
          <button (click)="toggleShowAll()" class="see-all-button">
            {{ showAll ? 'Show Less <<' : 'See All >>' }}
          </button>
        </div>
      </div>

      <div *ngIf="showAll">
        <table class="send-order-table">
          <thead>
            <tr>
              <th width="10%">Setup ID</th>
              <th width="15%">Category Name</th>
              <th width="20%">Sub Category Name</th>
              <th width="25%">Description</th>
              <th width="15%">Price</th>
              <th width="15%">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let setup of filteredLaboratorySetups()">
              <td>{{ setup.laboratorySetupId }}</td>
              <td>{{ setup.laboratoryCategoryId.laboratoryCategoryName }}</td>
              <td>{{ setup.laboratorySubCategoryId.laboratorySubCategoryName }}</td>
              <td>{{ setup.description }}</td>
              <td>{{ setup.price | currency : 'LKR' }}</td>
              <td class="status-cell">
                <button
                  [ngClass]="{
                    'status actived': setup.status === 'Active',
                    'status inactived': setup.status === 'Inactive'
                  }"
                >
                  {{ setup.status }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="card-list">
          <div
            class="order-card"
            *ngFor="let setup of filteredLaboratorySetups()"
          >
            <div class="card-header">
              Setup ID: {{ setup.laboratorySetupId }}
            </div>
            <div class="card-body">
              <p><strong>Category Name:</strong> {{ setup.laboratoryCategoryId.laboratoryCategoryName }}</p>
              <p><strong>Sub Category Name:</strong> {{ setup.laboratorySubCategoryId.laboratorySubCategoryName }}</p>
              <p><strong>Description:</strong> {{ setup.description }}</p>
              <p><strong>Price:</strong> {{ setup.price | currency : 'LKR' }}</p>
            </div>
            <div class="card-footer">
              <button
                [ngClass]="{
                  'status actived': setup.status === 'Active',
                  'status inactived': setup.status === 'Inactive'
                }"
              >
                {{ setup.status }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="!showAll">
        <table class="send-order-table">
          <thead>
            <tr>
              <th width="10%">Setup ID</th>
              <th width="15%">Category Name</th>
              <th width="20%">Sub Category Name</th>
              <th width="25%">Description</th>
              <th width="15%">Price</th>
              <th width="15%">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let setup of filteredLaboratorySetups() | slice: (currentPage - 1) * itemsPerPage : currentPage * itemsPerPage">
              <td>{{ setup.laboratorySetupId }}</td>
              <td>{{ setup.laboratoryCategoryId.laboratoryCategoryName }}</td>
              <td>{{ setup.laboratorySubCategoryId.laboratorySubCategoryName }}</td>
              <td>{{ setup.description }}</td>
              <td>{{ setup.price | currency : 'LKR' }}</td>
              <td class="status-cell">
                <button
                  [ngClass]="{
                    'status actived': setup.status === 'Active',
                    'status inactived': setup.status === 'Inactive'
                  }"
                >
                  {{ setup.status }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="card-list">
          <div
            class="order-card"
            *ngFor="let setup of filteredLaboratorySetups() | slice: (currentPage - 1) * itemsPerPage : currentPage * itemsPerPage"
          >
            <div class="card-header">
              Setup ID: {{ setup.laboratorySetupId }}
            </div>
            <div class="card-body">
              <p><strong>Category Name:</strong> {{ setup.laboratoryCategoryId.laboratoryCategoryName }}</p>
              <p><strong>Sub Category Name:</strong> {{ setup.laboratorySubCategoryId.laboratorySubCategoryName }}</p>
              <p><strong>Description:</strong> {{ setup.description }}</p>
              <p><strong>Price:</strong> {{ setup.price | currency : 'LKR' }}</p>
            </div>
            <div class="card-footer">
              <button
                [ngClass]="{
                  'status actived': setup.status === 'Active',
                  'status inactived': setup.status === 'Inactive'
                }"
              >
                {{ setup.status }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="pagination-container" *ngIf="!showAll">
        <ul class="pagination">
          <li
            class="page-item"
            [class.disabled]="currentPage === 1"
            (click)="goToPage(currentPage - 1)"
          >
            <a class="page-link" aria-label="Previous">
              <span aria-hidden="true">&laquo;</span>
            </a>
          </li>
          <li
            class="page-item"
            *ngFor="let page of visiblePages"
            [class.active]="page === currentPage"
            (click)="goToPage(page)"
          >
            <a class="page-link">{{ page }}</a>
          </li>
          <li
            class="page-item"
            [class.disabled]="currentPage === visiblePages.length"
            (click)="goToPage(currentPage + 1)"
          >
            <a class="page-link" aria-label="Next">
              <span aria-hidden="true">&raquo;</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
