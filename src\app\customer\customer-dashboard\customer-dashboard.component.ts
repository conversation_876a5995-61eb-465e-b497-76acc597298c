import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Appointment } from '../customer';
import { CustomerService } from '../customer.service';

@Component({
  selector: 'app-customer-dashboard',
  templateUrl: './customer-dashboard.component.html',
  styleUrls: ['./customer-dashboard.component.css']
})
export class CustomerDashboardComponent {
  appointments: Appointment[] = [];
  searchTermIncomplete: string = '';
  searchTermCompleted: string = '';

  constructor(private customerService: CustomerService, private router: Router) {}

  ngOnInit(): void {
    const userIdString = localStorage.getItem('userid');
    const userId = userIdString ? parseInt(userIdString, 10) : null;
    if (userId !== null) {
      this.loadAppointments(userId);
    } else {
      console.error('User ID is not available in localStorage.');
    }
  }

  loadAppointments(userId: number): void {
    this.customerService.getAppointmentsByUserId(userId).subscribe(
      (data: Appointment[]) => {
        console.log('Received appointments:', data);
        this.appointments = data;
      },
      (error) => {
        console.error('Error fetching appointments:', error);
      }
    );
  }

  get filteredIncompleteAppointments(): Appointment[] {
    return this.appointments
      .filter(appointment => appointment.status !== 'Completed')
      .filter(appointment => 
        !this.searchTermIncomplete ||
        appointment.fromDate.includes(this.searchTermIncomplete) ||
        appointment.toDate.includes(this.searchTermIncomplete) ||
        appointment.clinics.name.toLowerCase().includes(this.searchTermIncomplete.toLowerCase()) ||
        appointment.preferredservice.toLowerCase().includes(this.searchTermIncomplete.toLowerCase())
      );
  }

  get filteredCompletedAppointments(): Appointment[] {
    return this.appointments
      .filter(appointment => appointment.status === 'Completed')
      .filter(appointment => 
        !this.searchTermCompleted ||
        appointment.fromDate.includes(this.searchTermCompleted) ||
        appointment.toDate.includes(this.searchTermCompleted) ||
        appointment.clinics.name.toLowerCase().includes(this.searchTermCompleted.toLowerCase()) ||
        appointment.preferredservice.toLowerCase().includes(this.searchTermCompleted.toLowerCase())
      );
  }

  getOldestAppointment(): Appointment | null {
    const incompleteAppointments = this.appointments.filter(appointment => appointment.status !== 'Completed');
  
    if (incompleteAppointments.length === 0) return null;
  
    return incompleteAppointments.reduce((oldest, current) => {
      const oldestDate = new Date(oldest.fromDate);
      const currentDate = new Date(current.fromDate);
      return currentDate < oldestDate ? current : oldest;
    });
  }
  

  navigateMakeAppointment(): void {
    this.router.navigate(['/customer/add-appointment']);
  }
}

