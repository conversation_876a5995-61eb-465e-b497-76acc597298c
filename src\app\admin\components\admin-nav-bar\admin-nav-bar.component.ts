import { DOCUMENT } from '@angular/common';
import { Component, Inject, OnInit, Renderer2 } from '@angular/core';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-admin-nav-bar',
  templateUrl: './admin-nav-bar.component.html',
  styleUrls: ['./admin-nav-bar.component.css']
})
export class AdminNavBarComponent implements OnInit {
  msgCount: number = 0;
  notificationCount: number = 0;
  loginStatus: number = 0;
  assignedUserId: number = 0;
  notifications: Notification[] = [];
  description: string = '';
  notificationId: number = 0;
  notificationStatus: string = '';
  public compName: string = '';
  taskHeads: any = [];
  countArray: any = [];

  isIframe = false;
  isShow = false;
  loginDisplay = false;
  private readonly _destroying$ = new Subject<void>();
  userName: any;
  userProfilePic: any;

  constructor(
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document
  ) {}

  ngOnInit(): void {
    const firstName = localStorage.getItem("firstName");
    const lastName = localStorage.getItem("lastName");
    this.userName = firstName !=null || lastName !=null ? firstName+ " " +lastName : "Jane Anderson";
    this.userProfilePic = "https://img.icons8.com/?size=150&id=20749&format=png&color=000000";
  }

  toggleSidebar() {
    this.isShow = !this.isShow;
    if (this.isShow == true) {
      this.renderer.addClass(this.document.body, 'toggle-sidebar');
    } else {
      this.renderer.removeClass(this.document.body, 'toggle-sidebar');
    }
  }
}
