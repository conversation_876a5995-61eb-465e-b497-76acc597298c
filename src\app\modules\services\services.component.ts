import { Component } from '@angular/core';

@Component({
  selector: 'app-services',
  templateUrl: './services.component.html',
  styleUrls: ['./services.component.css']
})
export class ServicesComponent {
  dentalServices = ['Dental Crowns', 'Dental Implants', 'Teeth Whitening'];
  subDentalServices = ['Porcelain Crowns', 'Zirconia Crowns'];
  services = ['Service 1', 'Service 2'];
  searchTerm: string = '';
  selectedDentalService!: string;
  selectedSubDentalService!: string;
  selectedService!: string;

  allItems = [
    { id: "1", title: 'Asia Laboratory', contact: '011 2 700 700', image: '../../assets/images/dentures.jpeg', description: 'Porcelain Crowns', price: 'LKR. 50 000' },
    { id: "2", title: 'Asia Laboratory', contact: '011 2 700 700', image: '../../assets/images/dentures.jpeg', description: 'Zirconia Crowns', price: 'LKR. 75 000' },
    { id: "3", title: 'Asia Laboratory', contact: '011 2 700 700', image: '../../assets/images/dentures.jpeg', description: 'Zirconia Crowns', price: 'LKR. 25 000' },
    { id: "4", title: 'Asia Laboratory', contact: '011 2 700 700', image: '../../assets/images/dentures.jpeg', description: 'Zirconia Crowns', price: 'LKR. 195 000' },
    { id: "5", title: 'Asia Laboratory', contact: '011 2 700 700', image: '../../assets/images/dentures.jpeg', description: 'Porcelain Crowns', price: 'LKR. 145 000' },
  ];

  items = [...this.allItems];
  selectedItem: any = null;

  toggleSidebar() {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
    }
  }

  onDentalServiceChange(event: any) {
    this.selectedDentalService = event.target.value;
    this.filterItems();
  }

  onSubDentalServiceChange(event: any) {
    this.selectedSubDentalService = event.target.value;
    this.filterItems();
  }

  onServiceChange(event: any) {
    this.selectedService = event.target.value;
    this.filterItems();
  }

  filterItems() {
    this.items = this.allItems.filter(item => {
      return (!this.selectedDentalService || item.description.includes(this.selectedDentalService)) &&
             (!this.selectedSubDentalService || item.description.includes(this.selectedSubDentalService)) &&
             (!this.selectedService || item.description.includes(this.selectedService)) &&
             (!this.searchTerm || item.description.toLowerCase().includes(this.searchTerm.toLowerCase()));
    });
  }
  

  viewMoreDetails(item: any) {
    this.selectedItem = item;
  }

  clearSelectedItem() {
    this.selectedItem = null;
  }
}
