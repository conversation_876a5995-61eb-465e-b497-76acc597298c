<div class="container-fluid h-100 custom-background" >
  <div class="row" style="height: inherit;">
    <div class="col-12" style="height: 80px !important;">
      <app-default-navbar loggedUser="Hello" />
    </div>
    <div class="col-12" style="height: calc(100% - 80px) !important;">
      <div class="container h-100">
        <div class="row justify-content-center align-content-center" style="height: 100% !important;">
          <div class="col-12 col-md-10 col-lg-8 col-xl-6 px-2 px-lg-5">
            <div id="login-panel" class="card border-orange p-4 p-md-5">
              <div class="card-body py-3">
                <h3 class="text-start fs-4 login-header text-center" >Login</h3>
                <p class="text-black-50 login-sub-header mt-1 text-center">Enter the <Strong>Username</Strong> & <strong>Password</strong> Here</p>
                <form class="mt-4" (ngSubmit)="onSubmitLogin()" #loginForm="ngForm">
                  <div class="form-group pt-2">
                    <label for="username" class="input-label">Email address</label>
                    <input type="text" id="username" name="username" [(ngModel)]="user.username" class="form-control" required
                      minlength="3" #usernameField="ngModel" />
                    <div *ngIf="
                        usernameField.invalid &&
                        (usernameField.dirty || usernameField.touched)
                      " class="text-danger input-label px-1 mt-1">
                      <div *ngIf="usernameField.errors?.['required']">
                        Email is required.
                      </div>
                      <div *ngIf="usernameField.errors?.['minlength']">
                        Email must be valid one.
                      </div>
                    </div>
                  </div>
                  <label class="input-label" for="password">Password</label>
                  <div class="form-group position-relative" style="position: relative;">
                    <input type="password" id="password" name="password" [(ngModel)]="user.password" class="form-control"
                      required minlength="4" #passwordField="ngModel" style="padding-right: 40px;" />
                    <button type="button" id="show-password" class="show-password" (click)="togglePasswordVisibility()" style="
                        position: absolute;
                        right: 15px;
                        top: 24px;
                        padding: 0;
                      ">
                      <i [ngClass]="passwordVisible ? 'bi bi-eye-fill' : 'bi bi-eye-slash-fill'"></i>
                    </button>
                    <div *ngIf="
                        passwordField.invalid && (passwordField.dirty || passwordField.touched)
                      " class="text-danger input-label mt-1 px-1">
                      <div *ngIf="passwordField.errors?.['required']">
                        Password is required.
                      </div>
                    </div>
                  </div>

                  <div class="form-group d-grid d-sm-flex justify-content-sm-between mt-4 px-1 pt-3"
                    style="display: flex; flex-direction: row; width: 100%;">
                    <div
                    class="form-check"
                    style="display: flex; flex-direction: row; align-items: center;"
                    [ngStyle]="{ 'opacity': (usernameField.invalid || passwordField.invalid) ? 0.5 : 1, 'pointer-events': (usernameField.invalid || passwordField.invalid) ? 'none' : 'auto' }"
                  >
                    <input
                      type="checkbox"
                      class="form-check-input custom-check-box"
                      id="remember"
                      [(ngModel)]="remember"
                      name="remember"
                    />
                    <label class="form-check-label" for="remember">&nbsp;Remember me</label>
                    </div>
                    <div class="d-grid">
                      <a class="forgot-password w-100 text-end text-sm-start">Forgot Password?</a>
                    </div>
                  </div>
                  <app-primary-action-button buttonUI="primary" [buttonHeight]=45 class="d-grid" buttonText="Login" buttonType="submit" [disabled]="loginForm.invalid"/>
                </form>
                <div class="text-center mt-3">
                  <p style="font-size: 14px;">
                    Not Registered?
                    <a id="register-link" (click)="navigateToUserSelection()">Register</a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
