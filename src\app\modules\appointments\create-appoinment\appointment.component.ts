import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { EMPTY, Observable, from, map, mapTo, of, switchMap, tap } from 'rxjs'; // Import Observable
import { User, UserCategory } from '../../../user/user';

import { Appointments } from '../appointments';
import { AppointmentsService } from '../appointments.service';
import { Clinic } from 'src/app/clinic/clinic';
import { Customer } from './../customer';
import { SharedService } from '../../shared-services/shared.service';
import { UserService } from '../../../user/user.service';

interface Clinici {
  clinicId: number;
  name: string;
  city: string;
}

@Component({
  selector: 'app-appointment',
  templateUrl: './appointment.component.html',
  styleUrls: ['./appointment.component.css']
})
export class AppointmentComponent implements OnInit {

  appointments: Appointments = new Appointments();
  customer: Customer = new Customer();
  user: User = new User();
  userCategory: UserCategory = new UserCategory();
  appointmentForm: FormGroup;
  currentStep: number = 1;
  steps: string[] = ['Personal Info', 'Service Details', 'Clinic Selection', 'Confirmation'];
  currentDate: string = this.getCurrentDate();

  userEmailExistsMessage: string = '';  // Ensure this is defined
  isEmailRegistered: boolean = false;
  isImmediateBooking: boolean = false;

  selectedCity: string = '';
  nearestCity: string = '';

  districts: string[] = [];
  cities: String[] = [];

   clinics: Clinici[] = [

  ];
  selectedClinic: any | undefined;
  defaultDate: string = new Date().toISOString().substr(0, 10);

  passwordVisible: boolean = false;  // Declare passwordVisible as false initially

  private getCurrentDate(): string {
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!
    const yyyy = today.getFullYear();
    return `${yyyy}-${mm}-${dd}`;
}

constructor(
  private fb: FormBuilder,
  private appointmentsService: AppointmentsService,
  private userService: UserService,
  private sharedService: SharedService
) {
  // Set default date to today
  const today = new Date();
  this.defaultDate = today.toISOString().substring(0, 10); // Format date as YYYY-MM-DD

  // Initialize form
  this.appointmentForm = this.fb.group({
    firstName: ['', Validators.required],
    lastName: ['', Validators.required],
    address: ['', [Validators.required, Validators.minLength(10)]],
    city: [{ value: '', disabled: true }, [Validators.required, Validators.minLength(3)]],
    state: ['', Validators.required],
    district: ['', [Validators.required, Validators.minLength(3)]],
    telephone: ['', [Validators.required, Validators.pattern(/^\+?[0-9]{7,15}$/)]],
    email: ['', [Validators.required, Validators.email], [this.checkUserEmail.bind(this)]],
    username: ['', Validators.required],
    password: ['', [
      Validators.required,
      Validators.minLength(8),
      Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).*')
    ]],
    confirmPassword: ['', Validators.required],
    preferredservice: [''],
    nearestCity: [''],
    fromDate: [{ value: this.defaultDate, disabled: false }, Validators.required],
    toDate: [{ value: this.defaultDate, disabled: false }, Validators.required],
    fromTime: ['', Validators.required],
    toTime: ['', Validators.required],
    immediate: [false]
  }, { validators: this.passwordMatchValidator
  }); // Note: 'validators' is plural here



  // Subscribe to changes in the immediate checkbox
  this.appointmentForm.get('immediate')?.valueChanges.subscribe(value => {
    this.onImmediateBookingChange(value);
  });

}
// Toggle password visibility
  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
  }

// Accept the value from the checkbox
onImmediateBookingChange(isImmediate: boolean): void {
  if (isImmediate) {
      // Get the current date and time
      const currentDate = new Date().toISOString().substring(0, 10); // Format as YYYY-MM-DD
      const currentTime = new Date().toTimeString().substring(0, 5); // Format as HH:MM

      // Update the form values for immediate booking
      this.appointmentForm.patchValue({
          fromDate: currentDate,
          toDate: currentDate, // Setting to the same date for immediate booking
          fromTime: currentTime,
          toTime: '23:59' // Set the end time to the end of the day
      });
  } else {
      // Optionally clear values or handle logic for unchecking
      this.appointmentForm.patchValue({
          fromDate: '',
          toDate: '',
          fromTime: '',
          toTime: ''
      });
  }
}



// Custom validator to ensure date is today or in the future
private futureDateValidator(control: AbstractControl): { [key: string]: any } | null {
  const selectedDate = new Date(control.value);
  const today = new Date(this.defaultDate);

  return selectedDate < today ? { futureDate: true } : null;
}

  passwordMatchValidator(formGroup: FormGroup) {
    const password = formGroup.get('password');
    const confirmPassword = formGroup.get('confirmPassword');

    if (!password || !confirmPassword) {
      return null;
    }

    if (confirmPassword.errors && !confirmPassword.errors['mismatch']) {
      return null;
    }

    if (password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ mismatch: true });
    } else {
      confirmPassword.setErrors(null); // Clear the mismatch error if they match
    }

    return null; // Return null for no errors
  }
  checkUserEmail(control: any): Observable<{ [key: string]: any } | null> {
    const userEmail = control.value;

    if (userEmail) {
      return this.userService.checkUser(userEmail).pipe(
        map((data) => {
          if (data) {
            this.isEmailRegistered = true;
            this.userEmailExistsMessage = 'Email already registered. Try another.';
            return { emailExists: true }; // Return an error object
          } else {
            this.isEmailRegistered = false;
            this.userEmailExistsMessage = '';
            return null; // Return null if no error
          }
        })
      );
    } else {
      return of(null); // Return null if the control value is empty
    }
  }

  ngOnInit(): void {
    localStorage.clear();
    this.userService.getUserCategoryById(5).subscribe((response: UserCategory) => {
      this.userCategory = response;
    });
    this.districts = this.sharedService.getDistricts();
  }

  onDistrictChange(event: Event): void {
    const selectedDistrict = (event.target as HTMLSelectElement).value;

    if (selectedDistrict) {
      this.appointmentForm.get('city')?.enable();
      this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);
    } else {
      this.appointmentForm.get('city')?.disable();
      this.cities = [];
    }
    this.appointmentForm.get('city')?.setValue('');
  }

  onCitySelect(): void {
    this.nearestCity = this.selectedCity;
    this.appointmentForm.controls['nearestCity'].setValue(this.selectedCity);
  }

  nextStep(): void {

    if (this.currentStep === 1) {
      this.saveCustomerDetails();
    }

    if (this.currentStep < this.steps.length) {
      this.currentStep++;
    }

  }
  cliniclistload(): void {
    const fromDate = this.appointmentForm.get('fromDate')?.value;

    // Convert string to Date object
    const date = new Date(fromDate);

    // Get the day of the week (0 is Sunday, 1 is Monday, ..., 6 is Saturday)
    const dayOfWeek = date.getDay();

    // Array to convert day index to name
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayName = days[dayOfWeek]; // Get the day name like 'Monday'

    console.log('Selected Day:', dayName); // Log the selected day for debugging

    // Retrieve other form values
    const fromTime = this.appointmentForm.get('fromTime')?.value;
    const nearestCity = this.appointmentForm.get('city')?.value;
    const preferredservice = this.appointmentForm.get('preferredservice')?.value;

    // API call to find clinics
    this.appointmentsService.findClinics(dayName, fromTime, nearestCity, preferredservice).subscribe((response) => {
      this.clinics = response; // Assign response to clinics array

      // Check if clinics are found
      if (this.clinics.length === 0) {
        // Alert if no clinics are loaded
        alert('No clinics found in ' + nearestCity + '. Please try another nearest city.');
        this.selectedClinic = undefined; // Reset selected clinic

        // Set current step to 2 after alert acknowledgment
        this.currentStep = 2; // Update current step to 2

        return; // Exit the method to prevent further processing
      } else {
        console.log('Clinics loaded:', this.clinics); // Log clinics data
        this.selectedClinic = undefined; // Reset selected clinic when new data is fetched
      }
    }, (error) => {
      // Handle error if the API call fails
      console.error('Error loading clinics:', error);
      alert('An error occurred while loading clinics. Please try again later.');
    });
  }



  isStepInvalid(): boolean {
    // Step 1 validation
    if (this.currentStep === 1) {
      return !!this.appointmentForm.get('firstName')?.invalid ||
             !!this.appointmentForm.get('lastName')?.invalid ||
             !!this.appointmentForm.get('address')?.invalid ||
             !!this.appointmentForm.get('email')?.invalid ||
             !!this.appointmentForm.get('district')?.invalid ||
             !!this.appointmentForm.get('city')?.invalid ||
             !!this.appointmentForm.get('telephone')?.invalid ||
             !!this.appointmentForm.get('password')?.invalid ||
             !!this.appointmentForm.get('confirmPassword')?.invalid;
    }

    // Step 2 validation
    if (this.currentStep === 2) {
      return !!this.appointmentForm.get('preferredservice')?.invalid ||
             !!this.appointmentForm.get('nearestCity')?.invalid ||
             !!this.appointmentForm.get('fromDate')?.invalid ||
             !!this.appointmentForm.get('fromTime')?.invalid ||
             !!this.appointmentForm.get('toTime')?.invalid;
    }



    // Add checks for other steps if needed
    return false;
  }


  prevStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  saveCustomerDetails(): Observable<Customer> {
    const formValues = this.appointmentForm.value;

    this.appointments = {
        ...this.appointments,
        email: formValues.email,
    };

    this.updateEmail();

    return this.onUserRegister().pipe(
        switchMap((userId: number) => {
            // Create the customer object
            this.customer = {
                customerId: 0,
                userId: this.user.userId,
                firstName: formValues.firstName,
                lastName: formValues.lastName,
                address: formValues.address,
                city: formValues.city,
                state: formValues.state,
                country: '',
                telephone: formValues.telephone,
                email: this.appointments.email,
            };

            // Call the service to save the customer
            return this.appointmentsService.saveCustomer(this.customer);
        })
    );
}

  updateEmail(): void {
    this.user.username = this.appointments.email;
  }

  selectClinic(clinicId: number): void {
    this.selectedClinic = clinicId; // Update selected clinic on click
    console.log('Clinic selected:', clinicId);  // Log the clinic that was selected
    console.log('Current selectedClinic value:', this.selectedClinic);  // Log the updated selectedClinic value
  }

  resetForm(): void {
    this.appointmentForm.reset();
    this.currentStep = 1;
    this.selectedClinic = undefined;
  }

  cancelAppointment(): void {
    console.log('Appointment canceled.');
    this.resetForm();
  }

  completeAppointment(): void {
    console.log('Appointment completed.');
    this.resetForm();
  }

  onSubmit(): void {
    if (this.appointmentForm.valid) {
      this.saveCustomerDetails();
    }
  }

  // Save Appointment Method
saveAppointment(): void {

  // Call saveCustomerDetails() and handle its result
  // this.saveCustomerDetails().pipe(
  //     switchMap((customer: Customer) => {
  //         // Check if the customer is saved and populated
  //         if (customer && customer.customerId) {
  //             // Populate the appointments with the saved customer details
  //             const formValues = this.appointmentForm.value;

  //             this.appointments = {
  //                 ...this.appointments,
  //                 firstName: formValues.firstName,
  //                 lastName: formValues.lastName,
  //                 address: formValues.address,
  //                 city: formValues.city,
  //                 state: formValues.state,
  //                 district: formValues.district,
  //                 telephone: formValues.telephone,
  //                 email: formValues.email,
  //                 userName: formValues.username,
  //                 password: formValues.password,
  //                 preferredservice: formValues.preferredservice,
  //                 nearestCity: formValues.nearestCity,
  //                 fromDate: formValues.fromDate,
  //                 toDate: formValues.toDate,
  //                 fromTime: formValues.fromTime,
  //                 toTime: formValues.toTime,
  //                 clinics: new Clinic(),
  //                 customer: customer // Use the saved customer directly
  //             };
  //             this.appointments.clinics.clinicId = this.selectedClinic;

  //             // Call the service to save the appointment
  //             return this.appointmentsService.saveAppointments(this.appointments);
  //         } else {
  //             console.error('Customer is not populated correctly');
  //             return EMPTY; // Return an empty observable to end the chain
  //         }
  //     })
  // ).subscribe(
  //     (response: Appointments) => {
  //         console.log('Appointment saved successfully', response);
  //     },
  //     (error: any) => {
  //         console.error('Error saving appointment', error);
  //     }
  // );
}

  onUserRegister(): Observable<number> {
    this.user.userCategoryId = this.userCategory;
    return this.userService.register(this.user).pipe(
      tap((response: { id: number }) => {
        this.user.userId = response.id;
      }),
      map((response: { id: number }) => response.id)
    );
  }

}
