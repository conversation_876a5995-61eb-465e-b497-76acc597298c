<app-default-navbar loggedUser="Hello"/>
<div class="background-container">
  <div class="form-container">
    <h3>Supplier Registration</h3>
    <button class="backtoselection-button" (click)="navigateUserSelection()">Selection Menu</button>
    <form [formGroup]="supplierForm" (ngSubmit)="onUserTempRegister()">
      <div class="form-row">
        <div class="form-group">
          <label for="supplierName">Company Name</label>
          <input type="text" id="supplierName" name="supplierName" formControlName="supplierName" [(ngModel)]="userTemp.mainName"/>
          <div *ngIf="supplierForm.get('supplierName')?.invalid && (supplierForm.get('supplierName')?.dirty || supplierForm.get('supplierName')?.touched)">
            <small class="text-danger" *ngIf="supplierForm.get('supplierName')?.errors?.['required']">Supplier Name is required.</small>
            <small class="text-danger" *ngIf="supplierForm.get('supplierName')?.errors?.['pattern']">Supplier Name can only contain letters, numbers, spaces and ".()/,".</small>
          </div>
          <small *ngIf="isSupplierRegistered" class="text-danger">{{ supplierNameExistsMessage }}</small>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="address">Address</label>
          <textarea type="text" id="address" name="address" [(ngModel)]="userTemp.address" formControlName="address" rows="1"></textarea>
          <div *ngIf="
            supplierForm.get('address')?.invalid &&
            (supplierForm.get('address')?.dirty ||
            supplierForm.get('address')?.touched)
          ">
            <small class="text-danger" *ngIf="supplierForm.get('address')?.errors?.['required']">
              Address is required.
            </small>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" name="email" formControlName="email" [(ngModel)]="userTemp.userEmail"  (ngModelChange)="updateEmail()"/>
          <div *ngIf="supplierForm.get('email')?.invalid && (supplierForm.get('email')?.dirty || supplierForm.get('email')?.touched)">
            <small class="text-danger" *ngIf="supplierForm.get('email')?.errors?.['required']">Email is required.</small>
            <small class="text-danger" *ngIf="supplierForm.get('email')?.errors?.['email']">Invalid email format.</small>
          </div>
          <small *ngIf="isEmailRegistered" class="text-danger">{{ userEmailExistsMessage }}</small>
        </div>
      </div>

      <!-- <div class="form-row">
        <div class="form-group">
          <label for="city">City</label>
          <select
              name="city"
              id="city"
              formControlName="city"
              [(ngModel)]="supplier.city"
            >
              <option *ngFor="let city of cities" [value]="city">
                {{ city }}
              </option>
            </select>
          <div *ngIf="
            supplierForm.get('city')?.invalid &&
            (supplierForm.get('city')?.dirty || supplierForm.get('city')?.touched)
          ">
            <small class="text-danger" *ngIf="supplierForm.get('city')?.errors?.['required']">
              City is required.
            </small>
          </div>
        </div>
        <div class="form-group">
          <label for="district">District</label>
          <select
          name="district"
          id="district"
          formControlName="district"
          [(ngModel)]="supplier.state"
          (change)="onDistrictChange($event)"
        >
          <option *ngFor="let district of districts" [value]="district">
            {{ district }}
          </option>
        </select>
          <div *ngIf="
            supplierForm.get('district')?.invalid &&
            (supplierForm.get('district')?.dirty ||
            supplierForm.get('district')?.touched)
          ">
            <small class="text-danger" *ngIf="supplierForm.get('district')?.errors?.['required']">
              District is required.
            </small>
          </div>
        </div>
      </div> -->

      <div class="form-row">
        <div class="form-group">
          <label for="contactNumber">Contact Number</label>
          <input type="text" id="contactNumber" name="contactNumber" [(ngModel)]="userTemp.contactNumber" formControlName="tele"/>
          <div *ngIf="
              supplierForm.get('tele')?.invalid &&
              (supplierForm.get('tele')?.dirty || supplierForm.get('tele')?.touched)
            ">
            <small class="text-danger" *ngIf="supplierForm.get('tele')?.errors?.['required']">
              Contact Number is required.
            </small>
            <small class="text-danger" *ngIf="supplierForm.get('tele')?.errors?.['pattern']">
              Invalid Contact number.
            </small>
          </div>
        </div>
        <div class="form-group">
          <label for="contactPerson">Contact Person</label>
          <input type="text" id="contactPerson" name="contactPerson" formControlName="contactPerson"
            [(ngModel)]="userTemp.contactPerson"/>
          <div *ngIf="
              supplierForm.get('contactPerson')?.invalid &&
              (supplierForm.get('contactPerson')?.dirty || supplierForm.get('contactPerson')?.touched)
            ">
            <small class="text-danger" *ngIf="supplierForm.get('contactPerson')?.errors?.['required']">
              Contact Person is required.
            </small>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="designation">Contact Person Designation</label>
          <input type="text" id="designation" name="designation" formControlName="designation"
            [(ngModel)]="userTemp.contactPersonDesignation"/>
          <div *ngIf="
              supplierForm.get('designation')?.invalid &&
              (supplierForm.get('designation')?.dirty || supplierForm.get('designation')?.touched)
            ">
            <small class="text-danger" *ngIf="supplierForm.get('designation')?.errors?.['required']">
              Contact Person Designation is required.
            </small>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" [(ngModel)]="userTemp.userPassword" formControlName="password"/>
          <div
            *ngIf="supplierForm.get('password')?.invalid && (supplierForm.get('password')?.dirty || supplierForm.get('password')?.touched)">
            <small class="text-danger" *ngIf="supplierForm.get('password')?.errors?.['required']">Password is
              required.</small>
            <small class="text-danger" *ngIf="supplierForm.get('password')?.errors?.['minlength']">Password must be at
              least 8 characters long.</small><br>
            <small class="text-danger" *ngIf="supplierForm.get('password')?.errors?.['pattern']">Password must contain at
              least one uppercase letter, one lowercase letter, one digit, and one special character.</small>
          </div>
        </div>
        <div class="form-group">
          <label for="confirmPassword">Re-enter Password</label>
          <input type="password" id="rePassword" name="rePassword" formControlName="rePassword" />
          <div
            *ngIf="supplierForm.get('rePassword')?.invalid && (supplierForm.get('rePassword')?.dirty || supplierForm.get('rePassword')?.touched)">
            <small class="text-danger" *ngIf="supplierForm.get('rePassword')?.errors?.['required']">Please re-enter the
              password.</small>
          </div>
          <small class="text-danger"
            *ngIf="supplierForm.errors?.['mismatch'] && supplierForm.get('rePassword')?.dirty">Password do not
            match.</small>
        </div>
      </div>
      <button type="submit" #RegisterButton class="register-button">Register</button>
    </form>
  </div>
  <button class="backtoselection-button" (click)="navigateUserSelection()">Selection Menu</button>
</div>
