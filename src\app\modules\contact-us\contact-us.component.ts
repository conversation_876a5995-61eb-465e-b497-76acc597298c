import { Component } from '@angular/core';

@Component({
  selector: 'app-contact-us',
  templateUrl: './contact-us.component.html',
  styleUrls: ['./contact-us.component.css']
})
export class ContactUsComponent {
  firstname: string = '';
  lastname: string = '';
  email: string = '';
  phonenumber: string = '';
  message: string = '';

  // Function to handle form submission
  onSubmit() {
    if (this.firstname && this.lastname && this.email && this.message) {
      // Perform any actions like sending data to an API or service
      console.log('Form Submitted Successfully');
      console.log('First Name:', this.firstname);
      console.log('Last Name:', this.lastname);
      console.log('Email:', this.email);
      console.log('Phone Number:', this.phonenumber);
      console.log('Message:', this.message);

      // Reset form fields after submission (optional)
      this.firstname = '';
      this.lastname = '';
      this.email = '';
      this.phonenumber = '';
      this.message = '';
    } else {
      // Handle validation errors or show messages
      console.log('Please fill in all required fields.');
    }
  }

}
