import { Component } from '@angular/core';

interface OrderRequest {
  id: number;
  clinicname: string;
  date: string;
  time: string;
  address: string;
  status: string;
  contact: string;
}

@Component({
  selector: 'app-order-requests',
  templateUrl: './order-requests.component.html',
  styleUrls: ['./order-requests.component.css']
})
export class OrderRequestsComponent {
  closeModal() {
    this.showModal = false;
    this.selectedOrder = null;
  }

  orderRequests: OrderRequest[] = [
    { id: 1, contact: "0760061030", clinicname: 'MyDents', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 2, contact: "0760061030", clinicname: 'osro', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 3, contact: "0760061030", clinicname: 'Na<PERSON><PERSON>', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 4, contact: "0760061030", clinicname: '<PERSON><PERSON>', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 5, contact: "0760061030", clinicname: 'hospital', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 6, contact: "0760061030", clinicname: 'jayawardhana', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 1, contact: "0760061030", clinicname: 'MyDents', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 2, contact: "0760061030", clinicname: 'osro', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 3, contact: "0760061030", clinicname: 'Nawaloka', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 4, contact: "0760061030", clinicname: 'Asiri', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 5, contact: "0760061030", clinicname: 'hospital', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 6, contact: "0760061030", clinicname: 'jayawardhana', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 1, contact: "0760061030", clinicname: 'MyDents', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 2, contact: "0760061030", clinicname: 'osro', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 3, contact: "0760061030", clinicname: 'Nawaloka', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },
    { id: 4, contact: "0760061030", clinicname: 'Asiri', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 5, contact: "0760061030", clinicname: 'hospital', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Pending' },
    { id: 6, contact: "0760061030", clinicname: 'jayawardhana', date: '01-05-2024', time: '10:32 am', address: 'Medi lab pvt. ltd', status: 'Approved' },


  ];

  searchTerm: string = '';
  showModal: boolean = false;
  showAll: boolean = false;
  currentPage: number = 1;
  itemsPerPage: number = 8;
  selectedOrder: OrderRequest | null = null;
  toggleSidebar() {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
    }
  }
  get filteredItems() {
    return this.orderRequests.filter(orderRequest =>
      orderRequest.clinicname.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get paginatedItems() {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredItems.slice(start, end);
  }

  getPagesArray() {
    const totalItems = this.filteredItems.length;
    const pageCount = Math.ceil(totalItems / this.itemsPerPage);
    return Array.from({ length: pageCount }, (_, i) => i + 1);
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage -= 1;
    }
  }

  nextPage() {
    if ((this.currentPage * this.itemsPerPage) < this.filteredItems.length) {
      this.currentPage += 1;
    }
  }

  goToPage(page: number) {
    this.currentPage = page;
  }


  viewOrderDetails(id: number) {
    this.selectedOrder = this.orderRequests.find(order => order.id === id) || null;
    this.showModal = true;
  }
}
