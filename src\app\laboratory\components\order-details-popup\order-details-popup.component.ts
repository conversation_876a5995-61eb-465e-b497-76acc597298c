import { Component, Input, Output, EventEmitter } from '@angular/core';
import { LaboratoryClinicOrder } from '../../laboratory';
import { LaboratoryService } from '../../laboratory.service';

@Component({
  selector: 'app-order-details-popup',
  templateUrl: './order-details-popup.component.html',
  styleUrls: ['./order-details-popup.component.css'],
})
export class OrderDetailsPopupComponent {
  @Input() order: any;
  @Output() closePopup = new EventEmitter<void>();

  showAcceptPopup = false;
  showRejectPopup = false;
  selectedOrder: any = null;

  constructor(
    private laboratoryService: LaboratoryService,
  ) {}

  close() {
    this.closePopup.emit();
  }

  // openAcceptPopup(order: any) {
  //   this.selectedOrder = order;
  //   this.showAcceptPopup = true;
  // }

  closeAcceptPopup() {
    this.showAcceptPopup = false;
  }

  // Handle the order acceptance action
  // handleOrderAccepted(order: any) {
  //   this.laboratoryService.updateOrderStatus(order.laboratoryOrderId, 'Accepted').subscribe(
  //     (response) => {
  //       console.log('Order accepted:', response);
  //       this.closeAcceptPopup();
  //     },
  //     (error) => {
  //       console.log('Error accepting order:', error);
  //     }
  //   );
  // }

  acceptOrder(order: any) {
    this.selectedOrder = order;
    this.showAcceptPopup = true; 

    this.laboratoryService.updateOrderStatus(order.laboratoryOrderId, 'Accepted').subscribe(
      (response) => {
        console.log('Order accepted:', response);
        this.selectedOrder.status = 'Accepted';
      },
      (error) => {
        console.log('Error accepting order:', error);
      }
    );
  }

  // openRejectPopup() {
  //   this.showRejectPopup = true;
  // }

  closeRejectPopup() {
    this.showRejectPopup = false;
  }

  // Handle rejection confirmation
  // handleRejectConfirmed() {
  //   this.laboratoryService.updateOrderStatus(this.order.laboratoryOrderId, 'Rejected').subscribe(
  //     (response) => {
  //       console.log('Order rejected:', response);
  //       this.closeRejectPopup();
  //     },
  //     (error) => {
  //       console.log('Error rejecting order:', error);
  //     }
  //   );
  // }

  rejectOrder(order: any) {
    this.selectedOrder = order;
    this.showRejectPopup = true; 

    this.laboratoryService.updateOrderStatus(order.laboratoryOrderId, 'Rejected').subscribe(
      (response) => {
        console.log('Order rejected:', response);
        this.selectedOrder.status = 'Rejected';
      },
      (error) => {
        console.log('Error rejecting order:', error);
      }
    );
  }
}
