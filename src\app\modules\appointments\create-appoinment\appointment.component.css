/* Reset and global styles */

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  margin: 0;
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 30px;
}


/* Container styles */
.appointment-container {
  width: 60%;
  max-width: 1000px;
  margin: auto;
  background-color: #fff;
  border: 1px solid #ff6600;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  box-sizing: border-box;
}

.warnings{
  /* background-color: aquamarine; */
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 350px;
  font-size: 14px;
}
/* Make the select element look like an input field */
.custom-arrow select {
  width: 100%;
  height: 33px;
  padding: 5px;
  font-size: 14px;
  color: #495057;
  background-color: #fff;
  background-image: linear-gradient(45deg, transparent 50%, #ff7a00 50%),
    linear-gradient(135deg, #ff7a00 50%, transparent 50%);
  background-position: calc(100% - 20px) center, calc(100% - 15px) center;
  background-size: 5px 5px, 5px 5px;
  background-repeat: no-repeat;
  border: 1px solid #ced4da;
  border-radius: 4px;
  appearance: none;
  box-sizing: border-box;
}

.custom-arrow select:focus {
  outline: none;
  border-color: #ff7a00;
}

.custom-arrow select:invalid {
  color: #6c757d;
}


/* To ensure the select dropdown arrow is hidden */
select::-ms-expand {
  display: none;
}

/* Optional: add custom arrow if desired */
.custom-arrow {
  position: relative;
}



/* Header styles */
.header {
  height: 8%;
  background-color: #ff6600;
  color: #fff;
  padding: 1px;
  text-align: center;
  width: 100%;
  margin-bottom: 20px;
}

/* Content area styles */
.content {
  padding: 0px;
  width: 100%;
  max-width: 700px;
  box-sizing: border-box;
  flex-grow: 1;
}

/* Navigation bar styles */
.navigation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
  position: relative;
  margin-top: 20px;
  background-color: #fff3e3;
  padding-block: 16px;
  border-radius: 10px;
}

/* Navigation step styles */
.navigation-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;
}

.navigation-step .dot {
  width: 15px;
  height: 15px;
  background-color: #ffffff;
  border: 1px;
  border-style: solid;
  border-color: #333;
  border-radius: 50%;
  margin-bottom: 5px;
  transition: background-color 0.3s;
}

.navigation-step.active .dot {
  background-color: #ff6600;
  border-color: #ff6600;
}

.navigation-step.active span {
  color: #ff6600;
}

/* Table styles */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table td {
  padding: 10px;
  border: 1px solid #ddd;
  cursor: pointer;
}

.table .selected-clinic {
  background-color: #d4edda;
}

/* Primary text color */
.text-primary {
  color: #ff6600;
  cursor: pointer;
}

.text-end {
  text-align: end;
}

/* Form container and form row styles */
.form-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 2px;
}

/* Form group styles */
.form-group {
  width: 40%;
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #333;
  font-weight: 600;
}

.btn-field {
  margin-top: 0%;
}

.first-next {
  margin-left: 80%;
  width: 20%;
}

.second-next {
  width: 50%;
}

/* Form buttons styles */
.form-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.btn {
  cursor: pointer;
  font-size: 16px;
  border: none;
  border-radius: 5px;
}

/* Button styles */
.btn-secondary {
  background: transparent;
  color: #ff6600;
  border: 2px solid #ff6600;
  border-radius: 25px;
}

.btn-primary {
  background: linear-gradient(to right, #FB751E, #5f1108);
  color: #fff;
  border: none;
  border-radius: 25px;
}

.header1 {
  text-align: center;
  font-weight: 700;
}

/* Responsive styles */
@media (max-width: 768px) {
  .form-group {
    width: 80%;
  }

  .appointment-container {
    width: 100%;
    max-width: 1000px;
    margin: auto;
    background-color: #fff;
    border: 1px solid #ff6600;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 1050px;
    box-sizing: border-box;
  }
}

@media (min-width: 768px) {
  .form-group {
    width: 80%;
  }

  .appointment-container {
    width: 90%;
    max-width: 1000px;
    margin: auto;
    background-color: #fff;
    border: 1px solid #ff6600;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 900px;
    box-sizing: border-box;

  }

}

@media (min-width: 1024px) {
  .form-group {
    width: 80%;
  }

  .appointment-container {
    width: 60%;
    max-width: 1000px;
    margin: auto;
    background-color: #fff;
    border: 1px solid #ff6600;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 850px;
    box-sizing: border-box;
    /* margin-bottom: 100px; */
  }

}