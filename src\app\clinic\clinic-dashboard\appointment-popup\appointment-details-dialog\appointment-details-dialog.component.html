<div style="padding: 20px; min-width: 400px;">
    <h4 mat-dialog-title style="font-weight: 700;">Appointment Request</h4>
    <br/>
    <div mat-dialog-content style="display: flex; gap: 30px;">
        <div style="width: 290px;">
            <p><strong>Name</strong></p>
            <p><strong>Telephone</strong></p>
            <p><strong>Requested Date</strong></p>
            <p><strong>Requested Time</strong></p>
            <p><strong>Address</strong></p>
        </div>
        <div style="display: flex; flex-direction: column;">
            <p>{{ data.firstName }} {{ data.lastName }}</p>
            <p>{{ data.telephone }}</p>
            <p> {{ data.fromDate }}</p>
            <p>{{ data.fromTime }} - {{ data.toTime }}
            <p>{{ data.address }}</p>
        </div>
    
    </div>
    <div mat-dialog-actions align="end" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
        <button mat-button style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;" (click)="accept()">Accept</button>
        <button mat-button  style="width: 100px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;" (click)="reject() ">Reject</button>
        <button mat-button (click)="close() " style="width: 100px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;">Close</button>
    </div>
</div>
