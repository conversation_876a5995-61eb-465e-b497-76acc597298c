import { Component, ViewChild } from '@angular/core';

interface  PurchasingDashboard{
    orderID: string;
    date: string;
    time: string;
    supplier: string;
    status: string;

}
@Component({
  selector: 'app-purchasing-dashboard',
  templateUrl: './purchasing-dashboard.component.html',
  styleUrls: ['./purchasing-dashboard.component.css']
})
export class PurchasingDashboardComponent {


  toggleSidebar() {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
    }
  }

  purchasingDashboard: PurchasingDashboard[] = [
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', supplier: 'Medi lab pvt. ltd',  status: 'Pending' },
        // { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', supplier: 'Medi lab pvt. ltd',  status: 'Received' },
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', supplier: 'Medi lab pvt. ltd',  status: 'Completed' },
        // Add more dummy data as needed
    ];

    searchTerm: string = '';
    // showModal: boolean = false;
    newPurchasingDashboard: PurchasingDashboard = { orderID: '',date: '', time: '', supplier: '',  status: 'Pending' };


    filteredPurchasingDashboard() {
         return this.purchasingDashboard.filter(purchasingDashboard =>
             purchasingDashboard.orderID.toLowerCase().includes(this.searchTerm.toLowerCase())
         );
    }


   showAll = false;


}



