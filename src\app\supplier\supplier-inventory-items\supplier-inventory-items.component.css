.header-row,
.send-orders-list-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.add-in-items {
    width: 240px;
    height: 29px;
    top: 243px;
    left: 383px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 24px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: left;
    color: #000;
}

.header-row-h1 {
    color: black;
    width: 300px;
    top: 150px;
    font-size: 24px;
    left: 386px;
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
}

.rectangle {
    width: 1262px;
    height: auto;
    top: 296px;
    left: 686px;
    gap: 0px;
    border-radius: 10px;
    opacity: 0px;
    background: #FFFCFC;
    box-shadow: 0px 12px 12.8px 0px #00000040;
    align-items: center;
    padding: 10px 10px;
}


/* .profile-icon {
    color: #FF5722;
    margin-right: 8px;
    vertical-align: middle;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    text-align: right;
}

.close-btn:hover,
.close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
} */

form {
    display: flex;
    flex-direction: column;
}

form label {
    margin-top: 10px;
}

/* form input,
form select,
form textarea {
    padding-left: 30px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 5px;
    border-width: 30%;
} */

#text-p {
    width: 107px;
    height: 29px;
    top: 331px;
    left: 413px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 24px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: left;
    color: #000000;
    padding-left: 50px;
}

#text-i {
    width: 84px;
    height: 29px;
    top: 331px;
    left: 919px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 24px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: left;
    color: #000000;
    padding-top: 50px;
}



#description-p,
#price-p {
    width: 132px;
    height: 29px;
    top: 573px;
    left: 415px;
    font-family: Inter;
    font-size: 24px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: left;
    padding-top: 30px;
}

#description {
    width: 200px;
    /* Increase the width to fit the text */
    height: 19px;
    top: 633px;
    left: 415px;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    line-height: 19.36px;
    text-align: left;
    padding-left: 50px;
}

#inventory-p {
    width: 109px;
    height: 29px;
    top: 764px;
    left: 415px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 24px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: left;
}

#inventory-l {
    width: 66px;
    height: 19px;
    top: 818px;
    left: 415px;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    line-height: 19.36px;
    text-align: left;
    padding-left: 50px;
    /* Remove padding-left */
    color: #000000;
}

#title {
    width: 412px;
    height: 37px;
    padding-left: 30px;
    top: 415px;
    left: 715px;
    border-radius: 5px 5px 5px 5px;
    border: 1px 0px 0px 0px;
    border: 1px solid #000000;
    margin-left: 50px;
}

#title-1 {
    width: 412px;
    height: 37px;
    top: 415px;
    left: 715px;
    border-radius: 5px 5px 5px 5px;
    border: 1px 0px 0px 0px;
    border: 1px solid #000000;
    margin-left: 50px;
}

#cate-gory123 {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    /* Align images to the start with minimal gap */
    align-items: center;
    padding-left: 50px;
    padding-top: 30px;
    /* Remove padding to minimize gap */
}

.row-md-4 {
    margin-right: 15px;
    /* Minimal space between images */
}

#custom-image-input {
    width: 100px;
    height: auto;
}

#schedule {
    width: 167px;
    height: 35px;
    border-radius: 18px;
    border: 1px solid #FB751E;
    margin-left: 800px;
    margin-top: 20px;
    color: #FB751E;
    background-color: #FFFFFF;
    font-weight: bolder;
    /* Pushes the element to the right */
}

#schedule:hover {
    background-color: #FB751E;
    color: #FFFFFF;
}

#addProduct {
    width: 167px;
    height: 35px;
    border-radius: 18px;
    margin-left: 650px;
    margin-top: 20px;
    color: #FFFFFF;
    border: 1px solid #FB751E;
    background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);
}

#addProduct:hover {
    background: linear-gradient(266.16deg, #FB751E 0.91%, #B93426 60.88%);
    color: #FFFFFF;
}

.sidebar-container {
    height: calc(100vh - 80px);
    /* Adjust the height to account for the header */
    width: 20%;

    display: inline-block;
}

.main-content {
    height: 100%;
    /* Adjust the height to account for the header */
    width: 100%;

    padding: 16px;
    display: inline-block;
}

.icon-square {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 134px;
    height: 131px;
    top: 391px;
    left: 1224px;
    gap: 0px;
    border-radius: 3px;
    border: 1px;
    opacity: 0px;
    background: #FFFFFF;
    border: 1px solid #000000
}

.icon-square i {
    width: 54px;
    height: 53px;
    top: 434px;
    left: 1250px;
    padding: 8.83px 9px 8.83px 9px;
    gap: 0px;
    opacity: 0px;
    color: #a40a0a;
}

#custom-image-input {
    width: 134px;
    height: 131px;
    top: 391px;
    left: 919px;
    gap: 0px;
    border-radius: 5px;
    border: 1px;
    opacity: 0px;
}

.custom-select{
  border: 1px solid rgb(230,230,230);
  border-radius: 5px;
  height: 45px;
  background-color: transparent;
  font-size: 14px;
  outline: none;
}

.product-image{
  border-radius: 10px;
}

.product-image-container{
  max-height:250px;
  overflow-y: auto;
  min-height: 228px;
}

@media screen and (min-width: 1700px) {
  .your-component {
    max-height: 350px;
  }
}
