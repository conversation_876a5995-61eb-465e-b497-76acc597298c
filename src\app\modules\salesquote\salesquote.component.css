.container {
  border: 1px solid #c7c6c6;
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
  background-color: white;
  border-radius: 2%;
  box-shadow: rgb(177, 177, 177) 0px 0px 10px;
}

.headSection{


  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;


}

.headtitle{
  font-weight: bold;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 16px;
  background-color: white;
 
}

.buttons {
  margin-left: 60%;
 
}

.header h2 {
  margin: 0;
}

.header .buttons {
  display: flex;
  gap: 10px;
}

.header .buttons button {
  border: 1px solid orange;
  background-color: transparent;
  color: orange;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s, color 0.3s;

}

.header .buttons button:hover {
  background-color: orange;
  color: white;
}

.details{
  display: flex;
}

.details p, .order-number p {
  margin: 4px 0;
}

.form-group {
  margin: 16px 0;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 16px;
}

table th, table td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}

.remarks {
  margin-bottom: 16px;
  
}

.totals {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 16px;
}

.totals div {
  flex: 1;
  margin-right: 16px;
  
}

.totals div:last-child {
  margin-right: 0;
}

.send-button {
  background-image: linear-gradient(to right, rgb(255, 42, 0) , rgb(255, 153, 0));
  color: white;
  padding: 5px;
  border: none;
  cursor: pointer;
  margin-left: 79%;
  border-radius: 50px;
  width: 150px;
  margin-top: 20px;
  border: none;
  text-decoration: none;
}

#remarks {
  width: 400px;
  height: 100px;
}

.inputs{
  border-radius: 5px;
  border: none;
  box-shadow: rgb(201, 200, 200) 0px 0px 10px;
  padding: 5px;
  
}

#purchaseRequest{
  margin-top: 5px;
  width: 30%;
  padding: 5px;
  border-radius: 5px;
  border-color: rgb(159, 159, 159);
  box-shadow: rgb(201, 200, 200) 0px 0px 10px;
}