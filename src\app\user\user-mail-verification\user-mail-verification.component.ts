import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../user.service';

@Component({
  selector: 'app-user-mail-verification',
  templateUrl: './user-mail-verification.component.html',
  styleUrls: ['./user-mail-verification.component.css'],
})
export class UserMailVerificationComponent implements OnInit {
  loading = true; 
  message = '';
  userType = '';

  constructor(
    private route: ActivatedRoute,
    private userService: UserService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      const verificationToken = params['token'];
      this.userType = params['userType'];

      if (!verificationToken) {
        this.message = 'No verification token provided.';
        this.loading = false;
        return;
      }

      this.verifyEmail(verificationToken);
    });
  }

  verifyEmail(token: string): void {
    this.userService.verifyEmail(token).subscribe(
      (response: any) => { 
        if (response && typeof response === 'object') {
          this.message = response.message || 'No message provided.'; 
        } else {
          this.message = response; 
        }
        this.loading = false;
      },
      (error) => {
        this.message = error.error && error.error.message
          ? error.error.message
          : 'An error occurred. Please try again.';
        this.loading = false;
      }
    );
  }

  goToLogin(): void {
    this.router.navigate(['/user-login']);
  }
}
