import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../user.service';

@Component({
  selector: 'app-user-mail-verification',
  templateUrl: './user-mail-verification.component.html',
  styleUrls: ['./user-mail-verification.component.css'],
})
export class UserMailVerificationComponent implements OnInit {
  loading = true; 
  message = '';
  userType = '';

  constructor(
    private route: ActivatedRoute,
    private userService: UserService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    console.log('UserMailVerificationComponent initialized');
    this.route.queryParams.subscribe((params) => {
      console.log('Query params received:', params);
      const verificationToken = params['token'];
      this.userType = params['userType'] || 'User';

      if (!verificationToken) {
        console.error('No verification token provided in query params');
        this.message = 'No verification token provided. Please check your email link.';
        this.loading = false;
        return;
      }

      console.log('Starting email verification with token:', verificationToken);
      this.verifyEmail(verificationToken);
    });
  }

  verifyEmail(token: string): void {
    console.log('Calling verifyEmail service with token:', token);
    this.userService.verifyEmail(token).subscribe(
      (response: any) => {
        console.log('Email verification response:', response);
        if (response && typeof response === 'object') {
          this.message = response.message || 'Email verification completed.';
        } else {
          this.message = response || 'Email verification completed.';
        }
        this.loading = false;
        console.log('Verification completed. Message:', this.message);
      },
      (error) => {
        console.error('Email verification error:', error);
        this.message = error.error && error.error.message
          ? error.error.message
          : 'An error occurred during email verification. Please try again or contact support.';
        this.loading = false;
        console.log('Verification failed. Message:', this.message);
      }
    );
  }

  goToLogin(): void {
    this.router.navigate(['/user-login']);
  }
}
