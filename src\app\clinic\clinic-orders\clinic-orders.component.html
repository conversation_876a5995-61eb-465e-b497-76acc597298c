<div class="row">
  <div class="col-12">
    <div class="send-orders-container">
      <div class="header-row">
        <div class="header-row-h1">Orders</div>

        <div class="search-bar">
          <i class="bi bi-search search-icon"></i>
          <input
            type="text"
            placeholder="Search Order"
            [(ngModel)]="searchTerm"
            id="search-input"
          />
        </div>
      </div>
      <div class="header-bottom-line"></div>
      <br />
      <div>
        <div
          [ngStyle]="{
            display: 'flex',
            'flex-direction': 'row',
            width: '100%',
            'justify-content': 'flex-end',
            'margin-bottom': '20px'
          }"
        >
          <button
            [ngStyle]="{
              width: '250px',
              'border-radius': '40px',
              background: 'linear-gradient(to right, #FB751E, #B93426)',
              color: 'white',
              border: 'none',
              padding: '10px 20px'
            }"
          >
            Send Invoice
          </button>
        </div>

        <div>
          <div>
            <button (click)="showAll = !showAll" class="see-all-button">
              See All>
            </button>
          </div>
        </div>
        <table class="send-order-table">
          <thead>
            <tr>
              <th width="10%">OrderID</th>
              <th width="15%">Order Date</th>
              <th width="15%">Expected Date</th>
              <th width="15%">Clinic</th>
              <th width="15%">Value</th>
              <th width="30%">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let orders of filteredorders()">
              <td><input type="checkbox" /> {{ orders.orderID }}</td>
              <td>{{ orders.date }}</td>
              <td>{{ orders.exDate }}</td>
              <td>{{ orders.laboratory }}</td>
              <td>{{ orders.value }}</td>
              <td class="status-cell">
                <button
                  [ngClass]="{
                    'status pending': orders.status === 'Pending',
                    'status completed': orders.status === 'Completed',
                    'status progress': orders.status === 'Progress',
                    'status cancelled': orders.status === 'Cancelled'
                  }"
                >
                  {{ orders.status }}
                </button>

                <button
                  style="
                    border: 2px solid red;
                    color: red;
                    border-radius: 20px;
                    margin-left: 20px;
                  "
                  (click)="openPopup(orders)"
                >
                  View Details
                </button>

                <img src="assets/images/3dot.png" class="dot" />
              </td>
            </tr>
          </tbody>
        </table>
        <!-- Popup Component -->
        <app-order-details-popup
          *ngIf="showPopup"
          [order]="selectedOrder"
          (closePopup)="closePopup()"
        >
        </app-order-details-popup>

        <div class="pagination-container">
          <ul class="pagination">
            <li
              class="page-item"
              [class.disabled]="currentPage === 1"
              (click)="goToPage(currentPage - 1)"
            >
              <a class="page-link" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            <li
              class="page-item"
              *ngFor="let page of visiblePages"
              [class.active]="page === currentPage"
              (click)="goToPage(page)"
            >
              <a class="page-link">{{ page }}</a>
            </li>
            <li class="page-item" (click)="goToPage(currentPage + 1)">
              <a class="page-link" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
