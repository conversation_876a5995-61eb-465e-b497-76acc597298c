import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AdminRoutingModule } from './admin-routing.module';
import { AdminLayoutComponent } from './admin-layout/admin-layout.component';
import { AdminDashboardComponent } from './admin-dashboard/admin-dashboard.component';
import { AdminUserManagementComponent } from './admin-user-management/admin-user-management.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CoreModule } from "../core/core.module";
import { UserManagementModalComponent } from './components/user-management-modal/user-management-modal.component';
import { AdminSideBarComponent } from './components/admin-side-bar/admin-side-bar.component';
import { AdminNavBarComponent } from './components/admin-nav-bar/admin-nav-bar.component';


@NgModule({
  declarations: [
    AdminLayoutComponent,
    AdminDashboardComponent,
    AdminUserManagementComponent,
    UserManagementModalComponent,
    AdminSideBarComponent,
    AdminNavBarComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    AdminRoutingModule,
    CoreModule,
    ReactiveFormsModule
]
})
export class AdminModule { }
