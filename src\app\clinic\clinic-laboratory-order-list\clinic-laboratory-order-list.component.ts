import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ClinicLaboratoryOrder } from '../clinic';
import { ClinicService } from '../clinic.service';

@Component({
  selector: 'app-clinic-laboratory-order-list',
  templateUrl: './clinic-laboratory-order-list.component.html',
  styleUrls: ['./clinic-laboratory-order-list.component.css']
})
export class ClinicLaboratoryOrderListComponent implements OnInit {
  searchTerm: string = '';
  currentPage: number = 1;
  itemsPerPage: number = 8;
  clinicLaboratoryOrders: ClinicLaboratoryOrder[] = [];
  visiblePages: number[] = [];
  paginatedClinicLaboratoryOrders: ClinicLaboratoryOrder[] = [];
  totalPages: number = 0;
  showAll: boolean = false;

  constructor(private clinicService: ClinicService, private router: Router) {}

  ngOnInit(): void {
    const userIdString = localStorage.getItem('userid');
    const userId = userIdString ? parseInt(userIdString, 10) : null;
    if (userId !== null) {
      this.getLaboratoryOrderByClinicId(userId);
    } else {
      console.error('User ID is not available in localStorage.');
    }
  }

  getLaboratoryOrderByClinicId(userId: number): void {
    this.clinicService.getLaboratoryOrderByClinicId(userId).subscribe(
      (data: ClinicLaboratoryOrder[]) => {
        this.clinicLaboratoryOrders = data;
        this.updatePagination();
        this.updatePaginatedItems();
      },
      (error) => {
        console.error('Error fetching clinic laboratory orders:', error);
      }
    );
  }

 get filteredClinicLaboratoryOrders(): ClinicLaboratoryOrder[] {
    return this.clinicLaboratoryOrders.filter(order => {
      const labNameMatches =
        order.laboratorySetupId &&
        order.laboratorySetupId.laboratoryId &&
        order.laboratorySetupId.laboratoryId.name &&
        order.laboratorySetupId.laboratoryId.name
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase());

      const labNumberMatches = order.laboratoryOrderId.toString().includes(this.searchTerm);
      return labNameMatches || labNumberMatches;
    });
  }

  updatePagination(): void {
    this.totalPages = Math.ceil(this.filteredClinicLaboratoryOrders.length / this.itemsPerPage);
    this.visiblePages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  updatePaginatedItems(): void {
    if (this.showAll) {
      this.paginatedClinicLaboratoryOrders = this.filteredClinicLaboratoryOrders;
    } else {
      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;
      this.paginatedClinicLaboratoryOrders = this.filteredClinicLaboratoryOrders.slice(startIndex, endIndex);
    }
  }

  goToPage(page: number): void {
    if (page > 0 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePaginatedItems();
    }
  }

  navigateAddClinicLaboratoryOrder(): void {
    this.router.navigate(['/']);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';

    const dateParts = dateString.split('-');
    if (dateParts.length !== 3) {
      console.error('Invalid date format:', dateString);
      return '';
    }
    return `${dateParts[2]}-${dateParts[0]}-${dateParts[1]}`;
  }

  goToOrderCreatePage(){
    this.router.navigate(["clinic/laboratory-orders/new-order"])
  }
}
