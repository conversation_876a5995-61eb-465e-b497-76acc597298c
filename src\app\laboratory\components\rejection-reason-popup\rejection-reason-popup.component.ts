import { Component, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-rejection-reason-popup',
  templateUrl: './rejection-reason-popup.component.html',
  styleUrls: ['./rejection-reason-popup.component.css']
})
export class RejectionReasonPopupComponent {
  showReasonPopup: boolean = false; // To toggle the RejectionReasonPopupComponent visibility

  // Called when the "Yes" button is clicked
  onOpenReasonPopup() {
    this.showReasonPopup = true; // Display the RejectionReasonPopupComponent
  }

  // Called when the RejectionReasonPopupComponent is closed
  handlePopupClose(reason: string) {
    this.showReasonPopup = false; // Hide the RejectionReasonPopupComponent
    if (reason) {
      // Handle the rejection reason (you can send it to the backend or process it)
      console.log('Rejection reason submitted:', reason);
    }
  }

  // Close the current popup (reject-popup)
  onClose() {
    // You can implement the logic to close the reject-popup here
    console.log('Reject popup closed');
  }
}
