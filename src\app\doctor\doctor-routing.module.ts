import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DoctorLayoutComponent } from './doctor-layout/doctor-layout.component';
import { DoctorDashboardComponent } from './doctor-dashboard/doctor-dashboard.component';
import { DoctorAssignedClinicListComponent } from './doctor-assigned-clinic-list/doctor-assigned-clinic-list.component';
import { DoctorAppointmentListComponent } from './doctor-appointment-list/doctor-appointment-list.component';
import { authGuard } from '../auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: DoctorLayoutComponent,
    canActivateChild:[authGuard],
    children: [
      { path: '', component: DoctorDashboardComponent },
      { path: 'dashboard', component: DoctorDashboardComponent },
      { path: 'assigned-clinics', component: DoctorAssignedClinicListComponent },
      { path: 'appointments', component: DoctorAppointmentListComponent },
      { path: '**', redirectTo: '' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DoctorRoutingModule {}
