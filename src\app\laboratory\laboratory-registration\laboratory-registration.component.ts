import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { User, UserCategory } from '../../user/user';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserService } from '../../user/user.service';
import { map, mapTo, Observable, of, tap } from 'rxjs';
import { SharedService } from '../../modules/shared-services/shared.service';
import { Laboratory } from '../laboratory';
import { LaboratoryService } from '../laboratory.service';
import Swal from 'sweetalert2';
import { UserTemp } from 'src/app/auth/auth';
import { AuthService } from 'src/app/auth/auth.service';

@Component({
  selector: 'app-laboratory-registration',
  templateUrl: './laboratory-registration.component.html',
  styleUrls: ['./laboratory-registration.component.css'],
})
export class LaboratoryRegistrationComponent implements OnInit {
  laboratory: Laboratory = new Laboratory();
  user: User = new User();
  userCategory: UserCategory = new UserCategory();
  laboratoryForm: FormGroup;
  isEmailRegistered: boolean = false;
  isLaboratoryRegistered: boolean = false;
  userEmailExistsMessage: string = '';
  laboratoryNameExistsMessage: string = '';

  districts: string[] = [];
  cities: String[] = [];

  // User temp
  protected userTemp: UserTemp = new UserTemp();
  @ViewChild('RegisterButton') registerButton!: ElementRef<HTMLButtonElement>;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private userService: UserService,
    private authService: AuthService,
    private laboratoryService: LaboratoryService,
    private sharedService: SharedService
  ) {
    this.laboratoryForm = this.fb.group(
      {
        laboratoryName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],
        ],
        address: ['', Validators.required],
        // city: ['', Validators.required],
        // district: ['', Validators.required],
        tele: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
        contactPerson: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            Validators.pattern('^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\W).*$'),
          ],
        ],
        confirmPassword: ['', Validators.required],
      },
      { validator: this.passwordMatchValidator }
    );
  }

  ngOnInit(): void {
    localStorage.clear();
    this.userService.getUserCategoryById(6).subscribe((response) => {
      this.userCategory = response;
    });
    this.districts = this.sharedService.getDistricts();
  }

  // onDistrictChange(event: Event): void {
  //   const selectedDistrict = (event.target as HTMLSelectElement).value;

  //   if (selectedDistrict) {
  //     this.laboratoryForm.get('city')?.enable();
  //     this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);
  //   } else {
  //     this.laboratoryForm.get('city')?.disable();
  //     this.cities = [];
  //   }
  //   this.laboratoryForm.get('city')?.setValue('');
  // }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    // Only apply mismatch validation if both fields are touched or dirty
    if (
      password?.value &&
      confirmPassword?.value &&
      (confirmPassword.dirty || confirmPassword.touched)
    ) {
      return password.value === confirmPassword.value
        ? null
        : { mismatch: true };
    }

    // If the fields are not touched or dirty, return null (no error)
    return null;
  }

  updateEmail() {
    this.user.username = this.laboratory.email;
  }

  onUserRegister(): Observable<void> {
    this.user.userCategoryId = this.userCategory;
    this.user.firstName = this.laboratory.name;
    return this.userService.register(this.user).pipe(
      tap(
        (response) => {
          this.user.userId = response.id;
          this.laboratory.userId = this.user;
        },
        (error) => {
          console.log(error);
        }
      ),
      mapTo(void 0)
    );
  }

  onLaboratoryRegister(): Observable<void> {
    return this.laboratoryService.saveLaboratory(this.laboratory).pipe(
      tap(
        () => {
          this.router.navigate(['/user-login']);
        },
        (error) => {
          console.log(error);
        }
      ),
      mapTo(void 0)
    );
  }



  // UserTemp Saving
  onUserTempRegister() {
    if (this.laboratoryForm.invalid) {
      this.laboratoryForm.markAllAsTouched();
      return;
    }

    // Disable the register button and show a loading indicator
    this.registerButton.nativeElement.disabled = true;
    this.registerButton.nativeElement.innerHTML = `<img src="/assets/icons/more-30.png" />`;

    this.authService
      .checkUserTempAvailability(this.userTemp.userEmail)
      .subscribe((resp) => {

        if (resp !=null) {
          Swal.fire({
            title: 'Registration Already Exists!',
            text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',
            icon: 'info',
            confirmButtonText: 'OK',
          });

          // Reset the button state
          this.registerButton.nativeElement.disabled = false;
          this.registerButton.nativeElement.innerHTML = 'Register';
          return;
        }

        this.authService.saveUserTemp(this.userTemp).subscribe(
          (userTempSaved: UserTemp) => {
            console.log('Full userTempSaved object:', userTempSaved);

            const receivedUserTemp: UserTemp = userTempSaved;
            let title = 'Registration Completed!';
            let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';
            let iconName:
              | 'success'
              | 'info'
              | 'error'
              | 'warning'
              | 'question' = 'success';

            if (!receivedUserTemp) {
              title = 'Registration Failed!';
              message ='An error occurred while registering. Please try again.';
              iconName = 'error';
            }

            Swal.fire({
              title: title,
              text: message,
              icon: iconName,
              confirmButtonText: 'OK',
            });

            // Reset button state
            this.registerButton.nativeElement.disabled = false;
            this.registerButton.nativeElement.innerHTML = 'Register';
          },
          (error) => {
            Swal.fire({
              title: 'Registration Failed!',
              text: 'An error occurred during registration. Please try again later.',
              icon: 'error',
              confirmButtonText: 'OK',
            });

            this.registerButton.nativeElement.disabled = false;
            this.registerButton.nativeElement.innerHTML = 'Register';
          }
        );
      });
  }


  onSubmitRegister() {

    Swal.fire({
      title: "Wait until approval!",
      text: "Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.",
      icon: 'success',
      confirmButtonText: 'OK',
    });

    if (this.laboratoryForm.invalid) {
      this.laboratoryForm.markAllAsTouched();
      return;
    }
    this.checkLaboratoryName().subscribe((isLaboratoryRegistered) => {
      if (!isLaboratoryRegistered) {
        this.checkUserEmail().subscribe((isEmailRegistered) => {
          if (!isEmailRegistered) {
            this.onUserRegister().subscribe(() => {
              this.onLaboratoryRegister().subscribe(() => {
                // SweetAlert for successful registration
                Swal.fire({
                  icon: 'success',
                  title: 'Registered Successfully!',
                  text: 'Thank you for registering! Please verify your email to complete the login process.',
                  confirmButtonText: 'OK',
                }).then(() => {
                  // Redirect after confirmation
                  this.router.navigate(['/user-login']);
                });
              });
            });
          } else {
            Swal.fire({
              icon: 'error',
              title: 'Email Already Registered',
              text: this.userEmailExistsMessage,
              confirmButtonText: 'OK',
            });
          }
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Laboratory Name Already Taken',
          text: this.laboratoryNameExistsMessage,
          confirmButtonText: 'OK',
        });
      }
    });
  }

  checkUserEmail(): Observable<boolean> {
    if (this.laboratoryForm.get('email')?.valid) {
      const userEmail = this.laboratoryForm.get('email')?.value;
      return this.userService.checkUser(userEmail).pipe(
        map((data) => {
          if (data) {
            this.isEmailRegistered = true;
            this.userEmailExistsMessage =
              'Email already registered. Try another.';
          } else {
            this.isEmailRegistered = false;
            this.userEmailExistsMessage = '';
          }
          return this.isEmailRegistered;
        })
      );
    } else {
      this.isEmailRegistered = false;
      this.userEmailExistsMessage = '';
      return of(this.isEmailRegistered);
    }
  }

  checkLaboratoryName(): Observable<boolean> {
    if (this.laboratoryForm.get('laboratoryName')?.valid) {
      const laboratoryName = this.laboratoryForm.get('laboratoryName')?.value;
      return this.laboratoryService.checkLaboratoryName(laboratoryName).pipe(
        map((data) => {
          if (data) {
            this.isLaboratoryRegistered = true;
            this.laboratoryNameExistsMessage =
              'That name is taken. Try another.';
          } else {
            this.isLaboratoryRegistered = false;
            this.laboratoryNameExistsMessage = '';
          }
          return this.isLaboratoryRegistered;
        })
      );
    } else {
      this.isLaboratoryRegistered = false;
      this.laboratoryNameExistsMessage = '';
      return of(this.isLaboratoryRegistered);
    }
  }

  navigateUserSelection() {
    this.router.navigate(['/user-selection']);
  }
}
