/* General Styling */

body {
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    color: #333;
 
}

.border-orange {
    border: 2px solid orange !important;
}


/* Container and Rows */


.row {
    margin-bottom: 20px;
}

#set-date-time {
    border: 2px solid #********;
    border-radius: 30px;
    padding: 20px;
    background-color: #fff;
}

#date-time-container {
    border: 2px solid #********;
    border-radius: 30px;
    padding: 20px;
    background-color: #fff;
    width: 90%;
    flex-direction: column;
    
}

.first-row-right{
    margin-bottom: 30px;
    justify-content: center;
    width: 50%;
}

.first-row-left{
    margin-bottom: 30px;
    padding-left: 20px;
    width: 50%;
}

#open-hours {
    
    background-color: #fff;
}

#from-hours {
    background-color: #fff;
    border: 2px solid #807a78;
    border-radius: 5px;
    padding: 5px;
    display: inline-block;
    margin-top: 10px;
}

#to-hours {
    background-color: #fff;
    border: 2px solid #807a78;
    border-radius: 5px;
    padding: 5px;
    display: inline-block;
    margin-top: 10px;
}

.dropdown {
    padding: 5px;
    display: inline-block;
    margin-top: 10px;
}

#save-btn {
    width: 121px;
    height: 35px;
    font-size: 16px;
    color: white;
    background: linear-gradient(to right, #B93426, #FB751E);
    border: none;
    border-radius: 40px;
    margin-top: 15px;
    float: right;
}

#save-btn-service {
    width: 121px;
    height: 35px;
    font-size: 16px;
    color: white;
    background: linear-gradient(to right, #B93426, #FB751E);
    border: none;
    border-radius: 40px;
    margin-top: 15px;
    
}
/* #save-btn:hover {
    background: linear-gradient(to right, #FF512F, #F09819);
    color: white;
} */

.set-service {
    margin-right: 100px;
}


/* #bi-bi-pencil-square {
    font-size: 15px;
    background: linear-gradient(to right, #FF512F, #F09819);
    border: none;
    border-radius: 40px;
    color: rgb(104, 3, 3);
    margin-top: 15px;
    float: right;
} */

#make-appointment-btn {
    font-size: 18px;
    background: linear-gradient(to right, #FF512F, #F09819);
    border: none;
    border-radius: 40px;
    color: white;
    padding: 10px 20px;
}

#make-appointment-btn:hover {
    background: linear-gradient(to right, #FF512F, #F09819);
    color: white;
}

.day-col{
    display: flex;
    flex-direction: row;
    max-width: 595px;
    overflow-x: auto;
}

.day-col button{
    display: flex;
    flex-direction: row;
    white-space: nowrap;
}

.time{
    width: 100%;
    height: 40px;
    border-radius: 10px;
}

#dropdownMenuButton1 {
    color: #000;
    background-color: #fff;
    border: none;
}

#day-btn {
    width: 59.57px;
    height: 58.66px;
    font-size: 20px;
    background: #fff;
    color: #D85322;
    border: 1px solid #D85322;
    border-radius: 10px;
}

#day-btn:hover {
    background: #FB751E;
    color: white;
    border: none;
}
#day-btn.active {
    background: #FB751E;  /* Change background when pressed */
    color: white;  /* Change text color when pressed */
    border: none;  /* Remove border when pressed */
}

#markAsHolidayLink {
    color: #FB751E;
    font-style: italic;
}

.checkbox {
    text-align: right;
}

#add-Doctors-btn {
    display: flex;
    align-items: center;
    width: 232px;
    height: 54px;
    font-size: 16px;
    background: linear-gradient(to right, #FB751E, #B93426);
    border: none;
    border-radius: 40px;
    color: white;
    /* padding: 10px 20px; */
}

.icon {
    font-size: 40px;
    margin-right: 40px;
}

/* #add-Doctors-btn:hover {
    background: linear-gradient(to right, #FF512F, #F09819);
    color: white;
} */

#text {
    font-size: 20px;
    font-weight: bold;
}

.sidebar-container {
    height: calc(100vh - 80px);
    /* Adjust the height to account for the header */
    width: 20%;
    overflow-y: auto;
    display: inline-block;
}

.main-content {
    height: calc(100vh - 80px);
    /* Adjust the height to account for the header */

    overflow-y: auto;
    padding: 16px;
    display: inline-block;
}


.col-5{
    margin-top: 80%;
}
.row-mb-3{
    margin-top: 2%;
}


@media (max-width: 768px) {
    .sidebar-container,
    .main-content {
        width: 100%;
        display: block;
    }
}
.rounded-container {
    background-color: #fff;
    border-radius: 20px;
    padding: 20px;
    border: 2px solid #********;
  
}

.text-dark {
    color: #000;
    font-weight: bold;
    font-size: 1.25rem;
}



.btn-custom {

    color: #fff;
    border-radius: 20px;
    padding: 0.5rem 2rem;
    transition: background-color 0.3s ease;
    
}

.btn-custom:hover {
    background-color: #ff3d3d;
}

.table {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);

}

.table th, .table td {
    vertical-align: middle;
    text-align: center;
}

.btn-rounded {
    border-radius: 20px;
  
    color: #fff;
    transition: background-color 0.3s ease;
    padding: 0.25rem 1rem;
}

.btn-rounded:hover {
    background-color: #ff3d3d;
}
.form-select{
    width: 50%;
    border: 2px solid #********;
    border-radius: 20px;
}

/* diable te mark holidays */
.disabled-link {
    pointer-events: none;
    color: gray;
    text-decoration: none;
}
@media only screen and (max-width: 768px) {
    .col-md-7 .col-md-5 {
        flex: 100%;
        max-width: 100%;
        margin-bottom: 20px;
        
    }

    /* .day-col {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
       
    } */

 button#day-btn {
        width: 50px;
        height: 50px;
        margin: 5px;
    }
    .set-date-time, .open-hours {
        text-align: center;
    }

    .row {
        flex-direction: column;
    }

    .checkbox {
        display: flex;
        justify-content: center;
        
    }

    .mark-holidays{
        display: flex;
        flex-wrap: wrap; gap: 30px;
        justify-content: center;
      
    }
    #markAsHolidayLink {
        font-size: 14px;
    }

    #save-btn {
        width: 100%;
        margin-top: 10px;
    }
}

@media (max-width: 768px) {
    .table thead {
        display: none;
    }
    #add-Doctors-btn {
       max-width: 100%;
        margin: 35px auto;
        font-size: 16px;
    }
    .table, .table tbody, .table tr, .table td {
        display: block;
        width: 100%;
    }
    .table tr {
        margin-bottom: 15px;
    }
    .table td {
        text-align: right;
        padding-left: 50%;
        position: relative;
    }
    .table td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 50%;
        padding-left: 15px;
        font-weight: bold;
        text-align: left;
    }
    .table thead {
        display: none;
    }
    .table, .table tbody, .table tr, .table td {
        display: block;
        width: 100%;
    }
    .table tr {
        margin-bottom: 15px;
    }
    .table td {
        text-align: right;
        padding-left: 50%;
        position: relative;
    }
    .table td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 50%;
        padding-left: 15px;
        font-weight: bold;
        text-align: left;
    }
    .form-group{
        margin-top: 15px;
       }
  
}

@media (max-width: 1000px) {
    .first-row{
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    .first-row-right{
        width: 100%;
        justify-content: center;
    }
    .first-row-left{
        width: 100%;
        justify-content: center;
        margin-top: 40px;
    }
    .first-row-left input{
        max-width: 100%;
    }
    #date-time-container {
        border: 2px solid #********;
        border-radius: 30px;
        padding: 20px;
        background-color: #fff;
        width: 100%;
        
        
    }
    #save-btn {
        margin-top: -10px;
        margin-bottom: 20px;
    }
    .form-select{
        width: 60%;
    }
}



   @media only screen and (min-width: 769px) {
    .align-items{
        margin-left: 30%;
       
       }
      
   }

   .first-row{
    display: flex;
align-items: center;
    width: 100%;
}