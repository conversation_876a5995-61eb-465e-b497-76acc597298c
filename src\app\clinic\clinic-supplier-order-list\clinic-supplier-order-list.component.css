h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

input:focus {
  box-shadow: none;
}

.search-input {
  height: 40px;
  outline: none;
  box-shadow: none;
  border-radius: 5px;
  border: solid 1px rgb(200, 200, 200);
  padding-inline: 15px;
  position: absolute;
  right: 0px;
  font-size: 15px;
}

.search-input::placeholder {
  color: rgb(100, 100, 100);
  font-size: 13px;
}

.search-input:hover {
  border-color: #fb751e;
}
.search-input:focus {
  border-color: #fb751e;
}


.card-table-header {
  background: linear-gradient(to right, #fb751e, #b93426);
}

.view-odrer-button {
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  background: #fff6ef;
  color: #fb751e;
  border: 1px solid #ffd8be;
  cursor: pointer;
  padding: 8px 13px;
}

.pagination-button{
  border: 1px solid #fb751e;
  color: #fb751e;
  width: 40px;
  text-align: center;
 }

 .active{
   border: none;
   background: linear-gradient(to right, #fb751e, #b93426);
   color: white;
 }


