.sidebar {
  height: 100%;
  width: 20%; /* Adjust width as needed */
  position: fixed;
  top: 80px; /* Adjust based on header height */
  left: 0;
  background-color: #f8f9fa;
  padding-top: 20px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px 3vw;
  color: black; /* Set text color to black */
  text-decoration: none; /* Remove underline from links */
}

.active{
  position: relative;
  background: linear-gradient(to right, white 0%, white 50%, #fb761e27 100%);
}
.active i, .active div p:first-child{
  color: transparent;
  background: linear-gradient(to bottom, #fb761e, #b93526);
  background-clip: text;
}
.active::before{
  content: '';
  position: absolute;
  background: linear-gradient(to bottom, #fb761e, #b93526);
  height: 75%;
  width: 2px;
  transform: translate(50%,0%);
  right: 0px;
}

.nav-link i {
  margin-right: 10px;
}

.logout {
  margin-top: auto;
  padding: 10px 3vw;
}

.logout span {
  color: red;
}

.nav-link span {
  margin-left: 8px;
  font-size: 18px;
  font-weight: bold;
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    top: 0;
    left: -100%;
    transition: left 0.3s;
  }
  .sidebar.open {
    left: 0;
  }
}
