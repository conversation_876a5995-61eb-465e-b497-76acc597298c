import { Clinic } from "../clinic/clinic";
import { User } from "../user/user";

export class Supplier {
  supplierId: number = 0;
  userId: User = new User();
  supplierCategories: SupplierCategories = new SupplierCategories();
  name: string = '';
  address: string = '';
  city: string = '';
  state: string = '';
  country: string = '';
  tele: string = '';
  designation: string = '';
  email: string = '';
  web: string = '';
  registeredDate: string = '';
  latitude: string = '';
  longitude: string = '';
  contactPerson: string = '';
}

export class SupplierCategories {
  supplierCategoryId: number = 0;
  supplierCategory: string = '';
}

export class SupplierInventory {
  supplierInventoryId: number = 0;
  supplierId: Supplier = new Supplier();
  category: string = '';
  subCategory: string = '';
  description: string = '';
  price: number = 0;
  quantity: number = 0;
  itemStatus: string = '';
  itemImage: string = '';
}




export class SupplierCategoryItemList{
  id: number =0;
  name: string='';
}

export interface SupplierOrderHeader {
  supplierOrderHeaderId: number;
  clinic: Clinic;
  supplier: Supplier;
  orderStatus: ClinicSupplierOrderStatus;
  createdDateTime: string;
}


export interface SupplierOrderDetails {
  supplierOrderDetailsId: number;
  supplierInventory: SupplierInventory;
  requestedItemQty: number;
  approvedItemQty: number;
  supplierOrderHeader: SupplierOrderHeader;
}

export enum ClinicSupplierOrderStatus {
  CLINIC_CREATED ,SUPPLIER_APPROVED,SUPPLIER_REJECTED,CLINIC_REJECTED,SUPPLIER_COMPLETED,SUPPLIER_PROCESSING
}

export interface SupplierClinicQuoteDto{
  orderHeaderId:number
  quouteItemDetails:Map<number,QuoteDetails>;
}

export interface QuoteDetails{
  orderDetailId:number;
  requestedinventoryQty:number;
  acceptedinventoryQty:number;
  inventorySellingPrice:number;
}
