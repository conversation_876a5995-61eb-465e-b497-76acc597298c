import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FutureDentistRoutingModule } from './future-dentist-routing.module';
import { FutureDentistLayoutComponent } from './future-dentist-layout/future-dentist-layout.component';
import { FutureDentistDashboardComponent } from './future-dentist-dashboard/future-dentist-dashboard.component';
import { FutureDentistSidebarComponent } from './components/future-dentist-sidebar/future-dentist-sidebar.component';
import { FutureDentistNavbarComponent } from './components/future-dentist-navbar/future-dentist-navbar.component';


@NgModule({
  declarations: [
    FutureDentistLayoutComponent,
    FutureDentistDashboardComponent,
    FutureDentistSidebarComponent,
    FutureDentistNavbarComponent
  ],
  imports: [
    CommonModule,
    FutureDentistRoutingModule
  ]
})
export class FutureDentistModule { }
