h1,h2,h3,h4,h5,h6,p{
  padding: 0;
  margin: 0;
}
body {
  height: 100vh;
  background: linear-gradient(to top, #c9c9ff 50%, #9090fa 90%) no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
}

#background{
  background-image: url("/assets/images/background.png");
  background-size: cover;
  background-position: center;
    background-color: #f9f9f9;
    min-height: 90vh;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.custom-background{
  background: url("/assets/images/background.png") center center no-repeat;
  background-size: cover;

}

#login-container {
  width: 100%;
  max-width: 90%;
  padding-top: 5%;
  margin: 0 auto;
}

#login-panel {
  border-radius: 10px;
  border: 2px solid #fb751e;
}

.login-header {
  font-weight: 700;
  color: #ff5722;
}
.login-sub-header{
  font-size:13px;
}

.login-sub-header strong{
  font-weight: 600;
}

.input-label{
  font-size: 14px; font-weight: 500;
  color: rgb(100,100,100);
  margin-bottom: 5px;
}

input{
  box-shadow: none !important;
  outline: none;
}
input:not(:disabled):focus ,input:not(:disabled):hover{
  border-color: #fb751e !important;
}

.custom-check-box{
  appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 2px;
  border: 1px solid rgb(200,200,200);
  margin-block: auto;
}

.custom-check-box:checked{
  background: linear-gradient(to right, #fb751e, #b93426);
  border-radius: 3px;
  border: none;
}

#login-panel .form-group {
  margin-bottom: 15px;
}

#login-panel .form-control {
  border-radius: 5px;
  border: 1px solid #ddd;
  padding: 10px;
  width: 100%;
}

#login-panel .btn {
  border-radius: 25px;
  width: 100%;
}

#login-button {
  background: linear-gradient(to right, #fb751e, #b93426);
  border: none;
  padding: 10px;
}

.btn-google {
  background: #ffffff;
  color: #444;
  border: none;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.btn-google i {
  margin-right: 8px;
}

#show-password {
  position: absolute;
  top: 70%;
  transform: translateY(-50%);
  padding-top: 5px;
  padding-right: 10px;
  color: #6c757d;
}

.forgot-password {
  color: #ff5722;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

#forgot-password:hover {
  text-decoration: underline;
}

#divider {
  margin: 20px 0;
  position: relative;
  font-weight: bold;
  color: #aaa;
}

#divider:before,
#divider:after {
  content: "";
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background: #ddd;
}

#divider:before {
  left: 0;
}

.form-check-label {
  font-size: 14px !important;
  font-weight: 400;
}


.form-check-input {
  width: 12px;
  height: 12px;
}

.show-password {
  background-color: transparent;
  border: none;
}

#divider:after {
  right: 0;
}

#register-link {
  color: #ff5722;
  text-decoration: none;
}

#register-link:hover {
  text-decoration: underline;
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check-label {
  margin-left: 5px;
}

.text-center a {
  cursor: pointer;
}

@media (max-width: 768px) {
  #login-container {
    padding-top: 10%;
  }

  #login-panel {
    padding: 20px;
  }

  #login-header {
    padding-top: 0;
  }
}

@media (min-width: 640px) {
  #show-password {


    top: 70%;
    transform: translateY(-50%);
    padding-top: 5px;
    padding-right: 10px;
    color: #6c757d;
  }

  #forgot-password {
    color: #ff5722;
    text-decoration: none;
    font-size: 15px;
  }

  #login-container {
    width: 100%;
    max-width: 80%;
    padding-top: 5%;
    margin: 0 auto;
  }
}

@media (min-width: 1024px) {
  #login-container {
    width: 100%;
    max-width: 65%;
    padding-top: 5%;
    margin: 0 auto;
  }

  #forgot-password {
    color: #ff5722;
    text-decoration: none;
    font-size: 15px;
  }

  .form-check-label {
    font-size: 15px;
  }


  .form-check-input {
    width: 18px;
    height: 18px;
  }
}

@media (min-width: 768px) {
  #show-password {

    top: 70%;
    transform: translateY(-50%);
    padding-top: 5px;
    padding-right: 10px;
    color: #6c757d;
  }

  #login-container {
    width: 100%;
    max-width: 80%;
    padding-top: 5%;
    margin: 0 auto;
  }

  #forgot-password {
    color: #ff5722;
    text-decoration: none;
    font-size: 15px;
  }

  .form-check-label {
    font-size: 15px;
  }


  .form-check-input {
    width: 18px;
    height: 18px;
  }
}

@media (min-width: 1280px) {
  #login-container {
    width: 100%;
    max-width: 65%;
    padding-top: 5%;
    margin: 0 auto;
  }

  #forgot-password {
    color: #ff5722;
    text-decoration: none;
    font-size: 15px;
  }

  .form-check-label {
    font-size: 15px;
  }


  .form-check-input {
    width: 18px;
    height: 18px;
  }
}
