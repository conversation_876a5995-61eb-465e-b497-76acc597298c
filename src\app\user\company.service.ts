import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Company } from './company';

@Injectable({
  providedIn: 'root'
})
export class CompanyService {

  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.localStorage.getItem('auth_token');
  }

  request(method: string, url: string, data: any, params?: any): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set("Authorization", "Bearer " + this.getAuthToken());
    }

    const options = { headers: headers , params: new HttpParams({ fromObject: params })};

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  saveCompany(company: Company): Observable<any> {
    return this.request('POST', '/saveCompany', company);
  }

  getCompanyList(): Observable<Company[]> {
    return this.request('GET', '/companyList', {});
  }

  getCompanyById(id: number): Observable<Company> {
    return this.request("GET", `/getCompanyById/${id}`, {});
  }

  updateCompany(id: number, company: Company): Observable<object> {
    return this.request("PUT", `/updateCompany/${id}`, company);
  }
}
