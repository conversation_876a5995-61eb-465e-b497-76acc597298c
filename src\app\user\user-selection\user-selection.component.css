@import url("https://fonts.googleapis.com/css2?family=Poppins&display=swap");

.user-selection-page {
  background-image: url("/assets/images/background.png");
  background-size: cover;
  background-position: center;
  background-color: #f9f9f9;
  min-height: 90.4vh;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.user-selection-card {
  margin-top: 30px ;
  padding: 40px;
  border-radius: 25px;
  border: 1px solid #fb751e;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-selection-title {
  font-size: 32px;
  font-weight: 700;
  color: #fb751e;
  text-align: center;
  margin-bottom: 30px;
}

.selection-option {
  width: 157px;
  height: 157px;
  background-color: #fff;
  color: #000;
  cursor: pointer;
  border-radius: 10px;
  border: 1px solid #fb751e;
  padding: 15px;
  margin: 15px;
  text-align: center;
  display: block;
  flex-direction: row;
}

.selection-option:hover {
  background-color: #ffb07d3b;
  color: #fb751e;
}

.selection-option.selected {
  background-color: #ffb07d3b;
  color: #fb751e;
}

.option-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 5px;
  margin-top: 15px;
}

.option-text {
  font-size: 14px;
}

.continue-button {
  width: 522px;
  height: 50px;
  background: linear-gradient(to right, #fb751e, #333333);
  border: none;
  padding: 10px;
  border-radius: 20px;
  color: #fff;
  margin-top: 30px;
  text-align: center;
  cursor: pointer;
}

.continue-button:disabled {
  background: linear-gradient(to right, #cca489, #a19c9c);/* Disabled button color */
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .user-selection-page {
  
  }

  .user-selection-card {
    width: 100%;

 
  }

  .user-selection-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .row1 {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    align-items: center;  
    justify-content: center;
  }

  .continue-button {
    width: 90%;
    height: 40px;
    background: linear-gradient(to right, #fb751e, #333333);
    border: none;
    padding: 10px;
    
    border-radius: 20px;
    color: #fff;
    margin-top: 30px;
    text-align: center;
  }
}
