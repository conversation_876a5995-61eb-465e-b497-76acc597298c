.doctor-container {
    padding: 20px;
}

.header-row, .doctor-list-row {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
}

.add-doctor-btn, .add-doctor-modal-btn {
    background: linear-gradient(to right, #FB751E, #B93426);
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 40px;
    border-width: 30%;
}

.add-btn {
    color: #28a745; /* Green color for Add button */
}


.search-bar {
    position: relative;
    margin: 20px 0;
    width: 100%;
    max-width: 300px;
}

#search-input {
    padding: 10px;
    width: 100%;
    padding-left: 30px;
    border: 1px solid #ccc;
    border-radius: 40px;
    border-width: 30%;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

.doctor-table {
    width: 100%;
    border-radius: 10px;
    border-collapse: collapse;
    margin-top: 20px;
}

.doctor-table th, .doctor-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    border-width: 30%;
}

.doctor-table th {
    background-color: #FFB07D38;
    color: rgb(0, 0, 0);
}

.doctor-table thead tr th {
    border-bottom: 2px solid #ddd;
}

.profile-icon {
    color: #FF5722;
    margin-right: 8px;
    vertical-align: middle;
}

.availability {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
    border-width: 30%;
}

.available {
    border-radius: 40px;
    border-width: 30%;
    border: 1px solid;
    color: #00C820;
    border-color: #00C820;
}

.not-available {
    border-radius: 40px;
    border-width: 30%;
    border: 1px solid;
    color: #D85322;
    border-color: #D85322;
}

.action-btn {
    border: none;
    background: none;
    cursor: pointer;
    border-width: 30%;
}

.delete-btn {
    color: #D85322;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 10px 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 500px;
    border-radius: 5px;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    text-align: right;
}

.close-btn:hover,
.close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

form {
    display: flex;
    flex-direction: column;
}

form label {
    margin-top: 10px;
}

form input, form select, form textarea {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 5px;
    border-width: 30%;
}
