import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Laboratory, LaboratoryCategory, LaboratoryClinicOrder, LaboratorySetup, LaboratorySubCategory } from './laboratory';
import { HttpService } from '../http.service';

@Injectable({
  providedIn: 'root'
})

export class LaboratoryService {

  constructor(private httpservice: HttpService) {}

  //Laboratory APIs
  saveLaboratory(laboratory: Laboratory): Observable<any> {
    return this.httpservice.request('POST', '/saveLaboratory', laboratory);
  }

  getLaboratoryList(): Observable<Laboratory[]> {
    return this.httpservice.request('GET', '/laboratoryList', {});
  }

  getLaboratoryById(id: number): Observable<Laboratory> {
    return this.httpservice.request('GET', `/getLaboratoryById/${id}`, {});
  }

  updateLaboratory(id: number, laboratory: Laboratory): Observable<object> {
    return this.httpservice.request('PUT', `/updateLaboratory/${id}`, laboratory);
  }

  deleteLaboratory(id: number): Observable<any> {
    return this.httpservice.request('DELETE', `/deleteLaboratory/${id}`, {});
  }

  checkLaboratoryName(laboratoryName: string): Observable<any> {
    const params = { laboratoryName };
    return this.httpservice.request('GET', `/check_laboratoryName`, null, params);
  }


  // Laboratory Setup APIs
  saveLaboratorySetup(id: number, laboratorySetup: LaboratorySetup): Observable<any> {
    return this.httpservice.request('POST', `/saveLaboratorySetup/${id}`, laboratorySetup);
  }

  getLaboratorySetupsList(): Observable<LaboratorySetup[]> {
    return this.httpservice.request('GET', '/getLaboratorySetupsList', {});
  }

  getLaboratorySetupByUserId(id: number): Observable<LaboratorySetup[]> {
    return this.httpservice.request('GET', `/getLaboratorySetupByUserId/${id}`, {});
  }

  getLaboratorySetupById(id: number): Observable<LaboratorySetup> {
    return this.httpservice.request('GET', `/getLaboratorySetupById/${id}`, {});
  }

  getLaboratoryCategoriesList(): Observable<LaboratoryCategory[]> {
    return this.httpservice.request('GET', '/getLaboratoryCategoriesList', {});
  }

  getLaboratorySubCategoriesList(id: number): Observable<LaboratorySubCategory[]> {
    return this.httpservice.request('GET', `/getLaboratorySubCategoriesList/${id}`, {});
  }

  // Laboratory Clinic Order APIs
  getClinicOrdersByLabUserId(id: number): Observable<LaboratoryClinicOrder[]> {
    return this.httpservice.request('GET', `/getClinicOrdersByLabUserId/${id}`, {});
  }

  updateOrderStatus(id: number, status: string): Observable<LaboratoryClinicOrder[]> {
    return this.httpservice.request('PUT', `/updateOrderStatus/${id}`, status);
  }
}
