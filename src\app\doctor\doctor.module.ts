import { NgModule } from '@angular/core';
import { DoctorRoutingModule } from './doctor-routing.module';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DoctorListComponent } from '../clinic/doctor-list/doctor-list.component';
import { DoctorLayoutComponent } from './doctor-layout/doctor-layout.component';
import { DoctorDashboardComponent } from './doctor-dashboard/doctor-dashboard.component';
import { DoctorSideBarComponent } from './components/doctor-side-bar/doctor-side-bar.component';
import { DoctorRegistrationComponent } from './doctor-registration/doctor-registration.component';
import { DoctorNavbarComponent } from './components/doctor-navbar/doctor-navbar.component';
import { CoreModule } from '../core/core.module';
import { DoctorAssignedClinicListComponent } from './doctor-assigned-clinic-list/doctor-assigned-clinic-list.component';
import { DoctorAppointmentListComponent } from './doctor-appointment-list/doctor-appointment-list.component';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule } from '@angular/material/paginator';


@NgModule({
  declarations: [DoctorListComponent,DoctorLayoutComponent, DoctorDashboardComponent, DoctorSideBarComponent, DoctorNavbarComponent, DoctorAssignedClinicListComponent, DoctorAppointmentListComponent],
  imports: [CommonModule, DoctorRoutingModule, FormsModule,ReactiveFormsModule,CoreModule,MatTableModule,MatProgressSpinnerModule,MatPaginatorModule],
})
export class DoctorModule {}
