import { Component, OnInit, ViewChild } from '@angular/core';

interface  Orders{
  orderID: string;
  date: string;
  time: string;
  laboratory: string;
  status: string;
}
@Component({
  selector: 'app-orders',
  templateUrl: './orders.component.html',
  styleUrls: ['./orders.component.css']
})
export class OrdersComponent implements OnInit  {

  toggleSidebar() {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
    }
  }

  orders: Orders[] = [
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Pending' },
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Medi Laboratory',  status: 'Completed' },
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Completed' },
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Medi Laboratory',  status: 'Pending' },
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Completed' },
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Medi Laboratory',  status: 'Pending' },
        { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Completed' },

    ];

  searchTerm: string = '';
  showModal: boolean = false;
  newPurchasingDashboard: Orders = { orderID: '',date: '', time: '', laboratory: '',  status: 'Pending' };


  filteredorders() {
        return this.orders.filter(orders =>
          orders.orderID.toLowerCase().includes(this.searchTerm.toLowerCase())
        );
  }


  showAll = false;


  currentPage: number = 1;
  visiblePages: number[] = [];
  maxVisiblePages: number = 4; // Show only 4 pages at a time

  ngOnInit(): void {
    this.updateVisiblePages();
  }

  goToPage(page: number): void {
    if (page >= 1) { // No upper limit on page number
      this.currentPage = page;
      this.updateVisiblePages();
    }
  }

  updateVisiblePages(): void {
    let startPage = Math.max(this.currentPage - Math.floor(this.maxVisiblePages / 2), 1);
    let endPage = startPage + this.maxVisiblePages - 1;

    this.visiblePages = Array.from({ length: this.maxVisiblePages }, (_, i) => startPage + i);
  }
}
