import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { Dentist } from './dentist';

@Injectable({
  providedIn: 'root',
})
export class DentistService {
  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.localStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  //Dentist

  saveDentist(dentist: Dentist): Observable<any> {
    return this.request('POST', '/saveDentist', dentist);
  }

  getDentistList(): Observable<Dentist[]> {
    return this.request('GET', '/dentistList', {});
  }

  getDentistById(id: number): Observable<Dentist> {
    return this.request('GET', `/getDentistById/${id}`, {});
  }

  updateDentist(id: number, dentist: Dentist): Observable<object> {
    return this.request('PUT', `/updateDentist/${id}`, dentist);
  }

  deleteDentist(id: number): Observable<any> {
    return this.request('DELETE', `/deleteDentist/${id}`, {});
  }
}
