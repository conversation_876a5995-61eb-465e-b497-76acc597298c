import { Component } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Dentist } from './dentist';
import { DentistService } from './dentist.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2'; // Import SweetAlert

@Component({
  selector: 'app-future-dentist-registration',
  templateUrl: './future-dentist-registration.component.html',
  styleUrls: ['./future-dentist-registration.component.css'],
})
export class FutureDentistRegistrationComponent {
  newDentist: Dentist = {
    id: 0,
    firstName: '',
    lastName: '',
    email: '',
    contact<PERSON>erson: '',
    contactNumber: '',
    password: '',
    confirmPassword: '',
  };

  constructor(private dentistService: DentistService, private router: Router) {}

  registrationDentist() {
    Swal.fire({
      title: "Wait until approval!",
      text: "Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.",
      icon: 'success',
      confirmButtonText: 'OK',
    });
    // console.log('Dentist Registration');
    // this.dentistService.saveDentist(this.newDentist).subscribe(
    //   (data) => {
    //     // SweetAlert for successful registration
    //     Swal.fire({
    //       icon: 'success',
    //       title: 'Registered Successfully!',
    //       text: 'Thank you for registering! Please verify your email to complete the login process.',
    //       confirmButtonText: 'OK',
    //     }).then(() => {
    //       // Optionally redirect after confirmation
    //       this.router.navigate(['/user-selection']);
    //     });
    //   },
    //   (error) => {
    //     // Optionally handle error case
    //     Swal.fire({
    //       icon: 'error',
    //       title: 'Registration Failed',
    //       text: 'There was an error registering the dentist. Please try again.',
    //       confirmButtonText: 'OK',
    //     });
    //   }
    // );
  }

  navigateUserSelection() {
    this.router.navigate(['/user-selection']);
  }
}
