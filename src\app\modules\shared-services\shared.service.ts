import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class SharedService {
  private districts: string[] = [
    'Colombo',
    'Gampaha',
    'Kalutara',
    'Kandy',
    'Matale',
    'NuwaraEliya',
    'Galle',
    'Matara',
    'Hambantota',
    'Jaff<PERSON>',
    'Kilinoch<PERSON>',
    'Mannar',
    'Mullaitivu',
    'Vavuniya',
    'Trincomalee',
    'Batticaloa',
    'Ampara',
    'Kurunegala',
    'Puttalam',
    'Anuradhapura',
    'Polonnaruwa',
    'Badulla',
    'Monaragala',
    'Ratnapura',
    'Kegalle',
  ];

  private cities: { [key: string]: string[] } = {
    Colombo: [
      'Colombo',
      'Dehiwala-Mount Lavinia',
      'Moratuwa',
      'Sri Jayawardenepura Kotte',
      'Battaramulla',
      'Maharagama',
      'Nugegoda',
      'Piliyandala',
      'Ratmalana',
      'Boralesgamuwa',
      'Kadu<PERSON><PERSON>',
      'Homagama',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON>tta<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      'Seedu<PERSON>',
      'Katunaya<PERSON>',
      'Negombo',
      'Kiribathgoda',
      'Thalawathugoda',
      'Pannipitiya',
      'Malabe',
      'Athurugiriya',
      'Hanwella',
      'Padukka',
    ],
    Gampaha: [
      'Gampaha',
      'Negombo',
      'Ja-Ela',
      'Wattala',
      'Kelaniya',
      'Ragama',
      'Kadawatha',
      'Kiribathgoda',
      'Ganemulla',
      'Nittambuwa',
      'Minuwangoda',
      'Divulapitiya',
      'Veyangoda',
      'Mirigama',
      'Attanagalla',
      'Seeduwa',
      'Katunayake',
      'Biyagama',
      'Delgoda',
      'Weliweriya',
      'Mahara',
      'Dompe',
      'Udugampola',
      'Yakkala',
    ],
    Kalutara: [
      'Kalutara',
      'Beruwala',
      'Aluthgama',
      'Panadura',
      'Horana',
      'Matugama',
      'Bandaragama',
      'Wadduwa',
      'Dodangoda',
      'Agalawatta',
    ],
    Kandy: [
      'Kandy',
      'Peradeniya',
      'Gampola',
      'Nawalapitiya',
      'Kadugannawa',
      'Katugastota',
      'Pilimathalawa',
      'Wattegama',
      'Akurana',
      'Digana',
    ],
    Matale: [
      'Matale',
      'Dambulla',
      'Sigiriya',
      'Galewela',
      'Naula',
      'Ukuwela',
      'Rattota',
      'Pallepola',
      'Laggala',
    ],
    NuwaraEliya: [
      'Nuwara Eliya',
      'Hatton',
      'Talawakele',
      'Ginigathhena',
      'Kotagala',
      'Ragala',
      'Haputale',
      'Ambewela',
    ],
    Galle: [
      'Galle',
      'Hikkaduwa',
      'Ambalangoda',
      'Karapitiya',
      'Baddegama',
      'Elpitiya',
      'Bentota',
      'Weligama',
      'Unawatuna',
    ],
    Matara: [
      'Matara',
      'Weligama',
      'Akurassa',
      'Hakmana',
      'Kamburupitiya',
      'Deniyaya',
      'Dondra',
      'Dickwella',
      'Thihagoda',
    ],
    Hambantota: [
      'Hambantota',
      'Tangalle',
      'Tissamaharama',
      'Beliatta',
      'Ambalantota',
      'Weeraketiya',
      'Sooriyawewa',
      'Angunakolapelessa',
      'Kataragama',
    ],
    Jaffna: [
      'Jaffna',
      'Nallur',
      'Chavakachcheri',
      'Point Pedro',
      'Valvettithurai',
      'Kodikamam',
      'Kokuvil',
      'Karainagar',
      'Kankesanthurai',
    ],
    Kilinochchi: [
      'Kilinochchi',
      'Pooneryn',
      'Paranthan',
      'Kandawalai',
      'Karachchi',
    ],
    Mannar: ['Mannar', 'Adampan', 'Madu', 'Pesalai', 'Murunkan'],
    Mullaitivu: [
      'Mullaitivu',
      'Puthukudiyiruppu',
      'Thunukkai',
      'Oddusuddan',
      'Mallavi',
    ],
    Vavuniya: [
      'Vavuniya',
      'Nedunkeni',
      'Vavuniya South',
      'Vavuniya North',
      'Omanthai',
    ],
    Trincomalee: ['Trincomalee', 'Kinniya', 'Muttur', 'Kantale', 'Seruwila'],
    Batticaloa: [
      'Batticaloa',
      'Kalkudah',
      'Valachchenai',
      'Eravur',
      'Kattankudy',
    ],
    Ampara: [
      'Ampara',
      'Kalmunai',
      'Sainthamaruthu',
      'Uhana',
      'Akkaraipattu',
      'Sammanthurai',
    ],
    Kurunegala: [
      'Kurunegala',
      'Wariyapola',
      'Polgahawela',
      'Kuliyapitiya',
      'Pannala',
      'Maho',
      'Nikaweratiya',
      'Galgamuwa',
    ],
    Puttalam: [
      'Puttalam',
      'Chilaw',
      'Wennappuwa',
      'Nattandiya',
      'Mundel',
      'Dankotuwa',
      'Marawila',
      'Kalpitiya',
    ],
    Anuradhapura: [
      'Anuradhapura',
      'Kekirawa',
      'Medawachchiya',
      'Mihintale',
      'Talawa',
      'Thambuttegama',
      'Eppawala',
      'Horowpothana',
    ],
    Polonnaruwa: [
      'Polonnaruwa',
      'Hingurakgoda',
      'Medirigiriya',
      'Kaduruwela',
      'Thamankaduwa',
      'Giritale',
    ],
    Badulla: [
      'Badulla',
      'Bandarawela',
      'Ella',
      'Haputale',
      'Mahiyanganaya',
      'Hali-Ela',
      'Diyatalawa',
      'Welimada',
      'Passara',
    ],
    Monaragala: [
      'Monaragala',
      'Wellawaya',
      'Bibila',
      'Buttala',
      'Siyambalanduwa',
      'Kataragama',
    ],
    Ratnapura: [
      'Ratnapura',
      'Embilipitiya',
      'Balangoda',
      'Pelmadulla',
      'Eheliyagoda',
      'Kuruwita',
      'Opanayaka',
      'Kahawatta',
    ],
    Kegalle: [
      'Kegalle',
      'Mawanella',
      'Warakapola',
      'Rambukkana',
      'Galigamuwa',
      'Yatiyantota',
      'Dehiowita',
      'Deraniyagala',
    ],
  };

  getDistricts(): string[] {
    return this.districts;
  }

  getCitiesByDistrict(district: string): string[] {
    return this.cities[district] || [];
  }
}


