<div class="overlay" [class.show]="showModal"></div>
<div class="container-fluid">
  <div class="row">
      <div class="sidebar-container"[class.blurred]="showModal">
      </div>

  </div>
</div>

<div class="container">
  <header>
    <div class="send-orders-container">
      <div class="header-row">
        <div class="header-row-h1">
          Order Requests
        </div>

        <div class="search-bar">
          <i class="bi bi-search search-icon"></i>
          <input type="text" placeholder="Search Order" [(ngModel)]="searchTerm" id="search-input" />
        </div>
        <button class="btn btn-outline-warning" id="printButton"> + Add Inventory Items</button>
      </div>
      <div class="header-bottom-line"></div>
      <br>
      <div>

        <div>

          <button (click)="showAll = !showAll" class="see-all-button">See All></button>
        </div>
      </div>

    </div>
  </header>

  <main>
    <section class="order-requests">
      <h2>New Order Requests</h2>
      <table>
        <thead>
          <tr>
            <th><input type="checkbox"></th>
            <th>Clinic Name</th>
            <th>Date</th>
            <th>Time</th>
            <th>Address</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let orderRequest of paginatedItems">
            <td><input type="checkbox"></td>
            <td>{{ orderRequest.clinicname }}</td>
            <td>{{ orderRequest.date }}</td>
            <td>{{ orderRequest.time }}</td>
            <td>{{ orderRequest.address }}</td>
            <td class="status-cell">
              <button
                [ngClass]="{'status pending': orderRequest.status === 'Pending', 'status approved': orderRequest.status === 'Approved'}">
                {{ orderRequest.status }}
              </button>
            </td>
            <td>
              <button class="view-request-button" (click)="viewOrderDetails(orderRequest.id)">View Request</button>
            </td>
          </tr>
        </tbody>

      </table>
      <div class="pagination">
        <button class="btn btn-outline-secondary" (click)="prevPage()" [disabled]="currentPage === 1">&lt;</button>
        <span *ngFor="let page of getPagesArray()" class="page-number" [class.active]="currentPage === page"
          (click)="goToPage(page)">{{ page }}</span>
        <button class="btn btn-outline-secondary" (click)="nextPage()"
          [disabled]="(currentPage * itemsPerPage) >= filteredItems.length">&gt;</button>
      </div>

    </section>
  </main>
</div>


<div class="modal" [class.show]="showModal">
  <div class="modal-content">
    <span class="close" (click)="closeModal()">&times;</span>
    <div class="popuphead">View Request</div>
    <div *ngIf="selectedOrder" class="popuptext">

      <div class="popuptable">
        <table class="table table-borderless">
          <thead>
            <tr>
              <td style="font-weight: bold">Clinic Name:</td>
              <td>{{ selectedOrder.clinicname }}</td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="font-weight: bold">Address:</td>
              <td>{{ selectedOrder.address }}</td>
            </tr>
            <tr>
              <td style="font-weight: bold">Contact:</td>
              <td>{{ selectedOrder.contact }}</td>
            </tr>
          </tbody>
        </table>
      </div>

<!--      <div>-->
<!--      <p style="display: inline;"><strong></strong></p>-->
<!--      <div class="text" style="display: inline;"></div>-->
<!--      </div>-->
<!--      <div>-->
<!--      <p style="display: inline;"><strong>Address:</strong></p>-->
<!--      <div class="text" style="display: inline;">{{ selectedOrder.address }}</div>-->
<!--      </div>-->
<!--      <div>-->
<!--      <p style="display: inline;"><strong>Contact:</strong></p>-->
<!--      <div class="text" style="display: inline;">{{ selectedOrder.contact }}</div>-->
<!--      </div>-->
    </div>
    <div>
      <br>
    </div>
    <table>
      <thead>
        <tr>
          <th>Item</th>
          <th>Item Category</th>
          <th>Qty</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Clinic Item</td>
          <td>Clinic Item</td>
          <td>02</td>
        </tr>
        <tr>
          <td>Clinic Item</td>
          <td>Clinic Item</td>
          <td>01</td>
        </tr>
      </tbody>
    </table>
    <button class="create-quote-button">Create Quote</button>
  </div>
</div>
