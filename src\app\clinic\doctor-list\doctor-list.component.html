<div class="doctor-container">
    <div class="header-row">
        <h1 style="font-weight: bold;">Doctor</h1>
        <button class="btn add-doctor-btn" (click)="openAddDoctorModal()">Add Doctor</button>
    </div>
    <div class="header-bottom-line"></div>

    <div class="doctor-list-row">
        <h2 style="font-weight: bold;">Doctors List</h2>
        <div class="search-bar" >
            <i class="bi bi-search search-icon"></i>
            <input type="text" placeholder="Search Doctor" [(ngModel)]="searchTerm" 
            id="search-input" 
            (input)="searchDoctors()" />
        </div>
    </div>

    <table class="doctor-table">
        <thead>
            <tr>
                <th>Name</th>
                <th>reg_no</th>
                <th>Specialty</th>
                <th>Telephone</th>
                <th>Email</th>
                <th>Availability</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let doctor of filteredDoctors">
                <td>
                    <i class="bi bi-person-circle profile-icon"></i>
                    {{ doctor.firstName }}
                </td>
                <td>{{ doctor.regNo }}</td>
                <td>{{ doctor.specialty }}</td>
                <td>{{ doctor.telephone }}</td>
                <td>{{ doctor.email }}</td>
                <td class="availability-cell">
                    <button [ngClass]="{'availability available': doctor.availability === 'Available', 'availability not-available': doctor.availability !== 'Available'}">{{ doctor.availability }}</button>
                </td>
               <td class="action-cell">
    <button class="btn action-btn edit-btn">
        <i class="bi bi-pencil"></i>
    </button>
    <button class="btn action-btn delete-btn" (click)="deleteDoctor(doctor)">
        <i class="bi bi-trash"></i>
    </button>
    <!-- Add Assign Doctor icon -->
    <button class="btn action-btn assign-btn" (click)="assignDoctor(doctor)">
        <i class="bi bi-person-plus"></i> <!-- Add icon here -->
    </button>
</td>
            </tr>
        </tbody>
    </table>
    
    <!-- Add Doctor Modal -->
    <!-- <div class="modal" [ngClass]="{'show': showModal}">
        <div class="modal-content">
            <span class="close-btn" (click)="closeModal()">&times;</span>
            <h2>Add Doctor</h2>
            <form (submit)="addDoctor()">
                <label for="registrationNumber">SLMC Registration Number</label>
                <input type="text" id="registrationNumber" [(ngModel)]="newDoctor.registrationNumber" name="registrationNumber" />

                <label for="title">Title</label>
                <select id="title" [(ngModel)]="newDoctor.title" name="title">
                    <option value="Dr.">Dr.</option>
                    <option value="Mr.">Mr.</option>
                    <option value="Ms.">Ms.</option>
                </select>

                <label for="name">Name</label>
                <input type="text" id="name" [(ngModel)]="newDoctor.name" name="name" />

                <label for="specialty">Specialty</label>
                <input type="text" id="specialty" [(ngModel)]="newDoctor.specialty" name="specialty" />

                <label for="qualifications">Qualifications</label>
                <textarea id="qualifications" [(ngModel)]="newDoctor.qualifications" name="qualifications"></textarea>

                <label for="others">Others</label>
                <textarea id="others" [(ngModel)]="newDoctor.others" name="others"></textarea>

                <button type="submit" class="btn add-doctor-modal-btn" style="margin-top: 20px;">Add Doctor</button>
            </form>
        </div>
    </div> -->
</div>
