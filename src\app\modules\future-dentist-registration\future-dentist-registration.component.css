/* Base styles (desktop and larger screens) */
.registration-page {
  background-image: url('/assets/images/background.png');
  background-size: cover;
  background-position: center;
  background-color: #f9f9f9;
  /* min-height: 80vh; */
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  /* height: calc(100vh - 10vh); */
}
.backtoselection-button {
  width: 150px;
  height: 40px;
  background: none;
  border: 2px solid #fb751e;
  color: #fb751e;
  font-size: 16px;
  font-weight: bold;
  border-radius: 20px;
  cursor: pointer;
  /* position: absolute; */
  /* left: 0; */
  /* top: 107px; */
  /* margin-left: 5%; */
  justify-content: center;
  margin-bottom: 20px;
  margin-top: 20px;
}

.registration-card {
  width: 620px;
  height: auto;
  padding: 40px;
  border-radius: 25px;
  border: 1px solid #fb751e;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  background-color: white;
  display: flex;
  flex-direction: column;
  /* justify-content: space-between; */
}

h3 {
  font-family: 'Inter', sans-serif;
  font-size: 30px;
  font-weight: 700;
  color: #fb751e;
  text-align: center;
  margin-bottom: 40px;
}

.form-row {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
}

.form-row .form-group {
  flex: 1;
  margin-right: 10px;
}

.form-row .form-group:last-child {
  margin-right: 0;
}

.form-row label {
  font-family: "Inter";
  font-size: 16px;
  font-weight: 400;
  text-align: left;
  margin-bottom: 5px;
  color: #000000;
  display: block;
}

.form-group input {
  font-family: "Inter";
  font-weight: 400;
  font-size: 14px;
  color: #495057;
}

input[type="text"],
input[type="email"],
input[type="password"] {
  width: 100%;
  padding: 5px;
  border: 1px solid #b3b3b3;
  border-radius: 4px;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
  outline: none;
  border-color: #ff7a00;
}

.register-button {
  width: 100%;
  padding: 14px;
  background-image: linear-gradient(to right, #fb751e, #333333);
  border: none;
  border-radius: 20px;
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 30px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .registration-page {
    padding: 20px;
  }

  .header-links {
    flex-direction: column;
    align-items: flex-start;
  }

  .contact-link {
    margin-bottom: 10px;
  }

  .login-button {
    width: 100%;
    padding: 10px;
  }

  .registration-card {
    width: 100%;
    padding: 20px;
  }

  .registration-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .form-group {
    width: 100%;
    margin-bottom: 15px;
  }

  .form-control {
    font-size: 14px;
  }

  .register-button {
    padding: 12px;
    font-size: 16px;
  }
}

@media (min-width: 1024px) {
  .backtoselection-button {
    width: 150px;
    height: 40px;
    background: none;
    border: 2px solid #fb751e;
    color: #fb751e;
    font-size: 16px;
    font-weight: bold;
    border-radius: 20px;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 107px;
    margin-left: 5%;
  }
}