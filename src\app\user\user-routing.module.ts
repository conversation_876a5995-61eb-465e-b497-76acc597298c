import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserLayoutComponent } from './user-layout/user-layout.component';
import { UserAppointmentComponent } from './user-appointment/user-appointment.component';
import { UserMailVerificationComponent } from './user-mail-verification/user-mail-verification.component';

const routes: Routes = [
  {
    path: '',
    component: UserLayoutComponent,
    children:[
      {path:'',component:UserAppointmentComponent},
      {path:'verify-user', component: UserMailVerificationComponent}
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserRoutingModule {

}
