body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
}

app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.cover-page {
  background-image: url("/assets/images/back-appointment.png");
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  padding-top: 10%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.text-section {
  z-index: 1;
  margin-left: 5%;
}

.gradient-text {
  background: linear-gradient(to right, #fb751e, #70190f, #fb751e, #70190f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 35px;
  font-weight: bold;
  margin: 10px 0;
  background-clip: text;
}

h4 {
  color: #555;
  font-size: 24px;
  font-style: italic;
  margin: 30px 0;
  font-weight: bold;
}

.appointment-button {
  background: linear-gradient(to right, #333333, #fb751e);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 20px;
  border-radius: 23px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.appointment-button .arrow {
  width: 40px;
  height: 40px;
}
