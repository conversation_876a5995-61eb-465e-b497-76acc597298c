<app-default-navbar loggedUser="Hello"></app-default-navbar>
<div class="user-selection-page">
    <div class="user-selection-card">
        <!-- <div>
            <h3 class="user-selection-title">Are You</h3>
        </div> -->
        <div class="row row1">
            <div class="selection-option"
            [class.selected]="isSelected('Clinic')"
            (click)="selectOption('Clinic')">
                <img src="../../assets/images/vector2.png" alt="vector2-logo" class="option-logo">
                <h5 class="option-text">Clinic</h5>
            </div>
            <div class="selection-option"
            [class.selected]="isSelected('Doctor')"
            (click)="selectOption('Doctor')">
                <img src="../../assets/images/vector1.png" alt="vector1-logo" class="option-logo">
                <div class="option-text">Doctor</div>
            </div>
            <div class="selection-option"
            [class.selected]="isSelected('Supplier')"
            (click)="selectOption('Supplier')">
                <img src="../../assets/images/sup.png" alt="jam_shopify-circle-logo" class="option-logo">
                <div class="option-text">Supplier</div>
            </div>
        </div>
        <div class="row row1">
            <div class="selection-option"
            [class.selected]="isSelected('Laboratory')"
            (click)="selectOption('Laboratory')">
                <img src="../../assets/images/fontisto_laboratory.png" alt="fontisto_laboratory-logo" class="option-logo">
                <div class="option-text">Laboratory</div>
            </div>
            <div class="selection-option"
            [class.selected]="isSelected('Future Dentist')"
            (click)="selectOption('Future Dentist')">
                <img src="../../assets/images/wpf_doctors-bag.png" alt="wpf_doctors-bag-logo" class="option-logo">
                <div class="option-text">Future Dentist</div>
            </div>
        </div>
        <div class="row1">
            <button type="submit" class="continue-button" (click)="onContinue()" [disabled]="!selectedOption">Continue</button>
        </div>
    </div>
</div>
