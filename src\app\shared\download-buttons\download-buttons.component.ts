import { Component, HostListener } from '@angular/core';

@Component({
  selector: 'app-download-buttons',
  templateUrl: './download-buttons.component.html',
  styleUrls: ['./download-buttons.component.css']
})
export class DownloadButtonsComponent {
  deferredPrompt: any;

  constructor() { }

  @HostListener('window:beforeinstallprompt', ['$event'])
  onBeforeInstallPrompt(event: Event) {
    event.preventDefault();
    this.deferredPrompt = event;
  }

  installPWA(platform: string) {
    if (platform === 'android') {
      if (this.deferredPrompt) {
        this.deferredPrompt.prompt();
        this.deferredPrompt.userChoice.then((choiceResult: any) => {
          if (choiceResult.outcome === 'accepted') {
            console.log('User accepted the install prompt');
          } else {
            console.log('User dismissed the install prompt');
          }
          this.deferredPrompt = null;
        });
      } else {
        alert('PWA installation prompt not available');
      }
    } else if (platform === 'ios') {
      alert('To install this app on your iOS device, tap Share and then ADD to Home Screen');
    }
  }
}
