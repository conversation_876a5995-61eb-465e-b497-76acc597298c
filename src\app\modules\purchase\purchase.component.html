<div class="container">
  <div class="header">
    <h1 class="title">Material Purchase</h1>
    <div *ngIf="currentView == 'items'  || currentView == 'itemDetails'" class="cart-icon" (click)="openCartModal()">
      <i class="bi bi-cart-fill"></i>
      <span class="badge" [class.has-items]="cart.length > 0">{{ cart.length }}</span>
    </div>
  </div>
  <hr />
  <h2 class="subheading">Purchase Requests</h2>
  <h3 class="subheading_2">Select Item Category</h3>
  <div class="filter-section">
    <select class="form-select custom-select" [(ngModel)]="selectedCategory">
      <option>Advanced Dental Chair</option>
      <option>Dental Clinic Chair</option>
      <option>Portable Dental Chair</option>
    </select>
    <div class="search-container">
      <i class="bi bi-search search-icon"></i>
      <input type="text" class="form-control custom-input" placeholder=" Search item" [(ngModel)]="searchTerm">
    </div>
  </div>
  <div class="dynamic-section" [ngSwitch]="currentView">
    <!-- Supplier Details -->
    <div *ngSwitchCase="'suppliers'" class="supplier-section">
      <h3 class="section-header">All Suppliers Related to Your Selected Item</h3>
      <div class="supplier-table">
        <div class="supplier-row" *ngFor="let supplier of suppliers; let odd = odd" [class.odd-row]="odd">
          <div class="supplier-cell">
            <strong>{{ supplier.name }}</strong><br />
            {{ supplier.address }}
          </div>
          <div class="supplier-cell" style="font-size: 14px; margin-top: 15px;">
            {{ supplier.phone1 }}  |  {{ supplier.phone2 }}
          </div>
          <div class="supplier-cell">
            <button class="btn btn-outline-primary visit-store-btn" (click)="visitStore(supplier)">Visit Store</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Item Details -->
    <div *ngSwitchCase="'items'" class="items-section">
      <h3 class="section-header">{{ selectedSupplier?.name }}</h3>
      <div class="item-grid">
        <div *ngFor="let item of paginatedItems" class="item-card" (click)="showItemDetails(item)">
          <img [src]="item.image" [alt]="item.name" />
          <div class="item-name">{{ item.name }}</div>
          <div class="item-price">LKR. {{ item.price }}</div>
          <div class="item-stock" [class.in-stock]="item.stock > 5" [class.low-stock]="item.stock <= 5" style="display: inline-block;">
            {{ item.stock > 5 ? 'In Stock' : 'Low Stock' }}
          </div>
          <button class="btn btn-primary add-to-cart-btn" (click)="addToCart(item); $event.stopPropagation();">Add to Cart</button>
        </div>
      </div>
      <div class="pagination">
        <button class="btn btn-outline-secondary" (click)="prevPage()" [disabled]="currentPage === 1">&lt;</button>
        <span *ngFor="let page of [1, 2, 3, 4]" class="page-number" [class.active]="currentPage === page" (click)="goToPage(page)">{{ page }}</span>
        <button class="btn btn-outline-secondary" (click)="nextPage()" [disabled]="(currentPage * itemsPerPage) >= filteredItems.length">&gt;</button>
      </div>
    </div>
    <!-- Individual Item Details -->
    <div *ngSwitchCase="'itemDetails'" class="item-details-section">
      <h3 class="section-header">{{ selectedItem?.name }}</h3>
      <div class="item-details-content">
        <div class="item-details-info">
          <p><strong>Item Name:</strong> {{ selectedItem?.name }}</p>
          <p><strong>Item Code:</strong> {{ selectedItem?.id }}</p>
          <p><strong>Availability:</strong> <span [class.in-stock]="selectedItem?.stock! > 5" [class.low-stock]="selectedItem?.stock! <= 5">
            {{ selectedItem?.stock! > 5 ? 'In Stock' : 'Low Stock' }}
          </span></p>
          <p style="font-weight: bold; font-size: 25px;"><strong>Price:</strong> LKR. {{ selectedItem?.price }}</p>
          <button class="btn btn-primary request-now-btn" (click)="addToCart(selectedItem!)">Request Now</button><br>
          <button class="btn btn-link browse-more-btn" (click)="backToItems()">Browse further items from same supplier</button>
        </div>
        <div class="item-details-image">
          <img [src]="selectedItem?.image" [alt]="selectedItem?.name" />
        </div>
      </div>
    </div>
  </div>
  <!-- Cart Modal -->
  <!-- Cart Modal -->
<div id="cartModal" class="modal">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" (click)="closeCartModal()" style="font-size: 20px; font-weight: bold;"></button>
      </div>
      <div class="modal-title-row">
        <h5 class="modal-title">Cart View</h5>
        <button class="btn btn-sm btn-delete" (click)="deleteSelectedItems()" style="font-size: 20px; color: red; margin-top: 5px;">
          <i class="fas fa-trash-alt"></i>
        </button>
      </div>
      <div class="modal-body">
        <div *ngFor="let cartItem of cart" class="cart-item">
          <input type="checkbox" class="cart-item-checkbox" [(ngModel)]="cartItem.selected">
          <img [src]="cartItem.item.image" [alt]="cartItem.item.name" class="cart-item-image">
          <div class="cart-item-details">
            <strong>{{ cartItem.item.name }}</strong> &nbsp;
            <small>{{ cartItem.item.id }}</small>
          </div>
          <div class="cart-item-price">LKR. {{ cartItem.item.price * cartItem.quantity }}</div>
          <div class="cart-item-quantity">
            <button class="btn btn-sm btn-quantity" (click)="decreaseQuantity(cartItem)">-</button>
            <span>{{ cartItem.quantity }}</span>
            <button class="btn btn-sm btn-quantity" (click)="increaseQuantity(cartItem)">+</button>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary add-item-btn" (click)="closeCartModal()">Add Other Item</button>
        <button type="button" class="btn btn-primary request-now-btn-model">Request Order</button>
      </div>
    </div>
  </div>
</div>
</div>
