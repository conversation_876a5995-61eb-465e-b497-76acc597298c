import { Component, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-reject-popup',
  templateUrl: './reject-popup.component.html',
  styleUrls: ['./reject-popup.component.css']
})
export class RejectPopupComponent {
  @Output() closePopup = new EventEmitter<void>(); // Emit event to close the popup
  @Output() confirmReject = new EventEmitter<void>(); // Emit event to confirm rejection

  showRejectReasonPopup: boolean = false; // Control showing of the new rejection reason popup

  // Function to close the popup
  onClose() {
    this.closePopup.emit();
  }

  // Function to confirm rejection and show the reason popup
  onConfirmReject() {
    this.showRejectReasonPopup = true; // Show the new popup
  }

  // Handle closing the reason popup
  onReasonPopupClose() {
    this.showRejectReasonPopup = false;
  }

  // <PERSON>le receiving the submitted rejection reason
  onReasonSubmitted(reason: string) {
    console.log('Rejection Reason:', reason);
    this.showRejectReasonPopup = false;
    // You can emit the reason to the parent or handle it as needed
  }
}
