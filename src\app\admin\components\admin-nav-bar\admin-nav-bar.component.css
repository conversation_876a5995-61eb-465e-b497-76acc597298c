.navbar {
  height: 100%;
  background: linear-gradient(to right, white 0%, #fb761e27 100%);
   border-bottom:1px solid rgb(240,240,240)
}

.navbar-content {
  width: 100%;
  display: flex;
  align-items: center;
}

.logo {
  margin-left: 2%;
  height: 2vw; /* Adjusted height as a percentage of viewport height */
  width: 8.5vw; /* Adjusted width as a percentage of viewport width */
  margin-right: 2%; /* Adjust margin as needed */
}

.welcome-message {
  font-style: italic;
  margin-left: 10%;
  font-size: 2vw;
  color: white;
  margin-right: 35%;
}


.profile-pic {
  height: 10vh; /* Adjusted height as a percentage of viewport height */
  width: 10vh; /* Maintain aspect ratio */
  border-radius: 50%;
  border: 2px solid white;
  margin-right: 2%; /* Adjust margin as needed */
}

.username {
  margin-right: 2%; /* Adjust margin as needed */
  font-size: 1vw; /* Adjust font size as a percentage of viewport width */
  color: white;
}

.navbar-toggler {
  height: 6vw; /* Adjusted height as a percentage of viewport height */
  width: 6vw; /* Adjusted width as a percentage of viewport height */
  margin-left: auto;
}

@media (max-width: 768px) {
  .navbar-content {
    flex-direction: column;
    align-items: start;
  }
  .navbar-toggler {
    order: -1;
  }
  .spacer {
    display: none;
  }
}
