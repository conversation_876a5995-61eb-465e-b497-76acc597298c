import { Component, OnInit } from '@angular/core';
import { Notification } from './notification';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.css']
})
export class NotificationsComponent implements OnInit {
  notifications: Notification[] = [
    {
      id: 1,
      subject: 'NextDent Dental',
      message: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse et aliquam erat. Etiam semper, sed non volutpat maximus, leo ligula lectus nisi non egestas dui sapien eu est.',
      date: new Date('2024-06-17T10:10:00'),
      isRead: false
    },
    {
      id: 2,
      subject: 'NextDent Dental',
      message: 'Suspendisse et aliquam erat. Etiam semper, sed non volutpat maximus, leo ligula lectus nisi non egestas dui sapien eu est.',
      date: new Date('2024-06-17T11:30:00'),
      isRead: true
    },
    {
      id: 3,
      subject: 'NextDent Dental',
      message: 'Etiam semper, sed non volutpat maximus, leo ligula lectus nisi non egestas dui sapien eu est.',
      date: new Date('2024-06-16T09:45:00'),
      isRead: false
    },
    {
      id: 4,
      subject: 'NextDent Dental',
      message: 'Leo ligula lectus nisi non egestas dui sapien eu est.',
      date: new Date('2024-06-15T14:20:00'),
      isRead: true
    },
    {
      id: 5,
      subject: 'NextDent Dental',
      message: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse et aliquam erat. Etiam semper, sed non volutpat maximus, leo ligula lectus nisi non egestas dui sapien eu est.',
      date: new Date('2024-07-04T10:10:00'),
      isRead: false
    },
    {
      id: 6,
      subject: 'NextDent Dental',
      message: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse et aliquam erat. Etiam semper, sed non volutpat maximus, leo ligula lectus nisi non egestas dui sapien eu est.',
      date: new Date('2024-07-05T12:10:00'),
      isRead: false
    },
    {
      id: 7,
      subject: 'NextDent Dental',
      message: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse et aliquam erat. Etiam semper, sed non volutpat maximus, leo ligula lectus nisi non egestas dui sapien eu est.',
      date: new Date('2024-07-03T10:10:00'),
      isRead: false
    },
  ];

  selectedNotification: Notification | null = null;
  selectedNotifications: Set<number> = new Set();

  ngOnInit(): void {
    this.notifications.sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  openNotificationDetails(notification: Notification): void {
    this.selectedNotification = notification;
    if (!notification.isRead) {
      notification.isRead = true;
    }
  }

  closeNotificationDetails(): void {
    this.selectedNotification = null;
  }

  markAllAsRead(): void {
    this.notifications.forEach(notification => notification.isRead = true);
  }

  deleteAllNotifications(): void {
    this.notifications = [];
    this.selectedNotifications.clear();
  }

  getTodayNotifications(): Notification[] {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return this.notifications.filter(n => n.date >= today);
  }

  getYesterdayNotifications(): Notification[] {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return this.notifications.filter(n => n.date >= yesterday && n.date < today);
  }

  getPreviousNotifications(): Notification[] {
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
    twoDaysAgo.setHours(0, 0, 0, 0);
    return this.notifications.filter(n => n.date < twoDaysAgo);
  }

  toggleNotificationSelection(id: number): void {
    if (this.selectedNotifications.has(id)) {
      this.selectedNotifications.delete(id);
    } else {
      this.selectedNotifications.add(id);
    }
  }

  deleteSelectedNotifications(): void {
    this.notifications = this.notifications.filter(n => !this.selectedNotifications.has(n.id));
    this.selectedNotifications.clear();
  }
}