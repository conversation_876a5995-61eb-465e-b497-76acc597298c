body {
    font-family: Arial, sans-serif;
}

.services-page {
    max-width: 1400px;
    margin: 0 auto;
    padding: 50px;
    margin-left: 23%;
    height: calc(10vh - 10px);
    margin-top: -3.8%;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h1 {
    font-size: 64px;
    margin: 0;
    font-family: Inter;
}

.content-wrapper {
    display: flex;
    padding-bottom: 40px;
}

.dropdowns {
    display: flex;
    flex-direction: column;
    width: 350px;
    margin-right: 20px;
}

.dropdowns label {
    margin-top: 10px;
    font-weight: bold;
}

.dropdowns select {
    margin-top: 5px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.items-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 3fr));
    gap: 30px;
    max-height: 740px;
    overflow-y: auto;
    padding-right: 5px;
}

.item-box {
    border: 1px solid #ddd;
    border-radius: 20px;
    width: 580px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    /* display: flex; */
    /* flex-direction: column; */
    /* justify-content: space-between; */
}

.padding {
    padding: 15px;



}

.item-header {
    display: flex;
    border-radius: 10px 10px 0px 0px;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    margin-bottom: 10px;
    color: #ffffff;
    background: linear-gradient(90deg, #FB751E 49.4%, #DBB800 100%);

}

.item-title {
    font-weight: bold;


    border-radius: 3px;
}

.item-contact {
    font-size: 0.8em;
    color: #ffffff;
    font-family: Inter;
    font-size: 17px;
    font-weight: 600;
    line-height: 24.2px;
    text-align: left;
}

.item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.item-content img {
    width: 218px;
    height: 105px;
}

.item-details {
    display: flex;
    flex-direction: column;
}

.item-details h3 {
    font-family: Inter;
    font-size: 24px;
    font-weight: 700;
    line-height: 29.05px;
    text-align: left;
}

.item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-button {
    background: linear-gradient(90deg, #B93426, #FB751E);
    color: #fff;
    padding: 5px 10px;
    border: none;
    border-radius: 18px;
    cursor: pointer;
}

.details-button {
    font-style: italic;
    color: #FB751E;
    background: none;
    border: #ddd;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

header h1 {
    margin: 0;
    padding: 10;
}

.search-add {
    display: flex;
    align-items: center;
}

.search-add input {
    padding: 5px;
    margin-right: 10px;
}

.add-button {
    background-color: #ff7f50;
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
}

.page:hover {
    background-color: #ccc;
}

.send-orders-container {
    padding: 40px 0 20px 0;
}

.header-row {
    width: 957px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-row-h1 {
    width: 274px;
    height: 39px;
    top: 149px;
    left: 403px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    color: #000;
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
}

.search-bar {
    position: relative;

    width: 604px;

    margin-left: 50%;

}

#search-input {
    padding: 10px;
    width: 100%;
    padding-left: 30px;
    border: 1px solid #ccc;
    border-radius: 30px;
    border-width: 30%;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

.sidebar-container {
    height: calc(10vh - 70px);
    width: 10%;
    overflow-y: auto;
    display: block;
}

@media (max-width: 768px) {

    .sidebar-container,
    .main-content {
        width: 100%;
        display: block;
    }
}

.service-card {
    /* width: 100%; */
    width: 957px;
    /* max-width: 900px; */
    /* margin-top: 4%; */
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    font-family: Arial, sans-serif;
}

.card-header {
    background: linear-gradient(to right, #ff7b00, #ffb600);
    color: white;
    height: 64px;
    /* top: 314.36px;
    left: 407.41px;
    gap: 0px; */
    /* opacity: 0px; */
}

.card-header h3 {
    margin-left: 1%;
}

/* .card-header h3 {
    margin-left: 2%;
} */

.card-body {
    display: flex;
    align-items: center;
    padding: 20px;
    color: #666666;
    background-color: white;
}

.card-text {
    flex: 1;
    padding-right: 20px;
}

.card-text h5 {
    margin-bottom: 1rem;
}

.card-text p strong {
    display: block;
    margin-top: 10px;
    font-size: 18px;
    color: #000;
}

.order-btn {
    margin-top: 4%;
    padding: 5px 40px;
    background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);
    color: #ffffff;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    margin-top: 10px;
}

.order-btn:hover {
    background-color: #ff9000;
}

.card-image img {
    width: 300px;
    height: auto;
    border-radius: 8px;
    margin-bottom: 10%;
}

.title {
    color: #D85322;
    font-family: Inter;
    font-size: 24px;
    font-weight: 600;
    line-height: 29.05px;
    text-align: left;
    margin-bottom: 40px;


}

.orders-btn {
    margin-top: 4%;
    padding: 5px 40px;
    background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);
    color: #ffffff;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    margin-top: 10px;
    margin-left: 2%;
}