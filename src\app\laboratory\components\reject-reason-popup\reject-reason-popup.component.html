<div class="popup-overlay">
    <div class="popup-box">
        <div class="popup-header">
            <button class="close-btn" (click)="onClose()">&#10005;</button>
        </div>
        <div class="popup-content">
            <div class="popup-title" style="display: flex; flex-direction: row; width: 100%; justify-content: start; font-size: 25px; font-weight: 600; margin-top: 40px;">Please provide a reason for rejection:</div>
            <textarea [(ngModel)]="rejectionReason" rows="4" cols="50" placeholder="Enter the reason..."
                class="reason-input" style="height: 200px; border-radius: 10px;"></textarea>
            <div class="popup-actions" style="display: flex; flex-direction: row; width: 100%; justify-content: end;">
                <button class="cancel-btn" style="border-radius: 20px;" (click)="onClose()">Cancel</button>
                <button class="submit-btn" style="border-radius: 20px;" (click)="submitReason()">Submit</button>
            </div>
        </div>
    </div>
</div>