import { Company } from "./company";

export class User {
  userId: number = 0;
  firstName: string = '';
  lastName: string = '';
  password: string = '';
  username: string = '';
  email: string = '';
  userType: string = '';
  companyId: Company = new Company();
  userCategoryId: UserCategory = new UserCategory();
  userVerified:UserStatus = UserStatus.INACTIVE;
}

export class UserCategory {
  userCategoryId: number = 0;
  userCategory: string = '';
  routerPath: string = '';
}

export enum UserStatus{
 ACTIVE,INACTIVE,OTHER
}

