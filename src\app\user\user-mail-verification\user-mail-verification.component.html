<div class="page-background">
  <div class="rec1"></div>
  <div class="rec2"></div>
  <div class="verification-container">
    <div class="verification-content">
      <h2>{{ userType }} Email Verification</h2>

      <div *ngIf="loading" class="loading-section">
        <p>Verifying your email...</p>
        <div class="loading-spinner">⏳</div>
      </div>

      <div *ngIf="!loading" class="verification-result">
        <ng-container *ngIf="message === 'Email verified successfully!'">
          <div class="success-message">
            <h3>✅ Success!</h3>
            <p>Thank you for verifying your email!<br>
            You can now access your account and start using all the features of our platform.</p>
          </div>
        </ng-container>

        <ng-container *ngIf="message === 'Your email is already verified.'">
          <div class="info-message">
            <h3>ℹ️ Already Verified</h3>
            <p>Your email is already verified.<br>
            You can now access your account and start using all the features of our platform.</p>
          </div>
        </ng-container>

        <ng-container *ngIf="message !== 'Email verified successfully!' && message !== 'Your email is already verified.'">
          <div class="error-message">
            <h3>⚠️ Verification Issue</h3>
            <p>{{ message }}</p>
            <p><small>If you continue to have issues, please contact support or try requesting a new verification email.</small></p>
          </div>
        </ng-container>
      </div>

      <button *ngIf="message === 'Email verified successfully!' || message === 'Your email is already verified.'"
              class="login-button"
              (click)="goToLogin()">
        Go to Login
      </button>

      <button *ngIf="message !== 'Email verified successfully!' && message !== 'Your email is already verified.' && !loading"
              class="retry-button"
              (click)="goToLogin()">
        Back to Login
      </button>
    </div>
  </div>
</div>
