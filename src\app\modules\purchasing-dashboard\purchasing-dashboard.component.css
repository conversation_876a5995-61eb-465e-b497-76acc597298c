.send-orders-container {
    padding: 20px;
}

.header-row,
.send-orders-list-row {
  font-size: 15px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    /* Allows wrapping of elements */
}

.send-orders-list-row-new {
    font-family: Inter;
    font-size: 24px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: left;
    color: #000;
    margin-bottom: 10px;
    /* Adds space between elements */
}

.see-all-button {
    width: 114px;
    height: 0px;
    margin-left: 90%;
    font-family: Inter;
    font-size: 15px;
    font-weight: 400;
    line-height: 18.15px;
    text-align: left;
    color: #D85322;
    background: none;
    border: #ddd;
}

.header-row-h1 {
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    color: #000;
    margin-bottom: 10px;
    /* Adds space between the title and other elements */
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
}

.search-bar {
    position: relative;
    margin: 20px 0;
    width: 100%;
    max-width: 300px;
}

#search-input {
    padding: 10px;
    width: 100%;
    padding-left: 30px;
    border: 1px solid #ccc;
    border-radius: 40px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

.send-order-table {
    width: 100%;
    border-radius: 10px;
    border-collapse: collapse;
    margin-top: 20px;
}

.send-order-table th,
.send-order-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

/* Add border-radius to the first row's first and last cells */
.send-order-table tr:first-child th:first-child {
  border-top-left-radius: 10px;
}

.send-order-table tr:first-child th:last-child {
  border-top-right-radius: 10px;
}

/* Ensure border-radius corners align with the table border */
.send-order-table tr:first-child th:first-child {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
}

.send-order-table tr:first-child th:last-child {
  border-right: 1px solid #ddd;
  border-top: 1px solid #ddd;
}

.send-order-table th {
    background-color: #FFB07D;
    color: rgb(0, 0, 0);
}

.send-order-table thead tr th {
    border-bottom: 2px solid #ddd;
}

.profile-icon {
    color: #FF5722;
    margin-right: 8px;
    vertical-align: middle;
}

.status {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
}

.completed {
    border-radius: 18px;
    border: 1px solid #00C820;
    color: #00C820;
    background: none;
}

.pending {
    border-radius: 18px;
    background: #FFC1070D;
    border: 1px solid #FFC107;
    color: #FFC107;
}

.received {
    border-radius: 40px;
    border: 1px solid;
    color: #0075FF;
    border-color: #0075FF;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    text-align: right;
}

.close-btn:hover,
.close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

form {
    display: flex;
    flex-direction: column;
}

form label {
    margin-top: 10px;
}

form input,
form select,
form textarea {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 5px;
}

.sidebar-container {
    height: calc(100vh - 80px);
    width: 20%;
    overflow-y: auto;
    display: inline-block;
}

.main-content {
    height: calc(100vh - 80px);
    width: 80%;
    overflow-y: auto;
    padding: 16px;
    display: inline-block;
}


/* Media Queries for Responsiveness */

@media (max-width: 1024px) {
    .sidebar-container {
        width: 25%;
    }
    .main-content {
        width: 75%;
    }
}

@media (max-width: 768px) {
    .sidebar-container {
        width: 100%;
        height: auto;
        display: block;
    }
    .main-content {
        width: 100%;
        padding: 10px;
    }
    .header-row {
        flex-direction: column;
        align-items: flex-start;
    }
    .search-bar {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .header-row-h1 {
        font-size: 24px;
    }
    .send-orders-list-row-new {
        font-size: 20px;
    }
    .see-all-button {
        font-size: 14px;
        margin-left: 0;
        /* Ensures proper alignment */
    }
    .send-order-table th,
    .send-order-table td {
        font-size: 12px;
        padding: 5px;
    }
    .search-bar {
        width: 100%;
    }
}
