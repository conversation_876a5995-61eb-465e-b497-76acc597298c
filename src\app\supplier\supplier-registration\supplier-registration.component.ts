import { Component, ElementRef, OnInit,ViewChild } from '@angular/core';
import {FormBuilder, FormGroup, NgForm, Validators} from "@angular/forms";
import { Supplier } from '../supplier';
import { Router } from '@angular/router';
import { SupplierService } from '../supplier.service';
import { map, mapTo, Observable, of, tap } from 'rxjs';
import { UserCategory, User } from 'src/app/user/user';
import { UserService } from 'src/app/user/user.service';
import { SharedService } from 'src/app/modules/shared-services/shared.service';
import Swal from 'sweetalert2';
import { UserTemp, UserTempType } from 'src/app/auth/auth';
import { AuthService } from 'src/app/auth/auth.service';

@Component({
  selector: 'app-supplier-registration',
  templateUrl: './supplier-registration.component.html',
  styleUrls: ['./supplier-registration.component.css'],
})
export class SupplierRegistrationComponent implements OnInit {
  supplierForm: FormGroup;
  supplier: Supplier = new Supplier();
  userCategory: UserCategory = new UserCategory();
  user: User = new User();
  isUserRegistered: boolean = false;
  // passwordDoNotMatch = false;
  isEmailRegistered: boolean = false;
  userEmailExistsMessage: string = '';
  isSupplierRegistered: boolean = false;
  // isCityDisabled: boolean = true;
  supplierNameExistsMessage: string = '';

  districts: string[] = [];
  cities: String[] = [];

    // User temp
    protected userTemp: UserTemp = new UserTemp();
    @ViewChild('RegisterButton') registerButton!: ElementRef<HTMLButtonElement>;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private userService: UserService,
    private authService: AuthService,
    private supplierService: SupplierService,
    private sharedService: SharedService
  ) {
    this.supplierForm = this.fb.group(
      {
        supplierName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],
        ],
        address: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        // district: ['', Validators.required],
        // city: ['', Validators.required],
        tele: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
        designation: ['', Validators.required],
        contactPerson: ['', Validators.required],
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            Validators.pattern('^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\W).*$'),
          ],
        ],
        rePassword: ['', Validators.required],
      },
      { validator: this.passwordMatchValidator }
    );
  }

  // onDistrictChange(event: Event): void {
  //   const selectedDistrict = (event.target as HTMLSelectElement).value;

  //   if (selectedDistrict) {
  //     this.supplerForm.get('city')?.enable();
  //     this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);
  //   } else {
  //     this.supplerForm.get('city')?.disable();
  //     this.cities = [];
  //   }
  //   this.supplerForm.get('city')?.setValue('');
  // }

  ngOnInit(): void {
    localStorage.clear();
    this.userService.getUserCategoryById(4).subscribe((response) => {
      this.userCategory = response;
    });
    this.districts = this.sharedService.getDistricts();
    this.userTemp.userTempType = UserTempType.SUPPLIER;
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const rePassword = form.get('rePassword');

    if (
      password?.value &&
      rePassword?.value &&
      (rePassword.dirty || rePassword.touched)
    ) {
      return password.value === rePassword.value ? null : { mismatch: true };
    }
    return null;
  }

  updateEmail() {
    this.user.username = this.supplier.email;
  }

  onUserRegister(): Observable<void> {

    this.user.userCategoryId = this.userCategory;
    this.user.firstName = this.supplier.name;

    return this.userService.register(this.user).pipe(
      tap(
        (response) => {
          this.user.userId = response.id;
          this.supplier.userId = this.user;
        },
        (error) => {
          console.log(error);
        }
      ),
      mapTo(void 0)
    );
  }

  onsupplierRegister(): Observable<void> {
    return this.supplierService.saveSupplier(this.supplier).pipe(
      tap(
        () => {
          Swal.fire({
            title: 'Registration Successful!',
            text: 'Thank you for registering! Please verify your email to complete the login process.',
            icon: 'success',
            confirmButtonText: 'OK', // Display the "OK" button
          }).then((result) => {
            if (result.isConfirmed) {
              // Redirect after the user clicks "OK"
              this.router.navigate(['/user-login']); // Replace with the appropriate route
            }
          });
        },
        (error) => {
          console.log(error);
          Swal.fire({
            title: 'Error',
            text: 'An error occurred during supplier registration. Please try again later.',
            icon: 'error',
            confirmButtonText: 'OK',
          });
        }
      ),
      mapTo(void 0)
    );
  }

  onSubmit() {

    // Swal.fire({
    //   title: "Wait until approval!",
    //   text: "Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.",
    //   icon: 'success',
    //   confirmButtonText: 'OK',
    // });

    if (this.supplierForm.invalid) {
      this.supplierForm.markAllAsTouched();
      return;
    }
    this.checkSupplierName().subscribe((isSupplierRegistered) => {
      if (!isSupplierRegistered) {
        this.checkUserEmail().subscribe((isEmailRegistered) => {
          if (!isEmailRegistered) {
            this.onUserRegister().subscribe(() => {
              this.onsupplierRegister().subscribe(() => {
                console.log('supplier registered successfully');
              });
            });
          }
        });
      }
    });
  }

      // UserTemp Saving
      onUserTempRegister() {
        if (this.supplierForm.invalid) {
          this.supplierForm.markAllAsTouched();
          return;
        }

        // Disable the register button and show a loading indicator
        this.registerButton.nativeElement.disabled = true;
        this.registerButton.nativeElement.innerHTML = `<img src="/assets/icons/more-30.png" />`;

        this.authService
          .checkUserTempAvailability(this.userTemp.userEmail)
          .subscribe((resp) => {

            if (resp !=null) {
              Swal.fire({
                title: 'Registration Already Exists!',
                text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',
                icon: 'info',
                confirmButtonText: 'OK',
              });

              // Reset the button state
              this.registerButton.nativeElement.disabled = false;
              this.registerButton.nativeElement.innerHTML = 'Register';
              return;
            }

            this.authService.saveUserTemp(this.userTemp).subscribe(
              (userTempSaved: UserTemp) => {
                console.log('Full userTempSaved object:', userTempSaved);

                const receivedUserTemp: UserTemp = userTempSaved;
                let title = 'Registration Completed!';
                let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';
                let iconName:
                  | 'success'
                  | 'info'
                  | 'error'
                  | 'warning'
                  | 'question' = 'success';

                if (!receivedUserTemp) {
                  title = 'Registration Failed!';
                  message ='An error occurred while registering. Please try again.';
                  iconName = 'error';
                }

                Swal.fire({
                  title: title,
                  text: message,
                  icon: iconName,
                  confirmButtonText: 'OK',
                });

                // Reset button state
                this.registerButton.nativeElement.disabled = false;
                this.registerButton.nativeElement.innerHTML = 'Register';
              },
              (error) => {
                Swal.fire({
                  title: 'Registration Failed!',
                  text: 'An error occurred during registration. Please try again later.',
                  icon: 'error',
                  confirmButtonText: 'OK',
                });

                this.registerButton.nativeElement.disabled = false;
                this.registerButton.nativeElement.innerHTML = 'Register';
              }
            );
          });
      }



  checkUserEmail(): Observable<boolean> {
    if (this.supplierForm.get('email')?.valid) {
      const userEmail = this.supplierForm.get('email')?.value;
      return this.userService.checkUser(userEmail).pipe(
        map((data) => {
          if (data) {
            this.isEmailRegistered = true;
            this.userEmailExistsMessage =
              'Email already registered. Try another.';
          } else {
            this.isEmailRegistered = false;
            this.userEmailExistsMessage = '';
          }
          return this.isEmailRegistered;
        })
      );
    } else {
      this.isEmailRegistered = false;
      this.userEmailExistsMessage = '';
      return of(this.isEmailRegistered);
    }
  }

  checkSupplierName(): Observable<boolean> {
    if (this.supplierForm.get('supplierName')?.valid) {
      const supplierName = this.supplierForm.get('supplierName')?.value;

      return this.supplierService.supplierNameExists(supplierName).pipe(
        map((data) => {
          if (data) {
            this.isSupplierRegistered = true;
            this.supplierNameExistsMessage = 'That name is taken. Try another.';
          } else {
            this.isSupplierRegistered = false;
            this.supplierNameExistsMessage = '';
          }
          return this.isSupplierRegistered;
        })
      );
    } else {
      this.isSupplierRegistered = false;
      this.supplierNameExistsMessage = '';
      return of(this.isSupplierRegistered);
    }
  }

  navigateUserSelection() {
    this.router.navigate(['/user-selection']);
  }
}
