import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SupplierRoutingModule } from './supplier-routing.module';
import { SupplierLayoutComponent } from './supplier-layout/supplier-layout.component';
import { SupplierDashboardComponent } from './supplier-dashboard/supplier-dashboard.component';
import { SupplierSidebarComponent } from './components/supplier-sidebar/supplier-sidebar.component';
import { SupplierNavbarComponent } from './components/supplier-navbar/supplier-navbar.component';
import { InventoryItemsComponent } from './supplier-inventory-items/supplier-inventory-items.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SupplierClinicOrdersComponent } from './supplier-clinic-orders/supplier-clinic-orders.component';
import { SupplierClinicOrderSingleViewComponent } from './supplier-clinic-order-single-view/supplier-clinic-order-single-view.component';
import { CoreModule } from '../core/core.module';

@NgModule({
  declarations: [
    SupplierLayoutComponent,
    SupplierDashboardComponent,
    SupplierSidebarComponent,
    SupplierNavbarComponent,
    InventoryItemsComponent,
    SupplierClinicOrdersComponent,
    SupplierClinicOrderSingleViewComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    SupplierRoutingModule,
    ReactiveFormsModule,
    CoreModule
  ]
})
export class SupplierModule { }
