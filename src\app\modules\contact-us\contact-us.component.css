@import url("https://fonts.googleapis.com/css2?family=Poppins&display=swap");

body {
  height: 100vh;
  font-family: "Poppins", sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
}

/* Background div styles */
#background {
  background-image: url("/assets/images/background.png");
  background-size: cover;
  background-position: center;
  background-color: #f9f9f9;
  min-height:91%;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

/* Container styles */
.container {
  width: 85%;
  max-width: 900px;
  background: #fff;
  border-radius: 6px;
  padding: 20px 60px 30px 40px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 60px;
}

.container .content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.container .content .left-side {
  width: 25%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  position: relative;
}

.content .left-side::before {
  content: '';
  position: absolute;
  height: 70%;
  width: 2px;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  background: #afafb6;
}

.content .left-side .details {
  margin: 14px;
  text-align: center;
}

.content .left-side .details i {
  font-size: 30px;
  color: #ff5722;
  margin-bottom: 10px;
}

.content .left-side .details .topic {
  font-size: 18px;
  font-weight: 500;
}

.content .left-side .details .text-one,
.content .left-side .details .text-two {
  font-size: 14px;
  color: #afafb6;
}

.container .content .right-side {
  width: 75%;
  margin-left: 75px;
}

.content .right-side .topic-text {
  font-size: 30px;
  font-weight: 600;
  color: #ff5722;
  font-weight: bold;
}

.Soical.Media {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
  
  .social-btn {
    color: #fff;
    font-size: 18px;
    margin: 0 5px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(to right, #fb751e, #b93426);
    text-decoration: none;
    transition: background 0.3s ease;
  }
  
  .social-btn:hover {
    background: linear-gradient(to left, #fb751e, #b93426);
  }
  
  .social-btn i {
    font-size: 18px;
  }

.right-side .input-box {
  height: 50px;
  width: 100%;
  margin: 12px 0;
}

.right-side label{
    font-size: 20px;
    font-weight: 600;
    color: #ff5722;
    font-weight: bold;
}

.right-side .input-box input,
.right-side .input-box textarea {
  height: 100%;
  width: 100%;
  border: none;
  outline: none;
  font-size: 16px;
  background: #f0f1f8;
  border-radius: 6px;
  padding: 0 15px;
  resize: none;
}

.right-side .message-box {
  min-height: 110px;
}

.right-side .input-box textarea {
  padding-top: 6px;
}

.right-side .button {
  display: flex;
  align-items: center;
}

.right-side .button input[type="submit"] {
  color: #fff;
  font-size: 18px;
  width: 50%;
  outline: none;
  border: none;
  padding: 8px 16px;
  margin-top: 50px;
  border-radius: 20px;
  background: linear-gradient(to right, #fb751e, #b93426);
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.button:hover input[type="submit"] {
  background: linear-gradient(to left, #fb751e, #b93426);
}

@media (max-width: 950px) {
  .container {
    width: 90%;
    padding: 30px 40px 40px 35px;
  }
  .container .content .right-side {
    width: 75%;
    margin-left: 55px;
  }
}

@media (max-width: 820px) {
  .container {
    margin: 40px 0;
    height: 100%;
  }
  .container .content {
    flex-direction: column-reverse;
  }
  .container .content .left-side {
    width: 100%;
    flex-direction: row;
    margin-top: 40px;
    justify-content: center;
    flex-wrap: wrap;
  }
  .container .content .left-side::before {
    display: none;
  }
  .container .content .right-side {
    width: 100%;
    margin-left: 0;
  }
}
