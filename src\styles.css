/* You can add global styles to this file, and also import other style files */
/*@import  "~node_modules/bootstrap/dist/css/bootstrap.min.css"*/
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
.fixed-top,
.sb-nav-fixed #layoutSidenav #layoutSidenav_nav,
.sb-nav-fixed .sb-topnav {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}
html,
body {
  height: 100%;
}
body {
  margin: 0;
  /* font-family: Roboto, "Helvetica Neue", sans-serif; */
  font-family: "Inter", sans-serif !important;
}

@media only screen and (max-width: 320px) and (min-width: 480px) and (min-resolution: 5dpi) {
  .card {
    font-size: 5px;
    background-color: red;
  }
  .header {
    font-size: 1px;
  }
}

@media only screen and (max-width: 481px) and (min-width: 768px) and (min-resolution: 5dpi) {
  .body {
    font-size: 5px;
  }
}

@media only screen and (max-width: 769px) and (min-width: 1024px) and (min-resolution: 5dpi) {
  .body {
    font-size: 1px;
  }
}

@media only screen and (max-width: 1025px) and (min-width: 1200px) and (min-resolution: 5dpi) and (max-resolution: 300dpi) {
  div {
    font-size: 5px;
  }
}

/*
@media screen and (min-width: 768px) {
  header nav {
    display: none;
  }
} */
/*
@media screen and (orientation: landscape){
  header nav {
    position: fixed;
    left: 0;
    width: 20vw;
  } */
/* } */

/* html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; } */
