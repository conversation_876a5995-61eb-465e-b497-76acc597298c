<div class="container-fluid">
    <div class="row">
        <div class="sidebar-container">
        </div>

        <div class="services-page">
            <header>
                <div class="send-orders-container">
                    <div class="header-row">
                        <div class="header-row-h1">Services</div>
                        <div class="search-bar">
                            <i class="bi bi-search search-icon"></i>
                            <input type="text" placeholder="Search Service" [(ngModel)]="searchTerm" (input)="filterItems()" id="search-input" />
                        </div>
                    </div>
                    <hr>
                </div>
            </header>

            <div class="content-wrapper">
                <aside class="dropdowns" *ngIf="!selectedItem">
                    <label for="dental-services">Select Dental Services</label>
                    <select id="dental-services" name="dental-services" [(ngModel)]="selectedDentalService"
                        (change)="onDentalServiceChange($event)">
                        <option *ngFor="let service of dentalServices" [value]="service">{{service}}</option>
                    </select>

                    <label for="sub-dental-services">Select Sub Dental Services</label>
                    <select id="sub-dental-services" name="sub-dental-services" [(ngModel)]="selectedSubDentalService"
                        (change)="onSubDentalServiceChange($event)">
                        <option *ngFor="let subService of subDentalServices" [value]="subService">{{subService}}
                        </option>
                    </select>

                    <label for="services">Select Services</label>
                    <select id="services" name="services" [(ngModel)]="selectedService"
                        (change)="onServiceChange($event)">
                        <option *ngFor="let service of services" [value]="service">{{service}}</option>
                    </select>
                </aside>

                <main class="items-container" *ngIf="!selectedItem">
                    <div class="item-box" *ngFor="let item of items">
                        <div class="item-header">
                            <span class="item-title">{{item.title}}</span>
                            <span class="item-contact">{{item.contact}}</span>
                        </div>
                        <div class="padding">
                            <div class="item-content">
                                <div class="item-details">
                                    <p>{{item.description}}</p>
                                    <h3>{{item.price}}</h3>
                                </div>
                                <div class="imageposition">
                                    <img [src]="item.image" alt="Item image">
                                </div>
                            </div>
                            <div class="item-footer">
                                <button class="order-button">Order Now</button>
                                <button class="details-button" (click)="viewMoreDetails(item)">
                                    View More Details
                                </button>
                            </div>
                        </div>
                    </div>
                </main>

                <div *ngIf="selectedItem" class="service-card">
                    <div class="title">
                        <h2>{{selectedItem.title}} <i class="fas fa-edit edit-icon"></i></h2>
                    </div>
                    <div class="service-card">
                        <div class="card-header">
                            <h3>{{selectedItem.description}}</h3>
                        </div>
                        <div class="card-body">
                            <div class="card-text">
                                <h5>Prepared using porcelains and ceramics</h5>
                                <p>
                                    Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                                    Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,
                                    when an unknown printer took a galley of type and scrambled it to make a type
                                    specimen book.
                                </p>
                                <p><strong>Price: {{selectedItem.price}}</strong></p>
                                <button class="order-btn">Order Now</button>
                                <button (click)="clearSelectedItem()" class="orders-btn">Back to Services</button>

                            </div>
                            <div class="card-image">
                                <img [src]="selectedItem.image" alt="Dental Crowns Image" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
