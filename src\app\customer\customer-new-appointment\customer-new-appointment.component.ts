import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { Appointment } from '../customer';
import { CustomerService } from '../customer.service';
import { AppointmentsService } from '../../modules/appointments/appointments.service';
import { Customer } from './../customer';
import { Clinic } from '../../clinic/clinic'

@Component({
  selector: 'app-customer-new-appointment',
  templateUrl: './customer-new-appointment.component.html',
  styleUrls: ['./customer-new-appointment.component.css']
})
export class CustomerNewAppoinmentComponent implements OnInit {

  appointments: Appointment = new Appointment();
  appointmentForm: FormGroup;
  currentStep: number = 1;
  steps: string[] = ['Service Details', 'Clinic Selection', 'Confirmation'];
  currentDate: string = this.getCurrentDate();
  isImmediateBooking: boolean = false;
  customer: Customer = new Customer();
  nearestCity: string = '';
  clinics: Clinic[] = [];
  selectedClinic: number | undefined;
  defaultDate: string = new Date().toISOString().substr(0, 10);

  private getCurrentDate(): string {
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!
    const yyyy = today.getFullYear();
    return `${yyyy}-${mm}-${dd}`;
  }

  constructor(
    private fb: FormBuilder,
    private appointmentsService:  AppointmentsService ,
    private customerService: CustomerService
  ) {
    const today = new Date();
    this.defaultDate = today.toISOString().substring(0, 10); 

    this.appointmentForm = this.fb.group({     
      preferredservice: [''],
      nearestCity: [{value: this.customer.city}],
      fromDate: [{ value: this.defaultDate, disabled: false }, Validators.required],
      toDate: [{ value: this.defaultDate, disabled: false }, Validators.required],
      fromTime: ['', Validators.required],
      toTime: ['', Validators.required],
      immediate: [false],
      
    },);

    this.appointmentForm.get('immediate')?.valueChanges.subscribe(value => {
      this.onImmediateBookingChange(value);
    });
  }

  onImmediateBookingChange(isImmediate: boolean): void {
    if (isImmediate) {
      const currentDate = new Date().toISOString().substring(0, 10); // Format as YYYY-MM-DD
      const currentTime = new Date().toTimeString().substring(0, 5); // Format as HH:MM

      // Update the form values for immediate booking
      this.appointmentForm.patchValue({
        fromDate: currentDate,
        toDate: currentDate, 
        fromTime: currentTime,
        toTime: '23:59' 
      });
    } else {     
      this.appointmentForm.patchValue({
        fromDate: '',
        toDate: '',
        fromTime: '',
        toTime: ''
      });
    }
  }

  onSubmit(): void {

  }


  ngOnInit(): void {
    const userIdString = localStorage.getItem('userid');
    const userId = userIdString ? parseInt(userIdString, 10) : null;
    if (userId !== null) {
      this.getCustomerDetailsByuserId(userId);
    } else {
      console.error('User ID is not available in localStorage.');
    }
  }

  getCustomerDetailsByuserId(userId: number): void {
    this.customerService.getCustomerDetailsByuserId(userId).subscribe(
      (data: Customer) => {
        console.log('Received customer:', data);
        this.customer = data;

        this.appointmentForm.controls['city'].setValue(this.customer.city);
      },
      (error) => {
        console.error('Error fetching customer:', error);
      }
    );
  }

  nextStep(): void {

    if (this.currentStep < this.steps.length) {
      this.currentStep++;
    }
    const fromDate = this.appointmentForm.get('fromDate')?.value;

    const date = new Date(fromDate);
    const dayOfWeek = date.getDay();
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayName = days[dayOfWeek]; 
    console.log('Selected Day:', dayName); 

    const fromTime = this.appointmentForm.get('fromTime')?.value;
    const nearestCity = this.customer.city;
    const preferredservice = this.appointmentForm.get('preferredservice')?.value;  

  
    console.log('API Call Parameters:', { dayName, fromTime, nearestCity, preferredservice });

    this.appointmentsService.findClinics(dayName, fromTime, nearestCity, preferredservice).subscribe((response) => {
      this.clinics = response;
      this.selectedClinic = undefined; // Reset selected clinic when new data is fetched
      console.log('Clinics loaded:', this.clinics); // Log clinics data
      
    });


  }

  isStepInvalid(): boolean {
    if (this.currentStep === 1) {
      return !!this.appointmentForm.get('preferredService')?.invalid ||
        !!this.appointmentForm.get('nearestCity')?.invalid ||
        !!this.appointmentForm.get('fromDate')?.invalid ||
        !!this.appointmentForm.get('fromTime')?.invalid ||
        !!this.appointmentForm.get('toTime')?.invalid;
    }

    return false;
  }


  prevStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  selectClinic(clinicId: number): void {
    this.selectedClinic = clinicId; // Update selected clinic on click
    console.log('Clinic selected:', clinicId);  // Log the clinic that was selected
    console.log('Current selected Clinic value:', this.selectedClinic);  
  }

  resetForm(): void {
    this.appointmentForm.reset();
    this.currentStep = 1;
  }

  cancelAppointment(): void {
    console.log('Appointment canceled.');
    this.resetForm();
  }

  completeAppointment(): void {
    console.log('Appointment completed.');
    this.resetForm();
  }


  saveAppointment(): void {
    const formValues = this.appointmentForm.value;
  
    const appointmentData = new Appointment();
    
    appointmentData.firstName = this.customer.firstName; 
    appointmentData.lastName = this.customer.lastName;
    appointmentData.address = this.customer.address; 
    appointmentData.city = this.customer.city;
    appointmentData.state = this.customer.state;
    appointmentData.district = this.customer.state;
    appointmentData.telephone = this.customer.telephone;
    appointmentData.email = this.customer.email;
    appointmentData.customer = this.customer;
    appointmentData.status = "Pending";
    appointmentData.preferredservice = formValues.preferredservice; 
    appointmentData.nearestCity = this.customer.city;
    appointmentData.fromDate = formValues.fromDate;
    appointmentData.toDate = formValues.toDate;
    appointmentData.fromTime = formValues.fromTime;
    appointmentData.toTime = formValues.toTime;
    appointmentData.clinics  = new Clinic();
    appointmentData.clinics.clinicId = this.selectedClinic!;
    appointmentData.userName = ""; 
    appointmentData.password = ""; 
    
    console.log("appointment data: ",appointmentData);
    this.customerService.saveCustomerAppointment(appointmentData).subscribe(
      (response) => {
        console.log('Appointment saved successfully:', response);
        this.resetForm(); 
      },
      (error) => {
        console.error('Error saving appointment:', error);
        alert('Failed to save appointment. Please try again later.');
      }
    );
  }
  

}
