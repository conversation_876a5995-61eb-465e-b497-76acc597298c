.download-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    background-color: rgb(237, 237, 237);
    padding-bottom: 20px;
    align-items: center;
    padding-top: 40px;
}

.ios {
    width: 40px;
    height: 40px;
}

.android {
    width: 40px;
    height: 40px;
}

.btn-ios,
.btn-android {
    flex-direction: row;
    display: flex;
    height: 70px;
    width: 170px;
    align-items: center;
    border: 4px;
    border-radius: 10px;
    border-style: solid;
    gap: 10px;
    color: white;
}

/* Specific styles for Android button */
.btn-android {
    border-color: rgb(192, 65, 11);
    /* Example green border color for Android */
    background-color: rgb(192, 65, 11);
    /* Example green background color for Android */
}

/* Specific styles for iOS button */
.btn-ios {
    border-color: rgb(192, 65, 11);
    /* Existing red border color for iOS */
    background-color: rgb(192, 65, 11);
    /* Existing red background color for iOS */
}

.content {
    display: flex;
    flex-direction: column;
    align-items: start;
}

.subTopic {
    font-size: 12px;
}

.topic {
    font-size: 20px;
    font-weight: bold;
}

@media (max-width: 1024px) {
    /* Add styles for smaller screens if needed */
}

@media (min-width: 640px) {
    .download-buttons {
        display: flex;
        flex-direction: row;
        gap: 50px;
        background-color: rgb(237, 237, 237);
        padding-left: 80px;
        padding-bottom: 20px;
    }
}