<!-- <div class="container-fluid">
  <div class="row">
    <div class="col-3">
      <app-side-bar></app-side-bar>
    </div>
    <div class="col-9">
      <div class="row">
        <div class="col-12">
          <app-profile-header></app-profile-header>
        </div>
      </div>
      <div class="row">

        <div class="col-12">
          <app-appointment></app-appointment>
        </div>
      </div>
    </div>
  </div>
</div> -->

<div class="container mt-3">
  <!-- 1st Row -->
  <div class="row mb-3">
    <div class="col-md-6 d-flex flex-column">
      <div id="appointment-prompt" class="flex-grow-1 d-flex flex-column">
        <div class="row mb-2">
          <div class="col">
            <p class="text-left" id="add-appointment-text">Add Appointment in your schedule now</p>
          </div>
        </div>
        <div class="row mt-auto">
          <div class="col text-right d-flex flex-row-reverse">
            <button class="btn btn-primary" id="make-appointment-btn" (click)="navigateMakeAppointment()">
              <i class="fa fa-arrow-circle-right mr-5"></i> Make Appointment
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6 d-flex flex-column">
      <div id="reminder-card" class="flex-grow-1 d-flex flex-column" *ngIf="getOldestAppointment() as oldestAppointment">
        <div class="row mb-2">
          <div class="col-10">
            <p id="reminder-text">Reminder</p>
          </div>
          <div class="col-2">
            <i class="fas fa-bell fa-3x"></i>
          </div>
        </div>
        <div class="row mt-auto">
          <div class="col">
          <div class="col-2 text-center">
            <div id="reminder-day">{{oldestAppointment.fromDate}}</div>
          </div>
          <div class="col-6">
            <p id="doctor-name">{{ oldestAppointment.clinics.name}}</p>
          </div>
        </div>
          <div class="col-4 text-center" id="appointment-time-box">
            <p id="appointment-time" class="pt-2">{{ oldestAppointment.fromTime}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 2nd Row -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="px-4" id="current-appointments">
        <div class="row mb-2">
          <div class="col-md-6">
            <div class="row">
              <div class="col" id="statement-heading">Your Current Appointment</div>
            </div>
            <div class="row">
              <div class="col">Overview of your newly appointments</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="row">
              <div class="col text-right d-flex flex-row-reverse" id="icon-buttons">
                <i class="fas fa-ellipsis-h"></i>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col">
                <input type="text" class="form-control" placeholder="Search" id="search-input" [(ngModel)]="searchTermIncomplete">
              </div>
            </div>
          </div>
        </div>
        <div class="row mb-2">
          <div class="col text-right">
            <button class="btn btn-danger mr-2"><i class="fas fa-trash"></i></button>&nbsp;
            <button class="btn btn-warning"><i class="fas fa-clock"></i></button>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="30%">Date</th>
                  <th width="55%">Appointment Details</th>
                  <th width="15%">Clinic Type</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let appointment of filteredIncompleteAppointments">
                  <td>{{ appointment.fromDate }} - {{ appointment.toDate }}</td>
                  <td>
                    <div>{{ appointment.appointmentId }}</div>
                    <div>{{ appointment.fromTime }} - {{ appointment.toTime }}
                    </div>
                    <button id="view-details-button" class="btn btn-sm mt-2">View Details</button>
                  </td>
                  <td>
                    <div>{{ appointment.clinics.name }}</div>
                    {{ appointment.preferredservice }}
                    <div class="mt-3">
                      <button id="show-accept" class="btn btn-warning btn-sm">
                       Status
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 3rd Row -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="px-4" id="previous-appointments">
        <div class="row mb-2">
          <div class="col-md-6">
            <div class="row">
              <div class="col" id="statement-heading">Previously Completed Appointments</div>
            </div>
            <div class="row">
              <div class="col">Overview of your newly appointments</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="row">
              <div class="col text-right d-flex flex-row-reverse">
                <i class="fas fa-ellipsis-h"></i>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col">
                <input type="text" class="form-control" placeholder="Search" id="search-input" [(ngModel)]="searchTermCompleted">
              </div>
            </div>
          </div>
        </div>
        <div class="row mb-2">
          <div class="col text-right">
            <button class="btn btn-danger mr-2"><i class="fas fa-trash"></i></button>&nbsp;
            <button class="btn btn-warning"><i class="fas fa-clock"></i></button>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="80%">Appointment Number</th>
                  <th width="20%" class="mx-auto">Actions</th>
                </tr>
              </thead>
              <tbody *ngFor="let appointment of filteredCompletedAppointments">
                <tr>
                  <td>
                    <div>{{ appointment.appointmentId }}</div>
                    <div><i class="fas fa-user"></i></div>
                  </td>
                  <td>
                    <div>{{ appointment.clinics.name }}</div>
                    <button id="view-details-button" class="btn btn-outline-orange btn-sm mt-2 mr-2">View
                      Details</button>
                    <button id="show-complete" class="btn btn-warning btn-sm mt-2">Complete</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>