import {Component, ViewChild} from '@angular/core';
import {SideBarComponent} from "../admin/components/side-bar/side-bar.component";

interface OrdersDashboard {
  orderID:string;
  date:string;
  time:string;
  laboratory:string;
  status:string;
}

@Component({
  selector: 'app-orders-dashboard',
  templateUrl: './orders-dashboard.component.html',
  styleUrls: ['./orders-dashboard.component.css']
})
export class OrdersDashboardComponent {

  @ViewChild(SideBarComponent) sideBar!: SideBarComponent;

  toggleSidebar(){
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement){
      sidebarElement.classList.toggle('open')
    }
  }

  orderDashboard: OrdersDashboard[] = [
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Pending' },
    // { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Pending' },
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Medi Laboratory',  status: 'Pending' },
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Pending' },
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Pending' },
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Medi Laboratory',  status: 'in Progress' },
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'in Progress' },
    // { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Medi Laboratory',  status: 'Cancelled' },
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Asia Laboratory',  status: 'Completed' },
    { orderID: '12455878', date: '01-05-2024 ', time: '10.32 am', laboratory: 'Medi Laboratory',  status: 'Completed' },

  ];

  searchTerm: string = '';
  // showModal: boolean = false;
  newOrderDashboard: OrdersDashboard = { orderID: '',date: '', time: '', laboratory: '',  status: 'Pending' };

  filteredOrderDashboard() {
    return this.orderDashboard.filter(orderDashboard =>
      orderDashboard.orderID.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  showAll = false;

}
