import { Injectable } from '@angular/core';
import { Appointment } from './customer';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Customer } from './customer';

@Injectable({
  providedIn: 'root'
})

export class CustomerService {

  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.localStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  getAppointmentsByUserId(id: number): Observable<Appointment []> {
    return this.request('GET', `/getAppointmentsByCustomerId/${id}`, {});
  }

  getCustomerDetailsByuserId(id: number): Observable<Customer>{
    return this.request('GET', `/getCustomerByuserId/${id}`, {});
  }

  saveCustomerAppointment(appointmentData: Appointment): Observable<any>{
    return this.request('POST', `/saveCustomerAppointment/${appointmentData}`, {});
  }

}
