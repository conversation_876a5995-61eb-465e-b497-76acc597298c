{
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "ng serve",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "npm: start",
      "url": "http://localhost:4200/"
    },
    {
      "name": "ng test",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "npm: test",
      "url": "http://localhost:9876/debug.html"
    },
    {
      "type": "pwa-msedge",
      "name": "Launch Microsoft Edge",
      "request": "launch",
      "runtimeArgs": [
        "--remote-debugging-port=9222"
      ],
      "url": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-edgedevtools.vscode-edge-devtools-2.1.6\\out\\startpage\\index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    },
    {
      "type": "pwa-msedge",
      "name": "Launch Microsoft Edge in headless mode",
      "request": "launch",
      "runtimeArgs": [
        "--headless",
        "--remote-debugging-port=9222"
      ],
      "url": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-edgedevtools.vscode-edge-devtools-2.1.6\\out\\startpage\\index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    },
    {
      "type": "vscode-edge-devtools.debug",
      "name": "Open Edge DevTools",
      "request": "attach",
      "url": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-edgedevtools.vscode-edge-devtools-2.1.6\\out\\startpage\\index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    }
  ],
  "compounds": [
    {
      "name": "Launch Edge Headless and attach DevTools",
      "configurations": [
        "Launch Microsoft Edge in headless mode",
        "Open Edge DevTools"
      ]
    },
    {
      "name": "Launch Edge and attach DevTools",
      "configurations": [
        "Launch Microsoft Edge",
        "Open Edge DevTools"
      ]
    }
  ]
}
