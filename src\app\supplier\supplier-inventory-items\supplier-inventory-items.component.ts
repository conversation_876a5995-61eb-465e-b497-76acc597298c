import { Supplier, SupplierCategoryItemList, SupplierInventory } from './../supplier';
import { Component,ElementRef,ChangeDetectorRef, ViewChild } from '@angular/core';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { SupplierService } from '../supplier.service';
import { HttpErrorResponse } from '@angular/common/http';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-inventory-items',
  templateUrl: './supplier-inventory-items.component.html',
  styleUrls: ['./supplier-inventory-items.component.css']
})
export class InventoryItemsComponent {
  @ViewChild('fileInput') fileInput!: ElementRef;
  protected supplierInventoryForm: FormGroup;
  protected submitted = false;
  protected uploadedImages: string[] = []; // Holds base64 image data
  protected supplierItemCategoryList:SupplierCategoryItemList[] = [
  { id: 1, name: "Dental Chairs and Accessories" },
  { id: 2, name: "Dental Equipment" },
  { id: 3, name: "Dental Instruments" },
  { id: 4, name: "Dental Implants" },
  { id: 5, name: "Impression Materials" },
  { id: 6, name: "Filling/Bonding and Cement Materials" },
  { id: 7, name: "Endodontic Materials and Items" },
  { id: 8, name: "Rotary Accessories" },
  { id: 9, name: "Medicaments" },
  { id: 10, name: "Reusable Items and Materials" },
  { id: 11, name: "Orthodontic Materials and Items" },
  { id: 12, name: "Digital Dental Items" }
];

  supplierInventoryData: SupplierInventory = new SupplierInventory();

  constructor(
    private fb: FormBuilder,
    private supplierService: SupplierService,
    private router: Router,
    private cdr: ChangeDetectorRef,
  ) {
    this.supplierInventoryForm = this.fb.group({
      category: ['', Validators.required],
      subCategory: ['', Validators.required],
      description: ['', [Validators.required, Validators.minLength(5)]],
      quantity: [null, [Validators.required, Validators.min(1)]],
      price: [null, [Validators.min(1)]]
    });

  }

  // Trigger the hidden file input
  triggerFileInput() {
    this.fileInput.nativeElement.value = ''; // Reset file input
    this.fileInput.nativeElement.click();
  }


  // Handle selected files
  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      const selectedFiles = Array.from(input.files);

      selectedFiles.forEach((file) => {
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>) => {
          const base64 = e.target?.result as string;
          this.uploadedImages.push(base64); // Add the base64 string to the array
          this.cdr.detectChanges(); // Trigger change detection to update the view
        };
        reader.readAsDataURL(file);
      });
    }
  }



  // Remove an image from the preview
  removeImage(index: number) {
    this.uploadedImages.splice(index, 1); // Corrected to remove only one item
    this.cdr.detectChanges(); // Manually trigger change detection
  }




  hello(){
    if (this.supplierInventoryForm.invalid) {
        this.supplierInventoryForm.markAllAsTouched();
        console.log('Form Data:');
        console.table(this.supplierInventoryData);

      }else{
        console.log('Form is invalid');
      }
  }

  saveSupplierInventory() {
    const userId = +(localStorage.getItem('userid') || '');

    // Fetch supplier information using userId
    this.supplierService.getSupplierByUserId(userId).subscribe(
      (response: any) => {
        if (response && response.supplierId) {
          // Set the supplier ID in the supplierInventoryData object
          this.supplierInventoryData.supplierId = { supplierId: response.supplierId } as Supplier;

          // Prepare the FormData object to send as multipart
          const formData = new FormData();

          // Append all non-image data as parameters
          formData.append('supplierId', this.supplierInventoryData.supplierId.supplierId.toString());
          formData.append('category', this.supplierInventoryData.category);
          formData.append('subCategory', this.supplierInventoryData.subCategory);
          formData.append('description', this.supplierInventoryData.description);
          formData.append('price', this.supplierInventoryData.price.toString());
          formData.append('quantity', this.supplierInventoryData.quantity.toString());
          formData.append('itemStatus', this.supplierInventoryData.itemStatus);

          // If an image is uploaded, append it as a file
          if (this.uploadedImages && this.uploadedImages.length > 0) {
            const imageBlob = this.dataURItoBlob(this.uploadedImages[0]); // Convert base64 to blob
            formData.append('itemImage', imageBlob, 'itemImage.jpg'); // Append the image as 'itemImage'
          } else {
            // If no image is uploaded, you can append an empty field or handle as needed
            formData.append('itemImage', '');
          }

          // Save the supplier inventory data using multipart/form-data
          this.supplierService.saveSupplierInventory(formData).subscribe(
            (saveResponse: any) => {
              console.log('Inventory saved successfully:', saveResponse);
              Swal.fire('Success', 'Inventory saved successfully!', 'success');
            },
            (error: HttpErrorResponse) => {
              console.error('Error saving inventory:', error);
              Swal.fire('Error', 'Failed to save inventory. Please try again.', 'error');
            }
          );
        }
      },
      (error: HttpErrorResponse) => {
        console.error('Error fetching supplier:', error);
        Swal.fire('Error', 'Failed to fetch supplier information. Please try again.', 'error');
      }
    );
  }

// Helper function to convert base64 image to a Blob
  dataURItoBlob(dataURI: string): Blob {
    const byteString = atob(dataURI.split(',')[1]);
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const uintArray = new Uint8Array(arrayBuffer);

    for (let i = 0; i < byteString.length; i++) {
      uintArray[i] = byteString.charCodeAt(i);
    }

    return new Blob([arrayBuffer], { type: mimeString });
  }








}
