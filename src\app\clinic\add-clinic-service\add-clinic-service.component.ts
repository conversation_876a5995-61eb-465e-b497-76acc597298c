import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { ClinicServices } from '../clinic';
import { ClinicService } from '../clinic.service';

@Component({
  selector: 'app-add-clinic-service',
  templateUrl: './add-clinic-service.component.html',
  styleUrls: ['./add-clinic-service.component.css']
})
export class AddClinicServiceComponent implements OnInit  {

  addClinicServices:  ClinicServices = new  ClinicServices();
  AddClinicServicesList:   ClinicServices[]= [];
  dataSource: any;
  isLoading:any =0;
  displayedColumns: string[] = [ "clinicServiceCategoryName"];

  @ViewChild(MatSort) sort !: MatSort;
  @ViewChild(MatPaginator) paginator !: MatPaginator;
  @ViewChild('f') formReset: any;

  constructor(private clinicService: ClinicService, private router: Router) { }

  ngOnInit(): void {
    this.getAddservicesList();

  }

  onSubmit(){

    this.saveAddservices();

  }

  getAddservicesList() {
    this.isLoading = 1;
    this.clinicService.getAllClinicServicesCategory().subscribe(data => {
      this.AddClinicServicesList = data;
      this.isLoading = 0;
      this.dataSource = new MatTableDataSource<ClinicServices>(this.AddClinicServicesList);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    });
  }
  saveAddservices() {

    this.clinicService.saveClinicServicesCategory(this.addClinicServices).subscribe(
      (data) => {
        Swal.fire({
          title: 'Success!',
          text: ' Clinic Services Category saved successfully.',
          imageUrl: '././assets/img/Stax -10000.png',
          imageWidth: 700,
          imageHeight: 150,
          padding: 0,
          confirmButtonText: 'OK',
          confirmButtonColor: '#ff7e5f',
        }).then((result) => {
          if (result.isConfirmed) {

          }
        });


         this.formReset.resetForm();
         this.getAddservicesList();

      },
      (error: any) => {
        console.error(error);
        Swal.fire({
          title: 'error!',
          text: 'Failed to save Clinic Services Category.',
          imageUrl: '././assets/img/Stax -20000.png',
          imageWidth: 700,
          imageHeight: 150,
          confirmButtonText: 'OK',
          confirmButtonColor: '#be0032',
        });
      }
    );

  }
  Filterchange(event: Event) {
    const filvalue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filvalue;
  }
}
