import { User } from "src/app/user/user";


export class Doctor {
  doctorId: number = 0;
  doctorCategories: DoctorCategories = new DoctorCategories();
  userId: User = new User();
  title: string = '';
  firstName: string = '';
  lastName: string = '';
  regNo: string = '';
  telephone: string = '';
  email: string = '';
}

export class DoctorCategories {
  doctorCategoryId: number = 0;
  doctorCategory: string = '';
}

export class DoctorClinics {
  clinicId: number = 0;
  clinicName: string = '';
  clinicAddress: string = '';
  clinicCity: string = '';
}

export interface DoctorAppointment {
  patientName: string;
  clinicAddress: string;
  time: string;
  date: string;
  appNo: number;
  status: string;
}
