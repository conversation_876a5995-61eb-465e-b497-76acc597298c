
cdk-virtual-scroll-viewport {
  height: 75vh;
}
.card-striped > tbody > tr:hover,
.card-striped > tbody > tr:hover {
  background-color: #cff5ff;
}

.table-striped > tbody > tr:hover > td,
.table-striped > tbody > tr:hover > th {
  background-color: #cff5ff;
}

.vehicle-make{
  position: absolute;
    top: 1.14%;
    left: 0%;
    text-transform: capitalize;
    font-weight: 600;
}

h5 {
  padding-bottom: 0.25pt;
  margin: 0.25px;
}

@media screen and (max-width: 540px) {
  .section,
  .card,
  .table {
    flex-direction: column;
  }

  .table {
    max-width: 520px;
  }
}

/* For Tablets */
@media screen and (min-width: 540px) and (max-width: 780px) {
  .section,
  .card,
  .table {
    flex-direction: column;
  }
  .table {
    max-width: 520px;
  }
}

table {
  width: 100%;
}

th.mat-sort-header-sorted {
  color: black;
}

.example-container {
  height: 536px;
  overflow: auto;
}
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}
.mat-row {
  height: auto;
}
.mat-cell {
  padding: 20px 20px 20px 0;
}

.col-md-12{
  margin-bottom: 20px;
}



.btn-add-company {
  background: linear-gradient(90deg, #ff7e5f, #feb47b);
  border: none;
  color: white;
  height: 38px;
  width: 185px ;

}

.btn-add-company:hover {
  background: none;
  border: 1px solid #feb47b;
  color: #ff7e5f;
}

.btn-cancel {
  background: none;
  border: 1px solid #feb47b;
  color: #ff7e5f;
  height: 38px;
  width: 185px ;
}

.btn-cancel:hover {
  background: #ff7e5f;
  color: white;

}

.btn-orange {
  background-color:  #ff9800;
  border-color: #ff9800;
  color: white;
}

.btn-orange:hover {
  background-color: #feb47b;
  border-color: #feb47b;
  color: white;
}


.form-control[type="file"]::-webkit-file-upload-button {
  background-color:  #ff9800;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
