/* Background overlay */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    /* Semi-transparent black */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1200;
    /* Ensure it is on top */
}

/* Popup box styling */
.popup-box {
    background-color: white;
    border-radius: 10px;
    width: 700px;
    height: 520px;
    padding: 20px;
    padding-inline: 60px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1201;
    /* Ensure popup is above the overlay */
}

/* Header area for close button */
.popup-header {
    display: flex;
    justify-content: flex-end;
}

/* Close button styling */
.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
}

/* Popup title */
.popup-title {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 20px;
}

/* Textarea for rejection reason */
.reason-input {
    width: 100%;
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
    resize: none;
    margin-bottom: 20px;
}

/* Popup action buttons */
.popup-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

/* Button styles */
.cancel-btn,
.submit-btn {
    width: 120px;
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
}

.cancel-btn {
    background-color: gray;
    color: white;
    border: none;
}

.submit-btn {
    background-color: #FB751E;
    background-image: linear-gradient(to right, #FB751E, #B93426);
    color: white;
    border: none;
}