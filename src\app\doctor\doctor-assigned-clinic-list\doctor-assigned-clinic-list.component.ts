import { Component, OnInit } from '@angular/core';
import { DoctorService } from '../doctor.service';
import { DoctorClini<PERSON> } from '../doctor';
import { Router } from '@angular/router';

@Component({
  selector: 'app-doctor-assigned-clinic-list',
  templateUrl: './doctor-assigned-clinic-list.component.html',
  styleUrls: ['./doctor-assigned-clinic-list.component.css'],
})
export class DoctorAssignedClinicListComponent implements OnInit {

  protected clinicList: DoctorClinics[] = [];

  constructor(private doctorServices: DoctorService, private router:Router) {}

  ngOnInit(): void {
    // Get the Clinic List From Backend
    this.onLoadClinics();
  }

  onLoadClinics(){
    this.doctorServices.getClinicListByUserId().subscribe((response)=>{
      if (response) {
        this.clinicList = response;
      }
    });
  }

  public viewAppoinmentList(clinicId:number,clinicName:String){
    // Forward this to view the appointment list
    alert("Id : "+clinicId +" / Name : "+clinicName)
  }

}
