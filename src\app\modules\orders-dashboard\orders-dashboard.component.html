<app-profile-header [userName]="'<PERSON>'" [userProfilePic]="'../../assets/images/pro-pic.png'" (toggleSidebarEvent)="toggleSidebar()">
</app-profile-header>

<div class="container-fluid">
  <div class="row">
    <div class="sidebar-container">
      <app-side-bar #sideBar></app-side-bar>
    </div>
    <div class="main-content">
      <div class="orders-container">
        <div class="header-row">
          <div class="header-row-h1">
            Orders
          </div>
          <div class="search-bar">
            <i class="bi bi-search search-icon"></i>
            <input type="text" placeholder="Search" [(ngModel)]="searchTerm" id="search-input" />
          </div>
        </div>
        <div class="header-bottom-line"></div>
        <br>
        <div>
          <div class="send-orders-list-row-new">
            Your Orders
          </div>
          <div>
            <button (click)="showAll = !showAll" class="see-all-button">See All></button>
          </div>
        </div>
        <table class="send-order-table">
          <thead>
              <tr>
                <th> OrderID </th>
                <th> Date </th>
                <th> Time </th>
                <th> Laboratory </th>
                <th> Status </th>
              </tr>
          </thead>
          <tbody>
          <tr *ngFor="let purchasingDashboard of filteredOrderDashboard()">
            <td>
              <input type="checkbox"> {{ purchasingDashboard.orderID }}
            </td>
            <td>{{ purchasingDashboard.date}}</td>
            <td>{{ purchasingDashboard.time }}</td>
            <td>{{ purchasingDashboard.laboratory}}</td>
            <td class="status-cell">
              <button [ngClass]="{'status pending': purchasingDashboard.status === 'Pending',
                                  'status completed': purchasingDashboard.status === 'Completed',
                                  'status in-progress': purchasingDashboard.status === 'in Progress',
                                  'status cancelled': purchasingDashboard.status === 'Cancelled',
                                  }">{{ purchasingDashboard.status }}</button>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
