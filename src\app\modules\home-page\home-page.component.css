body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
}

app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.cover-page {
  /* background-image: url("/assets/images/back-home.png"); */
  position: relative;
    width: 100%;
    height: 100vh;
    padding-top: 8%;
  background-color: white;
}

.color-shape.bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 900px;
  /* Adjust width based on your image size */
  height: 600px;
  /* Adjust height based on your image size */
  background-image: url("/assets/images/bgImage.png");
  /* Add the path to your image */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom right;
}


.text-section {
  z-index: 1;
  margin-left: 5%;
  position: relative;
}

.gradient-text {
  /* background: linear-gradient(to right, #fb751e, #70190f, #fb751e, #70190f); */
  background: linear-gradient(
    268.23deg,
    #000000 3.24%,
    #d85322 34.72%,
    #000000 66.19%,
    #fb751e 97.67%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 40px;
  font-weight: 700;
  margin: 10px 0;
  background-clip: text;
}

h4 {
  color: #555;
  font-size: 32px;
  font-style: italic;
  margin: 30px 0;
  font-weight: 300;
}

.appointment-button {
  width: 329px;
  height: 52px;
  background: linear-gradient(to right, #333333, #fb751e);
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 23px;
  display: flex;
  align-items: center;
}

.appointment-button .arrow {
  width: 40px;
  height: 40px;
}

.btn-text {
  font-size: 18px;
  font-weight: 700;
  margin-left: 30px;
}

@media (max-width:1440px){
  .cover-page{
    height: 90vw;
  }
}


@media (max-width:1100px){
.gradient-text {
  /* background: linear-gradient(to right, #fb751e, #70190f, #fb751e, #70190f); */
  background: linear-gradient(
    268.23deg,
    #000000 3.24%,
    #d85322 34.72%,
    #000000 66.19%,
    #fb751e 97.67%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size:4.3vw;
  font-weight: 700;
  margin: 10px 0;
  background-clip: text;
}
.cover-page{
  height:110vw;
}
.color-shape.bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  /* Adjust width based on your image size */
  height: 600px;
  /* Adjust height based on your image size */
  background-image: url("/assets/images/bgImage.png");
  /* Add the path to your image */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom right;
}


}

@media (max-width:1365px){
  .register-now-button {
    width: 287px;
    height: 52px;
    background: none;
    border: 2px solid #fb751e;
    color: #fb751e;
    font-size: 20px;
    font-weight: bold;
    border-radius: 23px;
    cursor: pointer;
    margin-top: 10px;
    right: 0;
    top: 0;
    margin-right: 5%;
    margin-bottom: 30px;
  }
}

@media (max-width:1365px){

  .register-now-button {
    width: 287px;
    height: 52px;
    background: none;
    border: 2px solid #fb751e;
    color: #fb751e;
    font-size: 20px;
    font-weight: bold;
    border-radius: 23px;
    cursor: pointer;
    margin-top: 10px;
    right: 0;
    top: 0;
    margin-right: 5%;
    margin-bottom: 30px;
  }

    .color-shape.bottom-right {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      /* Adjust width based on your image size */
      height: 600px;
      /* Adjust height based on your image size */
      background-image: url("/assets/images/bgImage.png");
      /* Add the path to your image */
      background-size: contain;
      background-repeat: no-repeat;
      background-position: bottom right;
    }
  
}


@media (min-width:1366px){

  .register-now-button {
    width: 287px;
    height: 52px;
    background: none;
    border: 2px solid #fb751e;
    color: #fb751e;
    font-size: 20px;
    font-weight: bold;
    border-radius: 23px;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    margin-right: 5%;
  }
  /* .cover-page{
    height: 47.473vw;
  } */
  
}

@media (max-width:940px){
.text-section {
  z-index: 1;
  margin-left: 5%;
  position: relative;
  padding-top: 5%;
}
.cover-page{
  height: 135vw;
  
}
h4 {
  color: #555;
  font-size: 29px;
  font-style: italic;
  margin: 30px 0;
  font-weight: 300;
}

.color-shape.bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  /* Adjust width based on your image size */
  height: 600px;
  /* Adjust height based on your image size */
  background-image: url("/assets/images/bgImage.png");
  /* Add the path to your image */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom right;
}

}

@media (max-width:700px){
  .gradient-text {
    /* background: linear-gradient(to right, #fb751e, #70190f, #fb751e, #70190f); */
    background: linear-gradient(
      268.23deg,
      #000000 3.24%,
      #d85322 34.72%,
      #000000 66.19%,
      #fb751e 97.67%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size:4.8vw;
    font-weight: 700;
    margin: 10px 0;
    background-clip: text;
  }
  .text-section {
    z-index: 1;
    margin-left: 5%;
    position: relative;
    padding-top: 10%;
  }
  h4 {
    color: #555;
    font-size: 4.5vw;
    font-style: italic;
    margin: 30px 0;
    font-weight: 300;
  }
  .cover-page{
    height:160vw;
  }
    .color-shape.bottom-right {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      /* Adjust width based on your image size */
      height: 600px;
      /* Adjust height based on your image size */
      background-image: url("/assets/images/bgImage.png");
      /* Add the path to your image */
      background-size: contain;
      background-repeat: no-repeat;
      background-position: bottom right;
    }
  
}


@media(max-width:500px){
.text-section {
  z-index: 1;
  margin-left: 5%;
  position: relative;
  padding-top: 20%;
}
.gradient-text {
  /* background: linear-gradient(to right, #fb751e, #70190f, #fb751e, #70190f); */
  background: linear-gradient(
    268.23deg,
    #000000 3.24%,
    #d85322 34.72%,
    #000000 66.19%,
    #fb751e 97.67%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size:5.7vw;
  font-weight: 700;
  margin: 10px 0;
  background-clip: text;
}
.btn-text {
  font-size: 4vw;
  font-weight: 700;
  margin-left: 30px;
}
.appointment-button {
  width: 80%;
  height: 48px;
  background: linear-gradient(to right, #333333, #fb751e);
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 23px;
  display: flex;
  align-items: center;
}


.appointment-button .arrow {
  width: 38px;
  height: 38px;
}
h4 {
  color: #555;
  font-size: 5.5vw;
  font-style: italic;
  margin: 30px 0;
  font-weight: 300;
}
.register-now-button {
  width: 80%;
  height: 48px;
  background: none;
  border: 2px solid #fb751e;
  color: #fb751e;
  font-size: 4.8vw;
  font-weight: bold;
  border-radius: 23px;
  cursor: pointer;
  margin-top: 10px;
  right: 0;
  top: 0;
  margin-right: 5%;
  margin-bottom: 30px;
}
.cover-page{
  height: 200vw;
}

.color-shape.bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  /* Adjust width based on your image size */
  height: 600px;
  /* Adjust height based on your image size */
  background-image: url("/assets/images/bgImage.png");
  /* Add the path to your image */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom right;
}

}