import { Component, OnInit } from '@angular/core';
import { _MatTableDataSource, MatTableDataSource } from '@angular/material/table';
import { DoctorService } from '../doctor.service';
import { DoctorAppointment } from '../doctor';

@Component({
  selector: 'app-doctor-appointment-list',
  templateUrl: './doctor-appointment-list.component.html',
  styleUrls: ['./doctor-appointment-list.component.css']
})
export class DoctorAppointmentListComponent implements OnInit {
  dataSource: MatTableDataSource<DoctorAppointment> = new _MatTableDataSource();
  isLoading: any = 0;
  APPOINTMENT_DATA: DoctorAppointment[] = [];
  displayedColumns: string[] = ['patientName', 'clinicAddress', 'time', 'date', "appNo",'status'];

  constructor(private doctorService : DoctorService){}

  ngOnInit(): void {

    // Once the clinic assign a doctor to an appointment, uncomment this

    // this.doctorService.getMyAppointmentList().subscribe((resp)=>{
    //   if (resp) {
    //     this.APPOINTMENT_DATA = resp;
    //   }else{
    //     this.APPOINTMENT_DATA = [
    //       { patientName: 'John Doe', clinicAddress: '123 Main St', time: '10:00 AM', date: '2024-10-15', appNo: 101, status: 'Confirmed' },
    //       { patientName: 'Jane Smith', clinicAddress: '456 Oak St', time: '11:30 AM', date: '2024-10-15', appNo: 102, status: 'Pending' },
    //       { patientName: 'Michael Brown', clinicAddress: '789 Pine St', time: '1:00 PM', date: '2024-10-16', appNo: 103, status: 'Rejected' },
    //       { patientName: 'Lisa White', clinicAddress: '135 Maple St', time: '2:30 PM', date: '2024-10-16', appNo: 104, status: 'Confirmed' },
    //       { patientName: 'Jane Smith', clinicAddress: '456 Oak St', time: '11:30 AM', date: '2024-10-15', appNo: 102, status: 'Pending' },
    //       { patientName: 'Michael Brown', clinicAddress: '789 Pine St', time: '1:00 PM', date: '2024-10-16', appNo: 103, status: 'Rejected' },
    //       { patientName: 'Lisa White', clinicAddress: '135 Maple St', time: '2:30 PM', date: '2024-10-16', appNo: 104, status: 'Confirmed' },
    //       { patientName: 'Jane Smith', clinicAddress: '456 Oak St', time: '11:30 AM', date: '2024-10-15', appNo: 102, status: 'Pending' },
    //       { patientName: 'Michael Brown', clinicAddress: '789 Pine St', time: '1:00 PM', date: '2024-10-16', appNo: 103, status: 'Rejected' },
    //       { patientName: 'Lisa White', clinicAddress: '135 Maple St', time: '2:30 PM', date: '2024-10-16', appNo: 104, status: 'Confirmed' },
    //     ];
    //   }
    // });


    // Load this Dat tho the List from backend and assign them tot the 'APPOINTMENT_DATA'
    const APPOINTMENT_DATA: DoctorAppointment[] = [
      { patientName: 'John Doe', clinicAddress: '123 Main St', time: '10:00 AM', date: '2024-10-15', appNo: 101, status: 'Confirmed' },
      { patientName: 'Jane Smith', clinicAddress: '456 Oak St', time: '11:30 AM', date: '2024-10-15', appNo: 102, status: 'Pending' },
      { patientName: 'Michael Brown', clinicAddress: '789 Pine St', time: '1:00 PM', date: '2024-10-16', appNo: 103, status: 'Rejected' },
      { patientName: 'Lisa White', clinicAddress: '135 Maple St', time: '2:30 PM', date: '2024-10-16', appNo: 104, status: 'Confirmed' },
      { patientName: 'Jane Smith', clinicAddress: '456 Oak St', time: '11:30 AM', date: '2024-10-15', appNo: 102, status: 'Pending' },
      { patientName: 'Michael Brown', clinicAddress: '789 Pine St', time: '1:00 PM', date: '2024-10-16', appNo: 103, status: 'Rejected' },
      { patientName: 'Lisa White', clinicAddress: '135 Maple St', time: '2:30 PM', date: '2024-10-16', appNo: 104, status: 'Confirmed' },
      { patientName: 'Jane Smith', clinicAddress: '456 Oak St', time: '11:30 AM', date: '2024-10-15', appNo: 102, status: 'Pending' },
      { patientName: 'Michael Brown', clinicAddress: '789 Pine St', time: '1:00 PM', date: '2024-10-16', appNo: 103, status: 'Rejected' },
      { patientName: 'Lisa White', clinicAddress: '135 Maple St', time: '2:30 PM', date: '2024-10-16', appNo: 104, status: 'Confirmed' },
    ];

    this.dataSource.data = APPOINTMENT_DATA;
  }

  onConfirm(appointmentNumber:Number){
    alert("Confirmed : "+appointmentNumber)
  }

  onRejection(appointmentNumber:Number){
    alert("Rejected : "+appointmentNumber)
  }

}
