import { Component, OnInit } from '@angular/core';

interface Supplier {
  name: string;
  address: string;
  phone1: string;
  phone2: string;
}

interface Item {
  id: string;
  name: string;
  price: number;
  stock: number;
  image: string;
}

interface CartItem {
  item: Item;
  quantity: number;
  selected?: boolean;
}

@Component({
  selector: 'app-purchase',
  templateUrl: './purchase.component.html',
  styleUrls: ['./purchase.component.css']
})
export class PurchaseComponent implements OnInit {
  suppliers: Supplier[] = [
    { name: 'Medi Lab Pvt. Ltd', address: 'Galle Avenue Rd, Colombo 10', phone1: '011 2 100 100', phone2: '011 4 700 700' },
    { name: 'Medi Hub Pvt. Ltd', address: 'Galle Avenue Rd, Colombo 10', phone1: '011 2 100 100', phone2: '011 4 700 700' },
    { name: 'Medi Lanka Pvt. Ltd', address: 'Galle Avenue Rd, Colombo 10', phone1: '011 2 100 100', phone2: '011 4 700 700' },
    { name: 'MediHub Pvt. Ltd', address: 'Galle Avenue Rd, Colombo 10', phone1: '011 2 100 100', phone2: '011 4 700 700' },
    { name: 'Medi Pvt. Ltd', address: 'Galle Avenue Rd, Colombo 10', phone1: '011 2 100 100', phone2: '011 4 700 700' },
    // Add more suppliers as needed
  ];

  // need to change selected compnay

  items: Item[] = [
    {
      id: 'IC2114522',
      name: 'Dental Clinic Chair',
      price: 125000,
      stock: 10,
      image: '../../assets/images/dental-chair.png'
    },
    {
      id: 'IC2114523',
      name: 'Advanced Dental Chair',
      price: 150000,
      stock: 5,
      image: '../../assets/images/dental-chair.png'
    },
    {
      id: 'IC2114524',
      name: 'Advanced Dental Chair',
      price: 170000,
      stock: 5,
      image: '../../assets/images/dental-chair.png'
    },
    {
      id: 'IC2114525',
      name: 'Basic Dental Chair',
      price: 130000,
      stock: 6,
      image: '../../assets/images/dental-chair.png'
    },
    {
      id: 'IC2114526',
      name: 'Portable Dental Chair',
      price: 150000,
      stock: 3,
      image: '../../assets/images/dental-chair.png'
    },
    {
      id: 'IC2114527',
      name: 'Advanced Dental Chair',
      price: 90000,
      stock: 7,
      image: '../../assets/images/dental-chair.png'
    },
    {
      id: 'IC2114528',
      name: 'Portable Dental Chair',
      price: 140000,
      stock: 20,
      image: '../../assets/images/dental-chair.png'
    },
    {
      id: 'IC2114529',
      name: 'Dental Clinic Chair',
      price: 100000,
      stock: 2,
      image: '../../assets/images/dental-chair.png'
    },
    // Add more items as needed
  ];

  cart: CartItem[] = [];
  selectedCategory: string = '';
  searchTerm: string = '';
  currentView: string = 'suppliers';
  selectedSupplier: Supplier | null = null;
  selectedItem: Item | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 8; // 4 items per row * 2 rows = 8 items per page

  get filteredItems() {
    return this.items.filter(item =>
      item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) &&
      (this.selectedCategory ? item.id === this.selectedCategory : true)
    );
  }

  get paginatedItems() {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredItems.slice(start, end);
  }

  ngOnInit(): void {}

  visitStore(supplier: Supplier) {
    this.selectedSupplier = supplier;
    this.currentView = 'items';
  }

  showItemDetails(item: Item) {
    this.selectedItem = item;
    this.currentView = 'itemDetails';
  }

  backToItems() {
    this.currentView = 'items';
  }

  addToCart(item: Item) {
    const cartItem = this.cart.find(ci => ci.item.id === item.id);
    if (cartItem) {
      cartItem.quantity += 1;
    } else {
      this.cart.push({ item, quantity: 1 });
    }
  }

  removeFromCart(cartItem: CartItem) {
    this.cart = this.cart.filter(ci => ci.item.id !== cartItem.item.id);
  }

  increaseQuantity(cartItem: CartItem): void {
    cartItem.quantity++;
  }

  decreaseQuantity(cartItem: CartItem): void {
    if (cartItem.quantity > 1) {
      cartItem.quantity--;
    }
  }

  openCartModal() {
    const cartModal = document.getElementById('cartModal');
    if (cartModal) {
      cartModal.style.display = 'block';
    }
  }

  closeCartModal() {
    const cartModal = document.getElementById('cartModal');
    if (cartModal) {
      cartModal.style.display = 'none';
    }
  }

  deleteSelectedItems(): void {
    this.cart = this.cart.filter(cartItem => !cartItem.selected);
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage -= 1;
    }
  }

  nextPage() {
    if (this.currentPage * this.itemsPerPage < this.filteredItems.length) {
      this.currentPage += 1;
    }
  }

  goToPage(page: number) {
    this.currentPage = page;
  }
}
