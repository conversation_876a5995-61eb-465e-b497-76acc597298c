import {
  AfterViewInit,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AdminService } from '../admin.service';
import { UserTemp, UserTempStatus, UserTempType } from 'src/app/auth/auth';
import { Modal } from 'bootstrap';
import { FormGroup, FormBuilder } from '@angular/forms';

@Component({
  selector: 'app-admin-user-management',
  templateUrl: './admin-user-management.component.html',
  styleUrls: ['./admin-user-management.component.css'],
})
export class AdminUserManagementComponent implements OnInit, AfterViewInit {
  protected searchTerm: string = '';
  protected listingOrderAsc: boolean = true;
  protected selectTerm: string = 'USER_VERIFIED';
  protected fullUserTempList: UserTemp[] = [];
  protected filteredPaginatedData: UserTemp[] = [];
  protected selectedUserTemp: UserTemp | null = null;
  @ViewChild('exampleModal') exampleModal!: ElementRef<HTMLDivElement>;
  private modalInstance: Modal | undefined;

  protected currentPage: number = 1;
  protected itemsPerPage: number =5;
  protected totalPages: number = 1;
  protected visiblePages: number[] = [];

  filterForm: FormGroup;

  constructor(private fb: FormBuilder, private adminService: AdminService) {
    this.filterForm = this.fb.group({
      doctor: [true],
      clinic: [true],
      supplier: [true],
      laboratory: [true],
      futureDentist: [false],
    });
  }

  ngOnInit(): void {
    this.getUserTemps();
  }

  private getUserTemps() {
    this.adminService
      .getAllUserTempList()
      .subscribe((resp: UserTemp[] | null) => {
        if (resp) {
          this.fullUserTempList = resp;
          this.updatePagination();
        }
      });
  }

  get filteredUserTempList(): UserTemp[] {

    const filteredList = this.fullUserTempList.filter(userTemp => {

      const searchMatch = this.searchTerm.trim() === '' ||
                          userTemp.mainName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                          userTemp.userEmail.toLowerCase().includes(this.searchTerm.toLowerCase());

      const checkboxFilterMatch =
        (userTemp.userTempType?.toString() === "DOCTOR" && this.filterForm.value.doctor) ||
        (userTemp.userTempType?.toString() === "CLINIC" && this.filterForm.value.clinic) ||
        (userTemp.userTempType?.toString() === "SUPPLIER" && this.filterForm.value.supplier) ||
        (userTemp.userTempType?.toString() === "LABORATORY" && this.filterForm.value.laboratory) ||
        (userTemp.userTempType?.toString() === "FUTURE_DENTIST" && this.filterForm.value.futureDentist);

      const statusMatch = this.selectTerm === '0' ||
                          userTemp.userTempStatus?.toString() === this.selectTerm;

      return searchMatch && checkboxFilterMatch && statusMatch;
    });

    return filteredList.sort((a, b) => {
      const dateA = Array.isArray(a.createDateTime) ? new Date(a.createDateTime[0], a.createDateTime[1] - 1, a.createDateTime[2], a.createDateTime[3], a.createDateTime[4], a.createDateTime[5], a.createDateTime[6]) : new Date(a.createDateTime!);
      const dateB = Array.isArray(b.createDateTime) ? new Date(b.createDateTime[0], b.createDateTime[1] - 1, b.createDateTime[2], b.createDateTime[3], b.createDateTime[4], b.createDateTime[5], b.createDateTime[6]) : new Date(b.createDateTime!);

      if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
        console.warn('Invalid date found. Skipping sorting.');
        return 0;
      }

      if (this.listingOrderAsc) {
        if (dateA.getDate() == dateB.getDate()) {
          return dateA.getTime() - dateB.getTime();
        }else{
          return dateA.getDate() - dateB.getDate();
        }
      } else {
        if (dateB.getDate() == dateA.getDate()) {
          return dateB.getTime() - dateA.getTime();
        }else{
          return dateB.getDate() - dateA.getDate();
        }
      }
    });

  }

  ngAfterViewInit(): void {
    this.modalInstance = new Modal(this.exampleModal.nativeElement);
  }

  viewDetails(currentUser: UserTemp) {
    this.selectedUserTemp = currentUser;
    if (this.selectedUserTemp && this.modalInstance) {
      this.modalInstance.toggle();
    }
  }

  saveAsPermanantUser(userTempId: number | undefined) {
    if (userTempId) {
      this.adminService.updateUserAsPermanant(userTempId).subscribe((resp) => {
        if (resp != null && resp != '') {
          if (this.modalInstance) {
            this.exampleModal.nativeElement.addEventListener('hidden.bs.modal', () => {
              this.getUserTemps();
              this.selectedUserTemp = null;
            }, { once: true });
            this.modalInstance.toggle();
          }
        }
      });
    }
  }

    // Pagination methods
    updatePagination() {
      this.totalPages = Math.ceil(this.filteredUserTempList.length / this.itemsPerPage);
      this.filteredPaginatedData = this.paginatedData();
      this.visiblePages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    paginatedData() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredUserTempList.slice(start, end);
    }

    goToPage(page: number) {
      this.currentPage = page;
      this.filteredPaginatedData = this.paginatedData();
    }

}
