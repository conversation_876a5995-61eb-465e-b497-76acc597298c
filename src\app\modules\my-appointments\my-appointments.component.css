.sidebar-container {
    height: calc(100vh - 80px);
    width: 20%;
    overflow-y: auto;
    display: inline-block;
}

.main-content {
    height: calc(100vh - 80px);
    width: 80%;
    overflow-y: auto;
    padding: 16px;
    display: inline-block;
}

.container {
    padding: 20px;
}

.header-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    /* Allows wrapping of elements */
}

.header-row-h1 {
    color: black;
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 10px;
    /* Adds space between the title and other elements */
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 15px;
}

.search-bar {
    position: relative;
    margin: 20px 0;
    width: 100%;
    max-width: 300px;
}

#search-input {
    padding: 10px;
    width: 100%;
    padding-left: 30px;
    border: 1px solid #ccc;
    border-radius: 40px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

.rectangle {
    width: 100%;
    max-width: 1292px;
    height: auto;
    min-height: 400px;
    border-radius: 15px;
    background: #FFFFFF;
    box-shadow: 0px 3px 11.9px -1px #00000040;
    margin-top: 20px;
}

.my-appointments-table {
    width: 100%;
    border-radius: 10px;
    border-collapse: collapse;
    margin-top: 20px;
    table-layout: auto;
    /* Allows table to adjust to content */
}

.my-appointments-table th,
.my-appointments-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.my-appointments-table th {
    color: rgb(0, 0, 0);
    background: #FFB07D38;
}

.my-appointments-table thead tr th {
    border-bottom: 2px solid #ddd;
}

.action-cell {
    text-align: right !important;
}

.action-btn {
    background-color: transparent;
    border: none;
    padding: 5px;
    cursor: pointer;
}

.accept-btn i {
    color: green;
    font-size: 18px;
}

.delete-btn i {
    color: red;
    font-size: 18px;
}

/* Media Queries for Responsiveness */

@media (max-width: 1024px) {
    .sidebar-container {
        width: 25%;
    }
    .main-content {
        width: 75%;
    }
}

@media (max-width: 768px) {
    .sidebar-container {
        width: 100%;
        height: auto;
        display: block;
    }
    .main-content {
        width: 100%;
        padding: 10px;
    }
    .header-row {
        flex-direction: column;
        align-items: flex-start;
    }
    .search-bar {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .header-row-h1 {
        font-size: 24px;
    }
    .rectangle {
        width: 100%;
        min-height: auto;
    }
    .my-appointments-table th,
    .my-appointments-table td {
        font-size: 12px;
        padding: 5px;
    }
    .search-bar {
        width: 100%;
    }
}
