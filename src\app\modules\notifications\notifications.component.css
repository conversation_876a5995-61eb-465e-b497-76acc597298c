.notification-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.line {
  flex: 1;
  border: 0;
  height: 1px;
  background-color: #000;
}

.header-text {
  margin: 0 10px;
  font-weight: bold;
  text-transform: uppercase;
}


#notificationHeader {
    margin-bottom: 20px;
  }

  #filterButton {
    margin-right: auto;
  }

  #notificationList .card {
    margin-bottom: 10px;
    padding-bottom: 10px;
    cursor: pointer;
    transition: background-color 0.1s ease;
    border-radius: 20px;
  }

  #notificationList .card-body {
    display: flex;
    align-items: center;
    padding: 0.01rem;
  }

  #notificationList .profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid rgb(235, 117, 21);
    color: rgb(235, 117, 21);
    background-color: #eeeded;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
  }

  #notificationList .notification-content {
    flex-grow: 1;
    overflow: hidden;
  }

  #notificationList .unread {
    background-color: #f7efe6;
  }

  #notificationList .unread::after {
    content: '';
    width: 10px;
    height: 10px;
    background-color: #4caf50;
    border-radius: 50%;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
  }

  #notificationList .card-title {
    margin-bottom: 2px;
    font-weight: bold;
    font-size: 0.9rem;
  }

  #notificationList .card-text {
    margin-bottom: 2px;
    color: #979797;
    font-size: 0.8rem;
    overflow: hidden;
  }

  #notificationList .notification-date {
    font-size: 0.75em;
    color: #3b3b3b;
    padding-bottom: 0.9rem;
  }

  .modal {
    background-color: rgba(0, 0, 0, 0.5);

  }

  #notificationModal .modal-dialog {
    max-width: 500px;
  }

  #notificationModal .modal-body {
    max-height: 300px;
    overflow-y: auto;
  }

  #modalTitle {
    font-weight: bold;
    font-size: 1.2rem;
  }

  #modalCloseButton {
    background-color: transparent;
    border: none;
    font-size: 1.5rem;
  }

  #modalBody p {
    font-size: 1rem;
  }

  #modalDate {
    display: block;
    margin-top: 10px;
    font-size: 0.8rem;
    color: #777;
  }

  .form-check-input {
    cursor: pointer;
  }

  .filterButton {
    background-color: transparent;
    border: 2px solid #3b3b3b;
    color: #3b3b3b;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 0.8rem;
    font-weight: bold;
    cursor: pointer;
  }

  .filterButton:hover {
    background-color: #3b3b3b;
    color: white;
  }

  .mark_all_read {
    background-color: transparent;
    border: 2px solid #0075FF;
    color: #0075FF;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 0.8rem;
    font-weight: bold;
    cursor: pointer;
    margin-right: 10px;
  }

  .mark_all_read:hover {
    background-color: #0075FF;
    color: white;
  }

  .delete {
    background-color: transparent;
    border: 2px solid #FF0000;
    color: #FF0000;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 0.8rem;
    font-weight: bold;
    cursor: pointer;
  }

  .delete:hover {
    background-color: #FF0000;
    color: white;
  }
