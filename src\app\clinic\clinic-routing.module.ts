import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ClinicLayoutComponent } from './clinic-layout/clinic-layout.component';
import { ClinicOrdersComponent } from './clinic-orders/clinic-orders.component';
import { ClinicAddLaboratorOrderComponent } from './clinic-add-laborator-order/clinic-add-laborator-order.component';
import { ClinicLaboratoryOrderListComponent } from './clinic-laboratory-order-list/clinic-laboratory-order-list.component';
import { AddClinicServiceComponent } from './add-clinic-service/add-clinic-service.component';
import { ClinicDashboardComponent } from './clinic-dashboard/clinic-dashboard.component';
import { ClinicSelectLaboratorySetupComponent } from './clinic-select-laboratory-setup/clinic-select-laboratory-setup.component';
import { ClinicSupplierOrderComponent } from './clinic-supplier-order/clinic-supplier-order.component';
import {ClinicSetupClinicComponent} from './clinic-setup-clinic/clinic-setup-clinic.component'
import { DoctorListComponent } from './doctor-list/doctor-list.component';
import { ClinicSupplierOrderListComponent } from './clinic-supplier-order-list/clinic-supplier-order-list.component';
import { authGuard } from '../auth/auth.guard';
import { ClinicNewAppointmentComponent } from './clinic-new-appointment/clinic-new-appointment.component';

const routes: Routes = [
  {
    path: '',
    component: ClinicLayoutComponent,
    canActivateChild:[authGuard],
    children: [
      { path: '', pathMatch: 'prefix', redirectTo: 'dashboard' },
      { path: 'dashboard', component: ClinicDashboardComponent },
      { path: 'add-order', component: ClinicAddLaboratorOrderComponent },
      { path: 'supplier-orders', component: ClinicSupplierOrderListComponent },
      { path: 'supplier-orders/new-order', component: ClinicSupplierOrderComponent },
      { path: 'laboratory-orders', component: ClinicLaboratoryOrderListComponent },
      { path: 'laboratory-orders/new-order', component: ClinicSelectLaboratorySetupComponent },
      { path: 'add-service', component: AddClinicServiceComponent },
      { path: 'select-laboratory-setup', component: ClinicSelectLaboratorySetupComponent },
      { path: 'clinic-setup', component: ClinicSetupClinicComponent },
      { path: 'list-doctor', component: DoctorListComponent },
      { path: 'orders', component: ClinicSupplierOrderListComponent },
      { path: 'new-appointment', component: ClinicNewAppointmentComponent },
      { path: '**', redirectTo: 'dashboard' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ClinicRoutingModule {}
