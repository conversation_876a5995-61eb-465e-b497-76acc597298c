import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-user-selection',
  templateUrl: './user-selection.component.html',
  styleUrls: ['./user-selection.component.css']
})
export class UserSelectionComponent {
  selectedOption: string | null = null;

  constructor(private router: Router) {}

  // Function to handle selection
  selectOption(option: string): void {
    this.selectedOption = option;
  }

  // Function to handle continue button click
  onContinue(): void {
    switch (this.selectedOption) {
      case 'Clinic':
        this.router.navigate(['/clinic-registration']);
        break;
      case 'Doctor':
        this.router.navigate(['/doctor-registration']);
        break;
      case 'Supplier':
        this.router.navigate(['/supplier-registration']);
        break;
      case 'Laboratory':
        this.router.navigate(['/laboratory-registration']);
        break;
      case 'Future Dentist':
        this.router.navigate(['/future-dentist-registration']);
        break;
      default:
        break;
    }
  }

  // Function to check if an option is selected
  isSelected(option: string): boolean {
    return this.selectedOption === option;
  }
}
