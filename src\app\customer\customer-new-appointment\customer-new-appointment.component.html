<div class="appointment-container">

    <div class="content py-1">
        <div class="navigation mb-4">
            <h2 class="mb-3 header1">Make Appointment</h2>
            <div class="navigation-bar">
                <div class="navigation-step" *ngFor="let step of steps; let i = index"
                    [ngClass]="{'active': currentStep === i + 1}">
                    <div class="dot"></div>
                    <span>{{ step }}</span>
                </div>
            </div>
        </div>
        <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" style="padding-inline: 20px;">
            <div *ngIf="currentStep === 1">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="preferredservice" class="form-label">Preferred Service</label>
                        <div class="custom-arrow">
                            <select formControlName="preferredservice" id="preferredservice" class="form-control"
                                required>
                                <option value="" selected disabled>Select</option>
                                <option value="1">Dental Bonding</option>
                                <option value="2">Cosmetic Fillings</option>
                                <option value="3">Invisalign</option>
                                <option value="4">Teeth Cleanings</option>
                                <option value="5">Root Canal Therapy</option>
                                <option value="6">Dental Sealants</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="city" class="form-label">City</label>
                        <div class="custom-arrow">
                            <input type="text" class="form-control" [value]="customer.city" readonly />
                        </div>                   
                    </div>
                </div>

                <div class="row mb-3">
                    <label for="preferredDate" class="form-label">Preferred Date</label>
                    <div class="row">
                        <div class="col-md-6">
                            <input type="date" formControlName="fromDate" id="fromDate" class="form-control"
                                [min]="currentDate" required>
                            <div *ngIf="appointmentForm.get('fromDate')?.invalid && (appointmentForm.get('fromDate')?.dirty || appointmentForm.get('fromDate')?.touched)"
                                class="text-danger">
                                <div *ngIf="appointmentForm.get('fromDate')?.errors?.['required']">Date From is
                                    required.</div>
                                <div *ngIf="appointmentForm.get('fromDate')?.errors?.['pastDate']">Date From cannot be
                                    in the past.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label for="preferredTime" class="form-label">Preferred Time</label>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="fromTime" class="form-label">From</label>
                            <input type="time" formControlName="fromTime" id="fromTime" class="form-control"
                                [disabled]="isImmediateBooking" required>
                            <div *ngIf="appointmentForm.get('fromTime')?.invalid && (appointmentForm.get('fromTime')?.dirty || appointmentForm.get('fromTime')?.touched)"
                                class="text-danger">
                                <div *ngIf="appointmentForm.get('fromTime')?.errors?.['required']">Preferred Time From
                                    is required.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="toTime" class="form-label">To</label>
                            <input type="time" formControlName="toTime" id="toTime" class="form-control"
                                [disabled]="isImmediateBooking" required>
                            <div *ngIf="appointmentForm.get('toTime')?.invalid && (appointmentForm.get('toTime')?.dirty || appointmentForm.get('toTime')?.touched)"
                                class="text-danger">
                                <div *ngIf="appointmentForm.get('toTime')?.errors?.['required']">Preferred Time To is
                                    required.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input type="checkbox" formControlName="immediate" id="immediate" class="form-check-input">
                            <label for="immediate" class="form-check-label">Immediate Booking</label>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between btn-field">
                    <div></div>
                    <button type="button" class="btn btn-primary second-next" (click)="nextStep()"
                        [disabled]="isStepInvalid()">Find Your Nearest Dental Clinic</button>
                </div>
            </div>

            <div *ngIf="currentStep === 2">
                <h4 class="mb-4">Please select nearest clinic:</h4>
                <div class="clinic-list mb-4">
                    <table class="table">
                        <tbody>
                            <tr *ngFor="let clinic of clinics"
                                [ngClass]="{'selected-clinic': selectedClinic === clinic.clinicId}"
                                (click)="selectClinic(clinic.clinicId)">
                                <td>{{ clinic.name }}</td>
                                <td class="text-end">
                                    <span class="text-primary" *ngIf="selectedClinic !== clinic.clinicId">Book Now</span>
                                    <span class="badge bg-success" *ngIf="selectedClinic === clinic.clinicId">Selected</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" (click)="prevStep()">Previous</button>
                    <button type="button" class="btn btn-primary" (click)="nextStep()"
                        [disabled]="!selectedClinic">Request For The Appointment</button>
                </div>
            </div>
            <div *ngIf="currentStep === 3">
                <div class="text-center">
                    <h4 class="mb-4">Appointment Submission</h4>
                    <div class="appointment-success mb-4">
                        <i class="bi bi-check-circle-fill text-success display-4"></i>
                    </div>
                    <p class="mb-4">Your Appointment Submission is Sent to Approval Now. <br> Please Check Your
                        Notifications or Bookings.</p>
                    <div class="d-flex justify-content-center">
                        <button type="button" class="btn btn-secondary me-3"
                            (click)="cancelAppointment()">Cancel</button>
                        <button type="button" class="btn btn-primary" (click)="saveAppointment()">Done</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
