<form [formGroup]="orderForm" (ngSubmit)="saveLaboratoryOrder()">
  <div class="row">
    <div class="container">
      <div class="order-form-container">
        <div class="top-section">
          <div class="service-details">
            <h2>{{ laboratorySetup?.laboratorySubCategoryId?.laboratorySubCategoryName }}</h2>
            <p>{{ laboratorySetup?.description }}</p>
            <h2>LKR {{ laboratorySetup?.price | number: '1.2-2' }}</h2>
          </div>
          <div class="patient-details">
            <div>
              <label for="patient-name">Patient Name</label>
              <input type="text" id="patient-name" formControlName="patientName" placeholder="Enter patient name">
              <div *ngIf="orderForm.get('patientName')?.invalid && (orderForm.get('patientName')?.dirty || orderForm.get('patientName')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('patientName')?.errors?.['required']">Patient Name is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('patientName')?.errors?.['pattern']">Only letters are allowed.</small>
              </div>
            </div>
            <div>
              <label for="date-of-birth">Date of Birth</label>
              <input type="date" id="date-of-birth" formControlName="dateOfBirth">
              <div *ngIf="orderForm.get('dateOfBirth')?.invalid && (orderForm.get('dateOfBirth')?.dirty || orderForm.get('dateOfBirth')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('dateOfBirth')?.errors?.['required']">Date of Birth is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('dateOfBirth')?.errors?.['tooYoung']">You must be at least 1 year old.</small>
              </div>
            </div>
            <div>
              <label for="contactNumber">Telephone</label>
              <input type="tel" id="contactNumber" formControlName="contactNumber" placeholder="Enter phone number">
              <div *ngIf="orderForm.get('contactNumber')?.invalid && (orderForm.get('contactNumber')?.dirty || orderForm.get('contactNumber')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('contactNumber')?.errors?.['required']">Telephone is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('contactNumber')?.errors?.['pattern']">Only numbers are allowed.</small>
                <small class="text-danger" *ngIf="orderForm.get('contactNumber')?.errors?.['tooShort']">Please enter a valid 10 digit number.</small>
                <small class="text-danger" *ngIf="orderForm.get('contactNumber')?.errors?.['tooLong']">Please check your input.</small>
              </div>
            </div>
            <div>
              <label for="expected-date">Expected Date</label>
              <input type="date" id="expected-date" formControlName="expectedDate" [min]="minDate">
              <div *ngIf="orderForm.get('expectedDate')?.invalid && (orderForm.get('expectedDate')?.dirty || orderForm.get('expectedDate')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('expectedDate')?.errors?.['required']">Expected Date is required.</small>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom-section">
          <div class="form-row">
            <div>
              <label for="tooth-number">Tooth Number</label>
              <input type="text" id="tooth-number" formControlName="toothNumber" placeholder="Enter tooth number">
              <div *ngIf="orderForm.get('toothNumber')?.invalid && (orderForm.get('toothNumber')?.dirty || orderForm.get('toothNumber')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('toothNumber')?.errors?.['required']">Tooth Number is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('toothNumber')?.errors?.['pattern']">Only numbers are allowed.</small>
                <small class="text-danger" *ngIf="orderForm.get('toothNumber')?.errors?.['invalidToothNumber']">Tooth number must be between 1 and 32.</small>
              </div>
            </div>
            <div>
              <label for="occlusion">Occlusion</label>
              <input type="text" id="occlusion" formControlName="occlusion" placeholder="Enter occlusion">
              <div
                *ngIf="orderForm.get('occlusion')?.invalid && (orderForm.get('occlusion')?.dirty || orderForm.get('occlusion')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('occlusion')?.errors?.['required']">Occlusion is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('occlusion')?.errors?.['pattern']">Only letters areallowed.</small>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div>
              <label for="tooth-surface">Tooth Surface</label>
              <input type="text" id="tooth-surface" formControlName="toothSurface" placeholder="Enter tooth surface">
              <div *ngIf="orderForm.get('toothSurface')?.invalid && (orderForm.get('toothSurface')?.dirty || orderForm.get('toothSurface')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('toothSurface')?.errors?.['required']">Tooth Surface is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('toothSurface')?.errors?.['pattern']">Only letters are allowed.</small>
              </div>
            </div>
            <div>
              <label for="margin-type">Margin Type</label>
              <input type="text" id="margin-type" formControlName="marginType" placeholder="Enter margin type">
              <div *ngIf="orderForm.get('marginType')?.invalid && (orderForm.get('marginType')?.dirty || orderForm.get('marginType')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('marginType')?.errors?.['required']">Margin Type is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('marginType')?.errors?.['pattern']">Only letters are allowed.</small>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div>
              <label for="type-of-crown">Type of Crown</label>
              <input type="text" id="type-of-crown" formControlName="typeOfCrown" placeholder="Enter type of crown">
              <div *ngIf="orderForm.get('typeOfCrown')?.invalid && (orderForm.get('typeOfCrown')?.dirty || orderForm.get('typeOfCrown')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('typeOfCrown')?.errors?.['required']">Type of Crown is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('typeOfCrown')?.errors?.['pattern']">Only letters are allowed.</small>
              </div>
            </div>
            <div>
              <label for="crown-design">Crown Design</label>
              <input type="text" id="crown-design" formControlName="crownDesign" placeholder="Enter crown design">
              <div *ngIf="orderForm.get('crownDesign')?.invalid && (orderForm.get('crownDesign')?.dirty || orderForm.get('crownDesign')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('crownDesign')?.errors?.['required']">Crown Design is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('crownDesign')?.errors?.['pattern']">Only letters are allowed.</small>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div>
              <label for="shade">Shade</label>
              <input type="text" id="shade" formControlName="shade" placeholder="Enter shade">
              <div *ngIf="orderForm.get('shade')?.invalid && (orderForm.get('shade')?.dirty || orderForm.get('shade')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('shade')?.errors?.['required']">Shade is required.</small>
                <small class="text-danger" *ngIf="orderForm.get('shade')?.errors?.['pattern']">Only letters are allowed.</small>
              </div>
            </div>
            <div>
              <label for="material-specifications">Material Specifications</label>
              <input type="text" id="material-specifications" formControlName="materialSpecifications"
                placeholder="Enter material specifications">
              <div *ngIf="orderForm.get('materialSpecifications')?.invalid && (orderForm.get('materialSpecifications')?.dirty || orderForm.get('materialSpecifications')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('materialSpecifications')?.errors?.['required']">Material Specifications are required.</small>
                <small class="text-danger" *ngIf="orderForm.get('materialSpecifications')?.errors?.['pattern']">Only letters are allowed.</small>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div>
              <label for="additional-instructions">Additional Instructions</label>
              <textarea id="additional-instructions" formControlName="additionalInstructions"
                placeholder="Enter any additional instructions"></textarea>
              <div *ngIf="orderForm.get('additionalInstructions')?.invalid && (orderForm.get('additionalInstructions')?.dirty || orderForm.get('additionalInstructions')?.touched)">
                <small class="text-danger" *ngIf="orderForm.get('additionalInstructions')?.errors?.['required']">Additional Instructions are required.</small>
                <small class="text-danger" *ngIf="orderForm.get('additionalInstructions')?.errors?.['pattern']">Only letters and numbers are allowed.</small>
              </div>
            </div>
          </div>

          <button type="submit" class="buttons" [disabled]="isSubmitting">
            <span *ngIf="isSubmitting">Saving...</span>
            <span *ngIf="!isSubmitting">Create Order</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</form>