import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-accept-popup',
  templateUrl: './accept-popup.component.html',
  styleUrls: ['./accept-popup.component.css']
})
export class AcceptPopupComponent {

  constructor(
     private router: Router
  ) {}

  @Input() order: any; // The order details passed from the parent
  @Output() closePopup = new EventEmitter<void>(); // Emits when the popup is closed
  @Output() orderAccepted = new EventEmitter<any>(); // Emits when the order is accepted

  // Method to close the popup
  close() {
    this.closePopup.emit();
  }

  // Method to accept the order
  acceptOrder() {
    this.orderAccepted.emit(this.order);
    this.close(); // Close the popup after accepting
  }

  navigateSendInvoice(): void {
    this.router.navigate(['/laboratory/invoice']);
  }
}
