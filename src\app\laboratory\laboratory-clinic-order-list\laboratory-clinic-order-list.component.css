.send-orders-container {
  padding: 20px;
}

.header-row,
.send-orders-list-row {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.send-orders-list-row-new {
  width: 146px;
  height: 29px;
  top: 100px;
  left: 403px;
  gap: 0px;
  opacity: 0px;
  font-family: Inter;
  font-size: 24px;
  font-weight: 500;
  line-height: 29.05px;
  text-align: left;
  color: #000;
}

.see-all-button {
  font-family: Inter;
  font-size: 15px;
  font-weight: 400;
  color: #d85322;
  background: none;
  border: #ddd;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  width: 100%;
}

.header-row-h1 {
  width: 174px;
  height: 39px;
  top: 149px;
  left: 403px;
  gap: 0px;
  opacity: 0px;
  font-family: Inter;
  font-size: 32px;
  font-weight: 600;
  line-height: 38.73px;
  text-align: left;
  color: #000;
}

.header-bottom-line {
  border-bottom: 2px solid #ccc;
  margin-top: 10px;
}

.search-bar {
  position: relative;
  margin: 20px 0;
  width: 100%;
  max-width: 300px;
}

#search-input {
  padding: 10px;
  width: 100%;
  padding-left: 30px;
  border: 1px solid #ccc;
  border-radius: 40px;
  border-width: 30%;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.send-order-table {
  width: 100%;
  border-radius: 10px;
  border-collapse: collapse;
  margin-top: 20px;
}

.send-order-table th,
.send-order-table td {
  padding: 8px;
  text-align: center;
  border-width: 30%;
}

.send-order-table th {
  background-color: #ffb07d38;
  color: rgb(0, 0, 0);
}

.send-order-table thead tr th {
  border-bottom: 2px solid #ddd;
}

.send-order-table tbody tr:nth-child(odd) {
  background-color: white;
}

.send-order-table tbody tr:nth-child(even) {
  background-color: rgba(255, 176, 125, 0.12);
}

.profile-icon {
  color: #ff5722;
  margin-right: 8px;
  vertical-align: middle;
}

.status-cell {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.status-cell button {
  padding: 5px 8px;
  border-radius: 20px;
  width: 135px;
  text-align: center;
}

.status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px;
}

.completed,
.pending,
.accepted,
.inprogress,
.rejected {
  border-radius: 18px;
  background: #ffc1070d;
}

.completed {
  border: 1px solid #00c820;
  color: #00c820;
}

.pending {
  border: 1px solid #ffc107;
  color: #ffc107;
}

.accepted {
  border: 1px solid #d1641c;
  color: #d1641c;
}

.inprogress {
  border: 1px solid #0075ff;
  color: #0075ff;
}

.rejected {
  border: 1px solid #ff0000;
  color: #ff0000;
}

.view-order {
  border: 2px solid red;
  color: red;
  border-radius: 20px;
  margin-left: 20px;
}

.close-btn {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  text-align: right;
}

.close-btn:hover,
.close-btn:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

form {
  display: flex;
  flex-direction: column;
}

form label {
  margin-top: 10px;
}

form input,
form select,
form textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-top: 5px;
  border-width: 30%;
}

.pagination-container {
  display: flex;
  justify-content: right;
  margin: 20px 0;
}

.pagination {
  display: inline-flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}

.page-item {
  cursor: pointer;
}

.page-item.disabled .page-link {
  pointer-events: none;
  color: #ddd;
}

.page-item.active .page-link {
  background-color: #ff7e3b;
  border-color: #ff7e3b;
  color: white;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #ff7e3b;
  background-color: #fff;
  border: 1px solid #ddd;
}

@media (max-width: 768px) {
  .send-order-table {
    display: none;
  }

  .card-list {
    display: flex;
    flex-direction: column;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    margin-top: 20px;
  }

  .card-header {
    font-weight: bold;
    margin-bottom: 10px;
  }

  .card-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
  }

  .card-footer button {
    width: 100%;
  }

  .view-order {
    margin: 0%;
  }

  .pagination-container {
    justify-content: center;
  }
}

@media (min-width: 769px) {
  .card-list {
    display: none;
  }

  .send-order-table {
    display: table;
  }
}
