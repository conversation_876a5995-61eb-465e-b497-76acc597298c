// import { User } from '../admin/components/user/user';
import { Clinic } from 'src/app/clinic/clinic';
import { Customer } from './customer';

export class Appointments {
  appointmentId: number = 0;
  // userId: number = 0;
  firstName: string = '';
  lastName: string='';
  address: string = '';
  city: string = '';
  state: string = '';
  district: string = '';
  telephone: string = '';
  email: string = '';
  preferredservice: string = '';
  nearestCity: string = '';
  fromDate: string = '';
  toDate: string = '';
  fromTime: string = '';
  toTime: string = '';
  clinics: Clinic= new Clinic();
  userName:string='';
  password:string='';
  customer: Customer = new Customer(); // Reference to the Customer class
}
