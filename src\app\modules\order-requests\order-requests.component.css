body {
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    margin: 0;
    padding: 0;
}

.container {
    width: 110%;
    margin: 0 auto;
    margin-left: 24%;

}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

header h1 {
    margin: 0;
    padding: 10;
}

.search-add {
    display: flex;
    align-items: center;
}

.search-add input {
    padding: 5px;
    margin-right: 10px;
}

.add-button {
    background-color: #ff7f50;
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
}

main {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.order-requests h2 {
    margin-top: 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

thead th {
    background: #FFB07D38;

    text-align: left;
    padding: 10px;
}

tbody tr:nth-child(even) {
    background: #FFB07D1F;

}

tbody td {
    padding: 10px;
}

.status {
    padding: 5px 10px;
    border-radius: 5px;
}



.view-request {
    background-color: #ff7f50;
    color: white;
    border: none;
    padding: 5px 10px;
    cursor: pointer;
}

.pagination {
    text-align: center;
}

.page {
    background-color: #ddd;
    border: none;
    padding: 10px 15px;
    margin: 0 5px;
    cursor: pointer;
}

.page:hover {
    background-color: #ccc;
}
.send-orders-container {
    padding: 20px;
}

.header-row,
.send-orders-list-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}



.see-all-button {
    width: 114px;
    height: 0px;
    margin-right: 0px;
    margin-left: 1090px;
    font-family: Inter;
    font-size: 15px;
    font-weight: 400;
    line-height: 18.15px;
    text-align: left;
    color: #D85322;
    background: none;
    border: #ddd;
}

.header-row-h1 {
    width: 274px;
    height: 39px;
    top: 149px;
    left: 403px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    color: #000;
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
}

.search-bar {
    position: relative;
    margin: 20px 0;
    width: 354px;
    max-width: 300px;

}

#search-input {
    padding: 10px;
    width: 100%;
    padding-left: 30px;
    border: 1px solid #ccc;
    border-radius: 30px;
    border-width: 30%;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

#printButton {
border: 1px solid #FB751E;
width: 248px;
height: 35px;
top: 149px;
gap: 0px;
border-radius: 50px ;
border: 1px 0px 0px 0px;
opacity: 0px;

}

.status {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
    border-width: 30%;
}

.approved {
    width: 114px;
    height: 25px;
    top: 432px;
    left: 1155px;
    gap: 0px;
    border-radius: 18px 18px 18px 18px;
    border: 1px 0px 0px 0px;
    opacity: 0px;
    background: #FFC1070D;
    border: 1px solid #00C820;
    color: #00C820;
}

.pending {
    width: 114px;
    height: 25px;
    top: 432px;
    left: 1155px;
    gap: 0px;
    border-radius: 18px 18px 18px 18px;
    border: 1px 0px 0px 0px;
    opacity: 0px;
    background: #FFC1070D;
    border: 1px solid #FFC107;
    color: #FFC107;
}
.view-request-button {
    background-color: #FEEFEF;
    border: 1px solid #F39C9C;
    color: #D85322;

    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    border-radius: 20px;
    transition: background-color 0.3s, color 0.3s;
}

.view-request-button:hover {
    background-color: #F39C9C;
    color: #FFFFFF;
}










.modal {
    display: none;
    position: fixed;
    z-index: 1;

    width: 100%;

    background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.4);
    padding-top: 60px;
}

.modal.show {
    display: block;
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 100%;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    border: none;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}
.create-quote-button {
    background: linear-gradient(to right, #FF7F00, #D62900);
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    border-radius: 20px;
    margin-left: 70%;

}

.create-quote-button:hover {
    background: linear-gradient(to right, #D62900, #FF7F00);
}







.pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px 20px;
  }

  .btn-outline-secondary {
    border: none;
    background-color: transparent;
    color: #B93426;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 25px;
    font-weight: bold;
  }

  .btn-outline-secondary:disabled {
    color: #B93426;
    cursor: not-allowed;
  }

  .page-number {
    display: inline-block;
    padding: 2px 10px;
    margin: 0 2px;
    background-color: #FFB07D;
    color: black;
    border: none;
    cursor: pointer;
    border-radius: 4px;
  }

  .page-number.active {
    background-color: #FB751E;
    color: white;
  }
  .sidebar-container {
    height: calc(10vh - 80px);
    width: 10%;
    overflow-y: auto;

    display: block;
}

  .close{
    margin-left: 95%;
  }

  .popuphead{
    font-size: 24px;
    font-weight: normal;
    margin-top: -5%;
  }
  .popuptext{
    margin-top: 5%;
  }


@media (max-width: 768px) {
    .sidebar-container,
    .main-content {
        width: 100%;
        display: block;
    }
}


.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.overlay.show {
    display: block;
}

.blurred {

    pointer-events: none;
    opacity: 0.5;
}

.modal {
    z-index: 20;
    /* other modal styles */
}
