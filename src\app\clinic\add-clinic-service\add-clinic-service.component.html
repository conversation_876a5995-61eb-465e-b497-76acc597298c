<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12 p-0">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title"></h5>
                <form
                  #f="ngForm"
                  (ngSubmit)="f.form.valid && onSubmit()"
                  class="row g-1"
                  novalidate="feedback-form"
                >
                  <div class="pagetitle">
                    <h1>Add Services</h1>
                  </div>
                  <div><br /></div>
                  <div class="form-group row">
                    <div class="col-md-6">
                      <label class="form-label" for="contractortelephone"
                        >Service Category</label
                      >
                      <div class="form-group mb-4">
                        <input
                          type="text"
                          id="contractortelephone"
                          name="contractortelephone"
                          class="form-control"
                          [(ngModel)]="
                            addClinicServices.clinicServiceCategoryName
                          "
                          #contractortelephone="ngModel"
                          required
                        />
                        <div
                          *ngIf="
                            f.submitted &&
                            f.controls['contractortelephone'].invalid
                          "
                          class="text-danger"
                        >
                          <div
                            *ngIf="f.controls['contractortelephone'].errors?.['required']"
                          >
                            Service Category.
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <hr />
                  <div class="row mb-3">
                    <div class="col-md-auto mb-3">
                      <button type="submit" class="add btn btn-add-company">
                        Add Services
                      </button>
                    </div>
                    <div class="col-md-auto">
                      <button type="reset" class="cancel btn btn-cancel">
                        Cancel
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="row">
          <div class="card col-12">
            <div class="row">
              <div class="col-sm-12 col-md-12 col-lg-12">
                <div class="card-body">
                  <br />
                  <section class="example-container" tabindex="0">
                    <table mat-table [dataSource]="dataSource" matSort>
                      <ng-container matColumnDef="clinicServiceCategoryName">
                        <th
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="color: black"
                        >
                          Clinic Service Category Name
                        </th>
                        <td mat-cell *matCellDef="let element">
                          {{ element.clinicServiceCategoryName }}
                        </td>
                      </ng-container>
                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns; sticky: true"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div *ngIf="isLoading == 1" class="spinner-container">
                      <mat-progress-spinner
                        color="primary"
                        mode="indeterminate"
                      >
                      </mat-progress-spinner>
                    </div>
                  </section>

                  <mat-paginator
                    [pageSizeOptions]="[25, 50, 100]"
                    showFirstLastButtons
                  >
                  </mat-paginator>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
