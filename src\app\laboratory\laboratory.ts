import { Clinic } from '../clinic/clinic';
import { User } from '../user/user';

export class Laboratory {
  laboratoryId: number = 0;
  laboratoryCategories: LaboratoryCategories = new LaboratoryCategories();
  userId: User = new User();
  name: string = '';
  address: string = '';
  city: string = '';
  state: string = '';
  country: string = '';
  tele: string = '';
  contactPerson: string = '';
  email: string = '';
  web: string = '';
  registeredDate: string = '';
  latitude: string = '';
  longitude: string = '';
}

export class LaboratorySetup {
  laboratoryId: Laboratory = new Laboratory(); 
  laboratorySetupId: number = 0;
  laboratoryCategoryId: LaboratoryCategory = new LaboratoryCategory();
  laboratorySubCategoryId: LaboratorySubCategory = new LaboratorySubCategory();
  description: string = '';
  price: number = 0;
  status: string = '';
}

export class LaboratoryCategory {
  laboratoryCategoryId: number = 0;
  laboratoryCategoryName: string = '';
}

export class LaboratorySubCategory {
  laboratorySubCategoryId: number = 0;
  laboratorySubCategoryName: string = '';
  laboratoryCategoryId: LaboratoryCategory = new LaboratoryCategory();
}

export class LaboratoryCategories {
  laboratoryCategoryId: number = 0;
  laboratoryCategory: string = '';
}

export class LaboratoryServices {}

export class LaboratoryClinicOrder {
  laboratoryOrderId: number = 0;
  clinicId: Clinic = new Clinic();
  laboratorySetupId: LaboratorySetup = new LaboratorySetup();
  status: string = '';
  patientName: string = '';
  dateOfBirth: string = '';
  contactNumber: string = '';
  laboratoryOrderTime: string = '';
  laboratoryOrderDate: string = '';
  expectedDate: string = '';
  toothNumber: number = 0;
  toothSurface: string = '';
  typeOfCrown: string = '';
  shade: string = '';
  materialSpecifications: string = '';
  occlusion: string = '';
  marginType: string = '';
  crownDesign: string = '';
  additionalInstructions: string = '';
}
