h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

input:focus {
  box-shadow: none;
}

.search-input {
  height: 40px;
  outline: none;
  box-shadow: none;
  border-radius: 5px;
  border: solid 1px rgb(200, 200, 200);
  padding-inline: 15px;
  font-size: 15px;
  text-align: end;
  font-weight: 700;
}

.search-input::placeholder {
  color: rgb(100, 100, 100);
  font-size: 13px;
}

.search-input-colored{
  height: 40px;
  outline: none;
  box-shadow: none;
  border-radius: 5px;
  border: solid 1px #fb751e;
  padding-inline: 10px;
  font-size: 15px;
  text-align: end;
}

.search-input:hover {
  border-color: #fb751e;
}
.search-input:focus {
  border-color: #fb751e;
}


.single-order-view-button{
  background: linear-gradient(to right, #fb751e, #b93426);
  color: white;
  border-radius: 5px;
  font-size: 12px;
}

.card-table-header{
  background: linear-gradient(to right, #fb751e, #b93426);
}
