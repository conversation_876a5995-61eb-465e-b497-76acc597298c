<div class="appointment-container">
    <div class="header-row">
        <h2>Make Appointment</h2>
    </div>

    <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()">
        <!-- First Name and Last Name Row -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="firstName" class="form-label">First Name</label>
                <input
                    type="text"
                    formControlName="firstName"
                    id="firstName"
                    class="form-control"
                />
                <div
                    *ngIf="
                        appointmentForm.get('firstName')?.invalid &&
                        (appointmentForm.get('firstName')?.dirty ||
                        appointmentForm.get('firstName')?.touched)
                    "
                    class="text-danger"
                >
                    <div *ngIf="appointmentForm.get('firstName')?.errors?.['required']">
                        First Name is required.
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <label for="lastName" class="form-label">Last Name</label>
                <input
                    type="text"
                    formControlName="lastName"
                    id="lastName"
                    class="form-control"
                />
                <div
                    *ngIf="
                        appointmentForm.get('lastName')?.invalid &&
                        (appointmentForm.get('lastName')?.dirty ||
                        appointmentForm.get('lastName')?.touched)
                    "
                    class="text-danger"
                >
                <div *ngIf="appointmentForm.get('lastName')?.errors?.['required']">
                    Last Name is required.
                </div>
                </div>
            </div>
        </div>

        <!-- Email and Telephone Row -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="email" class="form-label">Email</label>
                <input
                    type="email"
                    formControlName="email"
                    id="email"
                    class="form-control"
                />
                <div
                    *ngIf="
                        appointmentForm.get('email')?.invalid &&
                        (appointmentForm.get('email')?.dirty ||
                        appointmentForm.get('email')?.touched)
                    "
                    class="text-danger"
                >
                    <div *ngIf="appointmentForm.get('email')?.errors?.['required']">
                        Email is required.
                    </div>
                    <div *ngIf="appointmentForm.get('email')?.errors?.['email']">
                        Invalid email format.
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <label for="telephone" class="form-label">Telephone</label>
                <input
                    type="tel"
                    formControlName="telephone"
                    id="telephone"
                    class="form-control"
                />
                <div
                    *ngIf="
                        appointmentForm.get('telephone')?.invalid &&
                        (appointmentForm.get('telephone')?.dirty ||
                        appointmentForm.get('telephone')?.touched)
                    "
                    class="text-danger"
                >
                    <div *ngIf="appointmentForm.get('telephone')?.errors?.['required']">
                        Telephone is required.
                    </div>
                    <div *ngIf="appointmentForm.get('telephone')?.errors?.['pattern']">
                        Invalid telephone number format.
                    </div>
                </div>
            </div>
        </div>

        <!-- Service and Date Row -->
        <div class="row mb-3">
            <div class="col-md-6">
              <label for="preferredservice" class="form-label">Preferred Service</label>
              <div class="custom-arrow">
                <select
                  formControlName="preferredservice"
                  id="preferredservice"
                  class="form-control"
                  required
                >
                    <option value="" selected disabled>Select</option>
                    <option value="1">Dental Bonding</option>
                    <option value="2">Cosmetic Fillings</option>
                    <option value="3">Invisalign</option>
                    <option value="4">Teeth Cleanings</option>
                    <option value="5">Root Canal Therapy</option>
                    <option value="6">Dental Sealants</option>
                </select>
              </div>
            </div>

            <div class="col-md-6">
                <label for="preferredDate" class="form-label">Preferred Date</label>
                <input
                    type="date"
                    formControlName="fromDate"
                    id="fromDate"
                    class="form-control"
                    [min]="currentDate"
                    required
                />
                <div
                  *ngIf="
                    appointmentForm.get('fromDate')?.invalid &&
                    (appointmentForm.get('fromDate')?.dirty ||
                      appointmentForm.get('fromDate')?.touched)
                  "
                  class="text-danger"
                >
                    <div *ngIf="appointmentForm.get('fromDate')?.errors?.['required']">
                        Date From is required.
                    </div>
                </div>
            </div>
        </div>

        <!-- Time Row -->
        <div class="row">
            <label for="preferredTime" class="form-label">Preferred Time</label>
        </div>
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="fromTime" class="form-label">From</label>
                <input type="time" formControlName="fromTime" id="fromTime" class="form-control" required>
                <div
                    *ngIf="appointmentForm.get('fromTime')?.invalid && (appointmentForm.get('fromTime')?.dirty || appointmentForm.get('fromTime')?.touched)"
                    class="text-danger"
                >
                    <div *ngIf="appointmentForm.get('fromTime')?.errors?.['required']">
                        Preferred Time From is required.
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <label for="toTime" class="form-label">To</label>
                <input type="time" formControlName="toTime" id="toTime" class="form-control" required>
                <div
                    *ngIf="appointmentForm.get('toTime')?.invalid && (appointmentForm.get('toTime')?.dirty || appointmentForm.get('toTime')?.touched)"
                    class="text-danger"
                >
                    <div *ngIf="appointmentForm.get('toTime')?.errors?.['required']">
                        Preferred Time To is required.
                    </div>
                </div>
            </div>
        </div>

        <!-- Cancel and Submit Buttons -->
        <div class="row mb-3">
            <div class="col text-end">
                <button type="button" class="btn btn-secondary me-3" (click)="cancelAppointment()">Cancel</button>
                <button type="submit" class="btn btn-primary" [disabled]="appointmentForm.invalid">Done</button>
            </div>
        </div>
    </form>
</div>