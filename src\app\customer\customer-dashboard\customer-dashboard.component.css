/* General Styling */
body {
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    color: #333;
  }
  
  .border-orange {
    border: 2px solid orange !important;
  }
  
  /* Container and Rows */
  .container {
    padding: 20px;
  }
  
  .row {
    margin-bottom: 20px;
  }
  
  /* Appointment Section */
  #appointment-prompt {
    border: 2px solid #D85322;
    border-radius: 30px;
    padding: 20px;
    background-color: #fff;
  }
  
  #appointment-prompt p {
    margin: 0;
  }
  
  #appointment-prompt .btn-primary {
    background-color: orange;
    border-color: orange;
  }
  
  #appointment-prompt .btn-primary:hover {
    background-color: darkorange;
    border-color: darkorange;
  }
  
  /* Reminder Section */
  #reminder-card {
    border: 2px solid orange;
    border-radius: 30px; /* Increased border radius */
    padding: 20px;
    background: linear-gradient(to top, #D85322, #FE9652); /* Gradient from top to bottom */
    color: white;
    text-align: center;
  }
  
  #reminder-card .reminder-date {
    font-size: 1.5em;
    font-weight: bold;
  }
  
  #reminder-card .reminder-time {
    background-color: white;
    color: #ffa500;
    border-radius: 5px;
    display: inline-block;
    margin-top: 10px;
  }
  
  #reminder-text {
    font-size: 30px;
    text-align: left;
    color: #000000;
    font-weight: bold;
  }
  
  .fa-bell {
    font-size: 40px; /* Increase size of the bell icon */
  }
  
  #reminder-day, #doctor-name {
    font-size: 25px; /* Increase size of the day, date, and doctor name */
  }
  
  #reminder-date{
    font-size: 30px;
  }
  
  #appointment-time-box {
    width: 126px;
    height: 45px;
    border-radius: 30px !important;
    background-color: white;
  
  }
  
  #appointment-time {
    color: #D85322;
    font-size: 20px;
    font-weight: bold;
  }
  
  #doctor-name {
    font-size: 30px;
    font-weight: bold;
    text-align: left;
  }
  
  
  /* Current Appointments Section */
  #current-appointments, #previous-appointments {
    border: 2px solid #D85322;
    border-radius: 30px;
    padding: 20px;
    background-color: #fff;
  }
  
  #current-appointments .search-input {
    margin-bottom: 15px;
  }
  
  #current-appointments .table th,
  #current-appointments .table td {
    vertical-align: middle;
  }
  
  #current-appointments .btn-outline-orange {
    border-color: orange;
    color: orange;
  }
  
  #current-appointments .btn-outline-orange:hover {
    background-color: orange;
    color: white;
  }
  
  /* Statement 1 heading */
  #statement-heading {
    font-weight: bold;
  }
  
  /* Search bar */
  #search-input {
    background-color: rgb(255, 255, 255);
    border-radius: 30px;
    border: 1px solid #D85322;
    color: #D85322;
  }
  
  /* Icon buttons gap */
  #trash-button {
    padding-right: 10px;
  }
  
  /* Buttons border radius and specific styles */
  #view-details-button {
    border-radius: 30px;
    border: 1px solid  #D85322;
    background-color: transparent;
    color:  #D85322;
    margin-right: 10px;
  }
  
  /* Hover effect for view details button */
  #view-details-button:hover {
    background-color: #D85322;
    color: white;
  }
  
  #show-accept{
    border-radius: 30px;
    border: 1px solid  #DCD535;
    background-color: #DCD535;
    font-size: 12px;
    color:  #ffffff;
  }
  
  #show-accept-button:hover {
    background-color: #ffffff;
    color: #DCD535;
  }
  
  #show-accepted{
    border-radius: 30px;
    border: 1px solid  #0075FF;
    background-color: #0075FF;
    color:  #ffffff;
  }
  
  #show-accepted-button:hover {
    background-color: #ffffff;
    color: #0075FF;
  }
  
  #show-Paynow , #show-complete{
    border-radius: 30px;
    border: 1px solid  #FB751E;
    background-color: #FB751E;
    color:  #ffffff;
  }
  
  #show-Paynow-button:hover , #show-complete-button:hover {
    background-color: #ffffff;
    color: #FB751E;
  }
  
  #show-confirmed{
    border-radius: 30px;
    border: 1px solid  #00C820;
    background-color: #00C820;
    color:  #ffffff;
  }
  
  #show-confirmed-button:hover {
    background-color: #ffffff;
    color: #00C820;
  }
  
  
  
  /* Previously Completed Appointments Section */
  #completed-appointments {
    border: 2px solid orange;
    border-radius: 10px;
    padding: 20px;
    background-color: #fff;
  }
  
  #completed-appointments .search-input {
    margin-bottom: 15px;
  }
  
  #completed-appointments .btn-outline-orange {
    border-color: orange;
    color: orange;
  }
  
  #completed-appointments .btn-outline-orange:hover {
    background-color: orange;
    color: white;
  }
  
  #close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #D85322;
  }
  
  #close-button:hover {
    cursor: pointer;
  }
  
  .modal-body {
    padding-top: 0%;
  }
  
  .card-body {
    padding: 0%;
  }
  
  #card-title{
    font-size: 1rem;
    color: #000000;
    margin-bottom: 10px;
    padding-top: 0%;
  }
  
  /* Utility Classes */
  .mb-3 {
    margin-bottom: 1rem !important;
  }
  
  .p-3 {
    padding: 1rem !important;
  }
  
  .mt-auto {
    margin-top: auto !important;
  }
  
  .flex-column {
    display: flex;
    flex-direction: column;
  }
  
  .d-flex {
    display: flex !important;
  }
  
  .flex-grow-1 {
    flex-grow: 1 !important;
  }
  
  /* Specific styling adjustments for elements */
  .appointment-details {
    background-color: #fff;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .appointment-details .btn {
    margin-top: 10px;
  }
  
  .pending-acceptance {
    color: orange;
    font-weight: bold;
  }
  
  .completed {
    color: green;
    font-weight: bold;
  }
  
  /* Selction one button */
  #add-appointment-text {
    font-size: 30px;
    font-weight: bold;
    color: #B93426;
  }
  
  /* Specific styling for Make Appointment button */
  #make-appointment-btn {
    font-size: 20px;
    background: linear-gradient(to right, #333333, #FB751E);
    border: none;
    border-radius: 40px;
    color: white;
    padding: 10px 20px;
  }
  
  #make-appointment-btn:hover {
    background: linear-gradient(to right, #FF512F, #F09819);
    color: white;
  }
  