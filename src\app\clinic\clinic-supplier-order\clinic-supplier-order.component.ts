import { Component, OnInit } from '@angular/core';
import { ClinicService } from '../clinic.service';
import { Supplier } from 'src/app/supplier/supplier';
import Swal from 'sweetalert2';
import { SupplierInventoryItem, CartItemDetails, SupplierCategoryItemList, InventoryOrderRequestDto } from '../clinic';
import { Router } from '@angular/router';


@Component({
  selector: 'app-clinic-supplier-order',
  templateUrl: './clinic-supplier-order.component.html',
  styleUrls: ['./clinic-supplier-order.component.css']
})
export class ClinicSupplierOrderComponent implements OnInit{

  constructor(private clinicService:ClinicService,private router:Router){
  }

  suppliers: Supplier[] = [];
  items: SupplierInventoryItem[] = [];
  cart: CartItemDetails[] = [];

  supplierItemCategoryList:SupplierCategoryItemList[] = [
    { id: 1, name: "Dental Chairs and Accessories" },
    { id: 2, name: "Dental Equipment" },
    { id: 3, name: "Dental Instruments" },
    { id: 4, name: "Dental Implants" },
    { id: 5, name: "Impression Materials" },
    { id: 6, name: "Filling/Bonding and Cement Materials" },
    { id: 7, name: "Endodontic Materials and Items" },
    { id: 8, name: "Rotary Accessories" },
    { id: 9, name: "Medicaments" },
    { id: 10, name: "Reusable Items and Materials" },
    { id: 11, name: "Orthodontic Materials and Items" },
    { id: 12, name: "Digital Dental Items" }
  ];

  selectedCategory: string = '';
  searchTerm: string = '';
  currentView: string = 'suppliers';
  selectedSupplier: Supplier | null = null;
  selectedItem: SupplierInventoryItem | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 8;
  pageCount:number = 0;

  ngOnInit(): void {
    // Get teh Supplier Item Category List
    // this.clinicService.getSupplierItemCategories().subscribe((response)=>{
    //   this.supplierItemCategoryList = response
    // })
  }

  // When the category changes, update the suppliers according to the category
  onCategoryChange(event: any) {
    // event will retrieve the ITEM_CATEGORY_NAME
    const value = event;
    console.log('Selected Category:', value);
    this.clinicService.getSupplierListByCategoryName(value).subscribe((response)=>{
      console.table(response);
      this.suppliers = response
    })
  }

  visitStore(supplier: Supplier) {
    this.selectedSupplier = supplier;
    // Make the supplier
    this.items = []
    this.clinicService.getSupplierItemListByCategoryNameAndSupplierId(this.selectedCategory,this.selectedSupplier.supplierId,).subscribe((response)=>{
      this.items = response
    })
    this.currentView = 'items';
  }

  // Getters

  get filteredItems() {
    const filtered= this.items.filter(item =>
      item.inventoryName.toLowerCase().includes(this.searchTerm.toLowerCase())
    );

    this.pageCount = Math.ceil(filtered.length / this.itemsPerPage)
    return filtered;
  }

  get pages(): number[] {
    return Array.from({ length: this.pageCount }, (_, i) => i + 1);
  }

  get paginatedItems() {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredItems.slice(start, end);
  }


  showItemDetails(item: SupplierInventoryItem) {
    this.selectedItem = item;
    this.currentView = 'itemDetails';
  }

  backToItems() {
    this.currentView = 'items';
  }

  backToSuppliers() {
    this.currentView = 'suppliers';
    this.cart = []
  }


  // Cart Processes

  addToCart(item: SupplierInventoryItem) {
    const CartItemDetails = this.cart.find(ci => ci.item.inventoryItemId === item.inventoryItemId);
    if (CartItemDetails) {
      CartItemDetails.quantity += 1;
    } else {
      this.cart.push({ item, quantity: 1 });
    }

    Swal.fire({
      title:`${item.inventoryName} added to the cart`,
      icon:'success',
      toast:true,
    })
  }

  removeFromCart(CartItemDetails: CartItemDetails) {
    this.cart = this.cart.filter(ci => ci.item.inventoryItemId !== CartItemDetails.item.inventoryItemId);
    Swal.fire({
      title:`${CartItemDetails.item.inventoryName} removed from the cart`,
      icon:'warning',
      toast:true,
    })
  }

  increaseQuantity(CartItemDetails: CartItemDetails): void {
    CartItemDetails.quantity++;
  }

  decreaseQuantity(CartItemDetails: CartItemDetails): void {
    if (CartItemDetails.quantity > 1) {
      CartItemDetails.quantity--;
    }
  }

  openCartModal() {
    const cartModal = document.getElementById('cartModal');
    if (cartModal) {
      cartModal.style.display = 'block';
    }
  }

  closeCartModal() {
    const cartModal = document.getElementById('cartModal');
    if (cartModal) {
      cartModal.style.display = 'none';
    }
  }

  deleteSelectedItems(): void {
    this.cart = this.cart.filter(CartItemDetails => !CartItemDetails.selected);
  }

  savetheOrder():void{
    const userId = localStorage.getItem('userid');
    if(this.selectedSupplier && this.cart && this.cart.length >0 && userId){

      const inventoryOrderRequestDto: InventoryOrderRequestDto = {
        supplierId:this.selectedSupplier.supplierId,
        cartItemList:this.cart,
        userId:Number(userId)
      }

      this.clinicService.saveInventoryOrderRequest(inventoryOrderRequestDto).subscribe((resp:boolean)=>{
        if (resp) {
          Swal.fire({
            title:"Request Successfull",
            icon:'success',
            toast:true,
            timer:4000
          })
          this.cart = [];
          this.closeCartModal()
        }else{
          Swal.fire({
            title:"Request Unsuccessfull",
            icon:'error',
            toast:true,
            timer:4000
          })
        }
      })
    }
  }

  backToOrderListView(){
    this.router.navigate(['clinic/supplier-orders'])
  }


  // Pagination

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage -= 1;
    }
  }

  nextPage() {
    if (this.currentPage * this.itemsPerPage < this.filteredItems.length) {
      this.currentPage += 1;
    }
  }

  goToPage(page: number) {
    this.currentPage = page;
  }
}
