h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

.search-input {
  height: 40px;
  outline: none;
  box-shadow: none;
  border-radius: 5px;
  border: solid 1px rgb(200, 200, 200);
  padding-inline: 15px;
  position: absolute;
  right: 0px;
  font-size: 15px;
  font-family: "Inter", sans-serif;
}

.input-colored {
  border-color: #fb751e;
}

.search-input::placeholder {
  color: rgb(100, 100, 100);
  font-size: 13px;
}

.search-input:hover {
  border-color: #fb751e;
}
.search-input:focus {
  border-color: #fb751e;
}

.card-table-header {
  background: linear-gradient(to right, #fb751e, #b93426);
}

.text-orange {
  color: #fb751e;
}

.bg-orange {
  color: #fb751e;
}

.user-details-table {
  width: 100%;
  border-collapse: collapse;
}

.user-details-table tr td {
  font-size: 13px;
  padding: 10px 15px;
  border: 1px solid rgb(240, 240, 240);
}

.user-details-table tr td:first-child {
  font-size: 13px;
  color: rgb(50, 50, 50);
  width: 30%;
}

.user-details-table tr td:last-child {
  width: 70%;
  color: black;
  font-weight: 500;
}

.user-details-table tr td:last-child p {
  padding: 10px 15px;
}

.user-details-table tr td:last-child p:not(:last-child) {
  border-bottom: 1px solid rgb(240, 240, 240);
}

.custom-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  font-size: 13px;
  font-weight: 500;
  font-family: "Inter", sans-serif;
  color: transparent;
  background: linear-gradient(to left, #fb751e, #b93426);
  background-clip: text;
  box-shadow: none;
  border: none;
  text-align: start;
  outline: none;
  padding: 5px 20px;
}

.custom-select option {
  background-color: white;
  color: rgb(65, 65, 65);
  font-weight: 500;
  font-family: "Inter", sans-serif !important;
}

.custom-select option:hover {
  background: linear-gradient(to left, #fb751e, #b93426);
  color: white;
}

.text-gradient {
  background: linear-gradient(to left, #fb751e, #b93426);
  background-clip: text;
}

.cb-base {
  border: 1px solid rgb(230, 230, 230);
  width: fit-content;
  border-radius: 5px;
  margin-right: 15px;
}

.cb-base p{
  margin-block: auto;
}
.custom-check-box{
  appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 2px;
  border: 1px solid rgb(200,200,200);
  margin-block: auto;
}

.custom-check-box:checked{
  background: linear-gradient(to right, #fb751e, #b93426);
  border-radius: 3px;
  border: none;
}

.cb-base:has(.custom-check-box:checked) {
  border-color: #ffb482;
}

.pagination-button{
  border: 1px solid #fb751e;
  color: #fb751e;
  width: 40px;
  text-align: center;
 }

 .active{
   border: none;
   background: linear-gradient(to right, #fb751e, #b93426);
   color: white;
 }
