.send-orders-container {
    padding: 0;
}

.header-row,
.send-orders-list-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.laboratory-setups-container {
    display: flex;
    justify-content: flex-end;
}

.button-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.add-setup-button {
    width: 250px;
    border-radius: 40px;
    background: linear-gradient(to right, #FB751E, #B93426);
    color: white;
    border: none;
    padding: 10px;
    margin-bottom: 20px;
}

.see-all-button {
    width: 114px;
    margin-left: 90%;
    font-family: Inter;
    font-size: 15px;
    font-weight: 400;
    color: #D85322;
    background: none;
    border: #ddd;
    margin-bottom: 10px;
}

.header-row-h1 {
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    color: #000;
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
}

.search-bar {
    position: relative;
    margin: 20px 0;
    width: 100%;
    max-width: 300px;
}

#search-input {
    padding: 10px 30px;
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 40px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

.send-order-table {
    width: 100%;
    border-radius: 10px;
    border-collapse: collapse;
    margin-top: 20px;
}

.send-order-table th,
.send-order-table td {
    padding: 8px;
    text-align: left;
}

.send-order-table th {
    background-color: #FFB07D38;
    color: #000;
}

.send-order-table thead tr th {
    border-bottom: 2px solid #ddd;
}

.send-order-table tbody tr:nth-child(odd) {
    background-color: white;
}

.send-order-table tbody tr:nth-child(even) {
    background-color: rgba(255, 176, 125, 0.12);
}

.status {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
}

.actived {
    border: 1px solid;
    border-radius: 8px;
    width: 80px;
    border-color: #00C820;
    color: #00C820;
}

.inactived {
    width: 80px;
    background: #FFC1070D;
    border-color: #FF0000;
    border-radius: 8px;
    color: #FF0000;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-btn:hover,
.close-btn:focus {
    color: black;
}

.sidebar-container,
.main-content {
    height: calc(100vh - 80px);
}

.sidebar-container {
    width: 20%;
    overflow-y: auto;
}

.main-content {
    width: 80%;
    padding: 16px;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .sidebar-container,
    .main-content {
        width: 100%;
        display: block;
    }
    
    .send-order-table {
        display: none;
    }

    .card-list {
        display: block;
    }

    .order-card {
        border: 1px solid #ddd;
        border-radius: 10px;
        margin-bottom: 20px;
        padding: 15px;
        background-color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .pagination-container {
        justify-content: center;
    }
}

@media (min-width: 769px) {
    .card-list {
        display: none;
    }
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin: 20px 0;
}

.pagination {
    display: inline-flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.page-item {
    cursor: pointer;
}

.page-item.disabled .page-link {
    pointer-events: none;
    color: #ddd;
}

.page-item.active .page-link {
    background-color: #ff7e3b;
    border-color: #ff7e3b;
    color: white;
}

.page-link {
    padding: 0.5rem 0.75rem;
    color: #ff7e3b;
    background-color: #fff;
    border: 1px solid #ddd;
}