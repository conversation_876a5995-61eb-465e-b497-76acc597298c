<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12 g-0">
        <div class="w-100">
          <div class="px-2 col-12 d-inline-flex">
            <i class="bi bi-arrow-left" style="cursor: pointer;" (click)="backToOrderListView()"></i>
            <div class="ms-3">
              <h3 class="fs-5 m-0 p-0" style="font-weight: 600">
                Purchase Request
              </h3>
              <p class="text-black-50 m-0 p-0" style="font-size: 12px">
                View all Supplier Orders
              </p>
            </div>
          </div>
          <div *ngIf="currentView == 'items'  || currentView == 'itemDetails'" class="cart-icon" (click)="openCartModal()">
            <i class="bi bi-cart-fill"></i>
            <span class="badge" [class.has-items]="cart.length > 0">{{ cart.length }}</span>
          </div>
        </div>
        <hr class="border-secondary" />
        <div class="dynamic-section mt-5" [ngSwitch]="currentView">
          <!-- Supplier Details -->
          <div *ngSwitchCase="'suppliers'">

            <!-- Filtering part of the supplier selection -->

            <div class="col-12">
              <p class="p-0 m-0 mb-2" style="font-weight: 500; font-size: 14px">Select Supplier</p>
              <div class="filter-section">
                <select class="custom-select w-100" [(ngModel)]="selectedCategory" (ngModelChange)="onCategoryChange($event)">
                  <option [value]="supplierCategoryItem.name" *ngFor="let supplierCategoryItem of supplierItemCategoryList">{{supplierCategoryItem.name}}</option>
                </select>
              </div>
            </div>

            <!-- Filtering part of the supplier selection -->

            <!--  supplier table selection -->

            <div class="col-12 supplier-section border-0 mt-5">
              <h3 class="section-header" style="font-weight: 600;font-size: 18px;padding: 10px 15px;">All Suppliers Related to Your Selected Item</h3>
              <div class="supplier-table" *ngIf="suppliers.length > 0">
                <div  class="supplier-row" *ngFor="let supplier of suppliers; let odd = odd" [class.odd-row]="odd">
                  <div class="supplier-cell my-auto">
                    <p class="p-0 m-0 text-black-50" style="font-size: 12px; font-weight: 400;line-height: 15px;">{{ supplier.address }}</p>
                    <p class="p-0 m-0"  style="font-weight: 500; font-size: 14px;line-height: 15px;">{{ supplier.name }}</p>
                  </div>
                  <div class="supplier-cell my-auto text-black-50" style="font-size: 13px;">
                    <span class="text-black" style="font-weight: 500;">{{ supplier.tele }}</span>&nbsp;  /&nbsp;  <span class="text-black" style="font-weight: 500;">{{ supplier.tele }}</span>
                  </div>
                  <div class="supplier-cell my-auto">
                    <button class="btn visit-store-btn" (click)="visitStore(supplier)">Visit Store</button>
                  </div>
                </div>
              </div>
              <div *ngIf="suppliers.length == 0" class="w-100 g-0 text-center">
                <div class="col-12 py-5">
                  <h5 style="font-weight: 500;">Suppliers Unavalable</h5>
                  <p class="text-black-50 mt-2" style="font-size: 12px;">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Labore mollitia ipsum quae.
                    Inventore vitae, obcaecati esse voluptatem tenetur unde aperiam.
                  </p>
                </div>
              </div>
            </div>

            <!--  supplier table selection -->

          </div>
          <!-- Item Details -->
          <div *ngSwitchCase="'items'" class="w-100">
            <div class="col-12 w-100 py-4 pb-5 text-end position-relative">
              <div class="search-container position-absolute w-100 f-flex" style="right: 0; top: 0%;border: none !important;">
                <i class="bi bi-search search-icon"></i>
                <input type="text" class="form-control custom-input py-2" style="outline: none; box-shadow: none; border-radius: 5px;" placeholder=" Search item" [(ngModel)]="searchTerm">
              </div>

            </div>
            <div class="col-12 items-section">
              <h3 class="section-header" style="font-weight: 600;font-size: 18px;padding: 10px 15px;"><i (click)="backToSuppliers()" style="cursor: pointer;" class="bi-arrow-left pe-2 "></i> {{ selectedSupplier?.name }}</h3>
              <div class="item-grid"
                [ngStyle]="paginatedItems.length > 4 ? {'row-gap': '20px'} : {}"
                *ngIf="paginatedItems.length > 0">
                <div *ngFor="let item of paginatedItems" class="item-card" (click)="showItemDetails(item)">
                  <img [src]="item.imageURL !=null ? item.imageURL :'../../assets/images/dental-chair.png' " [alt]="item.inventoryName" />
                  <div class="item-name">{{ item.inventoryName }}</div>
                  <div class="item-stock" [class.in-stock]="item.inventoryQty > 5" [class.low-stock]="item.inventoryQty <= 5" style="display: inline-block;">
                    {{ item.inventoryQty > 5 ? 'In Stock' : 'Low Stock' }}
                  </div>
                  <button class="btn add-to-cart-btn" (click)="addToCart(item); $event.stopPropagation();">Add to Cart</button>
                </div>
              </div>
              <div *ngIf="paginatedItems.length == 0" class="w-100 g-0 text-center">
                <div class="col-12 py-5">
                  <h5 style="font-weight: 500;">Items Unavalable</h5>
                  <p class="text-black-50" style="font-size: 12px;">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Labore mollitia ipsum quae.
                    Inventore vitae, obcaecati esse voluptatem tenetur unde aperiam.
                  </p>
                </div>
              </div>
              <div *ngIf="items.length > 8" class="pagination">
                <button class="btn btn-outline-secondary" (click)="prevPage()" [disabled]="currentPage === 1">&lt;</button>
                <span *ngFor="let page of pages" class="page-number" [class.active]="currentPage === page" (click)="goToPage(page)">{{ page }}</span>
                <button class="btn btn-outline-secondary" (click)="nextPage()" [disabled]="(currentPage * itemsPerPage) >= filteredItems.length">&gt;</button>
              </div>
            </div>
          </div>
          <!-- Individual Item Details -->
          <div *ngSwitchCase="'itemDetails'" class="item-details-section position-relative border-0">
            <h3 class="section-header" style="font-weight: 600;font-size: 18px;padding: 10px 15px;"> <i (click)="backToItems()" style="cursor: pointer;" class="bi-arrow-left pe-2 "></i> {{ selectedItem?.inventoryName }}</h3>
            <div class="item-details-content">
              <div class="item-details-info">
                <div>
                  <p style="font-size: 13px;" class="text-black-50">Item Name & Code</p>
                  <p style="font-size: 15px; font-weight: 500;">{{ selectedItem?.inventoryName }}</p>
                </div>
                <div class="my-3">
                  <p style="font-size: 13px;" class="text-black-50">Availability</p>
                  <p style="font-size: 15px; font-weight: 500;">
                    <span [class.in-stock]="selectedItem?.inventoryQty! > 5" [class.low-stock]="selectedItem?.inventoryQty! <= 5">
                      {{ selectedItem?.inventoryQty! > 5 ? 'In Stock' : 'Low Stock' }}
                    </span>
                  </p>
                </div>
                <button class="btn request-now-btn mt-4" (click)="addToCart(selectedItem!)">Request Now</button><br>
              </div>
              <div class="item-details-image">
                <img [src]="selectedItem?.imageURL !=null ? selectedItem?.imageURL :'../../assets/images/dental-chair.png' "[alt]="selectedItem?.inventoryName" />
              </div>
            </div>
          </div>
        </div>
        <!-- Cart Modal -->
        <div id="cartModal" class="modal">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="btn-close" (click)="closeCartModal()" style="font-size: 20px; font-weight: bold;"></button>
              </div>
              <div class="modal-title-row">
                <h5 class="modal-title">Cart View</h5>
                <button class="btn btn-sm btn-delete" (click)="deleteSelectedItems()" style="font-size: 20px; color: red; margin-top: 5px;">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
              <div class="modal-body">
                <div *ngFor="let cartItem of cart" class="cart-item">
                  <input type="checkbox" class="cart-item-checkbox" [(ngModel)]="cartItem.selected">
                  <img [src]="cartItem.item.imageURL !=null ? cartItem.item.imageURL :'../../assets/images/dental-chair.png' " [alt]="cartItem.item.imageURL" class="cart-item-image">
                  <div class="cart-item-details">
                    <strong>{{ cartItem.item.inventoryName }}</strong> &nbsp;
                    <small>{{ cartItem.item.inventoryItemId }}</small>
                  </div>
                  <div class="cart-item-price d-none">LKR. {{ cartItem.item.inventoryPrice * cartItem.quantity }}</div>
                  <div class="cart-item-quantity">
                    <button class="btn btn-sm btn-quantity" (click)="decreaseQuantity(cartItem)">-</button>
                    <span>{{ cartItem.quantity }}</span>
                    <button class="btn btn-sm btn-quantity" (click)="increaseQuantity(cartItem)">+</button>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary add-item-btn" (click)="closeCartModal()">Add Other Item</button>
                <button type="button" class="btn btn-primary request-now-btn-model" (click)="savetheOrder()">Request Order</button>
              </div>
            </div>
          </div>
        </div>
        <!-- Cart Modal -->
      </div>
    </div>
  </div>
</div>
