
.sidebar-container {
    height: calc(100vh - 80px);
    width: 20%;
    overflow-y: auto;
    display: inline-block;
}

.main-content {
    height: calc(100vh - 80px);
    width: 80%;
    overflow-y: auto;
    padding: 16px;
    display: inline-block;
}

.container {
    padding: 20px;
}

.header-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#printButton {
    border: 1px solid #FB751E;
    width: 248px;
    height: 35px;
    top: 149px;
    gap: 0px;
    border-radius: 50px ;
    border: 1px 0px 0px 0px;
    opacity: 0px;

    }
.header-row-hm {
    color: black;
    width: 390px;
    top: 150px;
    font-size: 32px;
    left: 386px;
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: Inter;


}

.header-bottom-line {
    border-bottom: 1px solid #ccc;
    margin-top: 15px;
}



#header1 {
    height: 64px;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(90deg, #FB751E 49.4%, #DBB800 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: Inter, sans-serif;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 0;
}



.order-form-container {
    background-color: #FFFCFC;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 1000px;
    width: 190%;
    height: 85%;
    margin-left: 1%;
}

.top-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.service-details {
    max-width: 300px;
}

.service-details img {
    width: 100px;
    height: auto;
    display: block;
    margin-bottom: 10px;
}

.patient-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.patient-details input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;

    width: 462px;
    height: 37px;
    top: 883.69px;
    left: 913.16px;
    gap: 90px;
    border-radius: 5px 0px 0px 0px;
    border: 1px 0px 0px 0px;
    opacity: 0px;


}

.bottom-section {
    margin-top: 60px;
    gap: 300px;
}

.form-row {
    display: flex;
    gap: 40px;
    margin-bottom: 10px;
}

.form-row select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    width: 100%;

}

.form-row textarea {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    width: 48%;

}


.buttons {
    padding: 7px 20px;
    background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);
    color: #fff;
    border: none;
    border-radius: 18px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
    margin-left: 85%
}

.buttons:hover {
    background-color: #e64a19;
}

.form-row>div {
    flex: 1;
    min-width: 200px;
}

label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
    font-weight: bold;
}
 .size{
    width: 1000px;
    margin-left: 2%;
 }
