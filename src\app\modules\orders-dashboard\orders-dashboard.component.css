  .orders-container {
    padding: 20px;
  }

  .header-row,
  .send-orders-list-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .send-orders-list-row-new {
    width: 146px;
    height: 29px;
    top: 100px;
    left: 403px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 24px;
    font-weight: 500;
    line-height: 29.05px;
    text-align: center;
    color: #000;
  }

  .see-all-button {
    text-align: center;
    width: 114px;
    height: 0px;
    margin-right: 0px;
    margin-left: 90%;
    font-family: Inter;
    font-size: 15px;
    font-weight: 400;
    line-height: 18.15px;
    color: #D85322;
    background: none;
    border: #ddd;
  }

  .header-row-h1 {
    width: 174px;
    height: 39px;
    top: 149px;
    left: 403px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: center;
    color: #000;
  }

  .header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
  }

  .search-bar {
    position: relative;
    margin: 20px 0;
    width: 100%;
    max-width: 300px;
  }

  #search-input {
    padding: 10px;
    width: 100%;
    padding-left: 30px;
    border: 1px solid #ccc;
    border-radius: 40px;
    border-width: 30%;
  }
  .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
  }

  .send-order-table {
    text-align: center;
    width: 100%;
    border-radius: 10px;
    border-collapse: collapse;
    margin-top: 20px;
  }

  .send-order-table th,
  .send-order-table td {
    border: 1px solid #ddd;
    text-align: center;
    padding: 8px;
    border-width: 30%;
  }

  .send-order-table th {
    text-align: center;
    background-color: #FE9652;
    color: rgb(0, 0, 0);
  }

  .send-order-table thead tr th {
    text-align: center;
    border-bottom: 2px solid #ddd;
  }

  .profile-icon {
    color: #FF5722;
    margin-right: 8px;
    vertical-align: middle;
  }

  .status {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
    border-width: 30%;
  }

  .completed {
    width: 114px;
    height: 25px;
    top: 432px;
    left: 1155px;
    gap: 0px;
    border-radius: 18px 18px 18px 18px;
    border: 1px 0px 0px 0px;
    opacity: 0px;
    border: 1px solid #00C820;
    color: #00C820;
  }
  .cancelled {
    justify-content: center;
    text-align: center;
    width: 114px;
    height: 25px;
    top: 432px;
    left: 1155px;
    gap: 0px;
    border-radius: 18px 18px 18px 18px;
    border: 1px 0px 0px 0px;
    opacity: 0px;
    border: 1px solid #FF0000;
    color: #FF0000;
  }

  .in-progress{
    width: 114px;
    height: 25px;
    top: 432px;
    left: 1155px;
    gap: 0px;
    border-radius: 18px 18px 18px 18px;
    border: 1px 0px 0px 0px;
    opacity: 0px;
    border: 1px solid #0075FF;
    color: #0075FF;
  }

  .pending {
    text-align: center;
    width: 114px;
    height: 25px;
    top: 432px;
    left: 1155px;
    gap: 0px;
    border-radius: 18px 18px 18px 18px;
    border: 1px 0px 0px 0px;
    opacity: 0px;
    background: #FFC1070D;
    border: 1px solid #FFC107;
    color: #FFC107;
  }

  .received {
    border-radius: 40px;
    border-width: 30%;
    border: 1px solid;
    color: #0075FF;
    border-color: #0075FF;
  }

  .close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    text-align: right;
  }

  .close-btn:hover,
  .close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }

  form {
    display: flex;
    flex-direction: column;
  }

  form label {
    margin-top: 10px;
  }

  form input,
  form select,
  form textarea {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 5px;
    border-width: 30%;
  }

  .sidebar-container {
    height: calc(100vh - 80px);
    /* Adjust the height to account for the header */
    width: 20%;
    overflow-y: auto;
    display: inline-block;
  }

  .main-content {
    height: calc(100vh - 80px);
    /* Adjust the height to account for the header */
    width: 80%;
    overflow-y: auto;
    padding: 16px;
    display: inline-block;
  }

  @media (max-width: 768px) {
    .sidebar-container,
    .main-content {
      width: 100%;
      display: block;
    }
  }
