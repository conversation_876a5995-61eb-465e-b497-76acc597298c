<div class="container-fluid base-background" style="height: 100dvh;overflow-y: auto;">
  <div class="row h-100">
    <div class="col-12" style="height: 80px !important;">
      <app-default-navbar loggedUser="Hello Doctor" />
    </div>
    <div class="col-12" style="height: calc(100% - 80px) !important;">
      <div class="row h-100">
        <div class="col-11 m-auto py-5">
          <div class="row">
            <div class="col-12 col-sm-10 col-lg-8 col-xl-6 col-xxl-5 mx-auto bg-white p-5 bordered-container">
              <div class="row">
                <h3 class="fs-4 m-0 header">Clinic Registration</h3>
                <form [formGroup]="clinicForm" class="col-12 base-form" (ngSubmit)="onUserTempRegister()">
                  <div class="row">
                    <div class="col-12">
                      <label class="input-label" for="clinic-name">Clinic Name</label>
                      <input
                        type="text"
                        id="clinic-name"
                        name="clinic-name"
                        formControlName="clinicName"
                        [(ngModel)]="userTemp.mainName"
                        required

                      />
                      <div
                      class="px-1" style="font-weight: 500;"
                        *ngIf="
                          clinicForm.get('clinicName')?.invalid &&
                          (clinicForm.get('clinicName')?.dirty ||
                            clinicForm.get('clinicName')?.touched)
                        "
                      >
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('clinicName')?.errors?.['required']"
                        >
                          Clinic Name is required.
                        </small>
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('clinicName')?.errors?.['pattern']"
                          >Clinic Name can only contain letters, numbers, spaces, and ".()/,".</small
                        >
                      </div>
                      <small *ngIf="isClinicRegistered" class="text-danger">
                        {{ clinicNameExistsMessage }}
                      </small>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12">
                      <label class="input-label" for="address">Address</label>
                      <textarea
                        type="text"
                        id="address"
                        name="address"
                        formControlName="address"
                        [(ngModel)]="userTemp.address"
                        rows="1">
                      </textarea>
                      <div
                      class="px-1" style="font-weight: 500;"
                        *ngIf="
                          clinicForm.get('address')?.invalid &&
                          (clinicForm.get('address')?.dirty ||
                            clinicForm.get('address')?.touched)
                        "
                      >
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('address')?.errors?.['required']"
                        >
                          Address is required.
                        </small>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12">
                      <label class="input-label" for="email-address">Email Address</label>
                      <input
                        type="email"
                        id="email-address"
                        name="email-address"
                        formControlName="email"
                        [(ngModel)]="userTemp.userEmail"
                        (ngModelChange)="updateEmail()"
                      />
                      <div
                      class="px-1" style="font-weight: 500;"
                        *ngIf="
                          clinicForm.get('email')?.invalid &&
                          (clinicForm.get('email')?.dirty ||
                            clinicForm.get('email')?.touched)
                        "
                      >
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('email')?.errors?.['required']"
                        >
                          Email is required.
                        </small>
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('email')?.errors?.['email']"
                        >
                          Invalid email format.
                        </small>
                      </div>
                      <small *ngIf="isEmailRegistered" class="text-danger">
                        {{ userEmailExistsMessage }}
                      </small>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12 col-xl-6 d-grid">
                      <label class="input-label" for="district">District</label>
                      <select
                        name="district"
                        id="district"
                        formControlName="district"
                        [(ngModel)]="userTemp.district"
                        (change)="onDistrictChange($event)"
                        class=""
                      >
                        <option value="" disabled selected>Select</option>
                        <option *ngFor="let district of districts" [value]="district">
                          {{ district }}
                        </option>
                      </select>
                      <div
                      class="px-1" style="font-weight: 500;"
                        *ngIf="
                          clinicForm.get('district')?.invalid &&
                          (clinicForm.get('district')?.dirty ||
                            clinicForm.get('district')?.touched)
                        "
                      >
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('district')?.errors?.['required']"
                        >
                          District is required.
                        </small>
                      </div>
                    </div>
                    <div class="col-12 col-xl-6 d-grid mt-3 mt-xl-0">
                      <label class="input-label" for="city">City</label>
                      <select
                        name="city"
                        id="city"
                        formControlName="city"
                        [(ngModel)]="userTemp.city"
                      >
                        <option value="" disabled selected>Select District</option>
                        <option *ngFor="let city of cities" [value]="city">
                          {{ city }}
                        </option>
                      </select>
                      <div
                      class="px-1" style="font-weight: 500;"
                        *ngIf="
                          clinicForm.get('city')?.invalid &&
                          (clinicForm.get('city')?.dirty || clinicForm.get('city')?.touched)
                        "
                      >
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('city')?.errors?.['required']"
                        >
                          City is required.
                        </small>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12 col-xl-6">
                      <label for="contact-number" class="input-label">Contact Number</label>
                      <input
                        type="text"
                        id="contact-number"
                        name="contact-number"
                        formControlName="tele"
                        [(ngModel)]="userTemp.contactNumber"
                      />
                      <div
                      class="px-1"
                      style="font-weight: 500;"
                        *ngIf="
                          clinicForm.get('tele')?.invalid &&
                          (clinicForm.get('tele')?.dirty || clinicForm.get('tele')?.touched)
                        "
                      >
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('tele')?.errors?.['required']"
                        >
                          Contact Number is required.
                        </small>
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('tele')?.errors?.['pattern']"
                        >
                          Invalid Contact number.
                        </small>
                      </div>
                    </div>
                    <div class="col-12 col-xl-6 mt-3 mt-xl-0">
                      <label for="contact-person" class="input-label">Contact Person</label>
                      <input
                        type="text"
                        id="contact-person"
                        name="contact-person"
                        formControlName="contactPerson"
                        [(ngModel)]="userTemp.contactPerson"
                      />
                      <div class="px-1"
                      style="font-weight: 500;" *ngIf="clinicForm.get('contactPerson')?.invalid && (clinicForm.get('contactPerson')?.dirty || clinicForm.get('contactPerson')?.touched)">
                        <small class="text-danger" *ngIf="clinicForm.get('contactPerson')?.errors?.['required']">Contact Person is required.</small>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12 col-xl-6 position-relative">
                      <label for="password" class="input-label">Password</label>
                      <input
                        type="{{ passwordVisible ? 'text' : 'password' }}"
                        id="password"
                        name="password"
                        formControlName="password"
                        [(ngModel)]="userTemp.userPassword"
                        class="form-control"
                        [ngClass]="{
                  'is-invalid': clinicForm.get('password')?.invalid &&
                  (clinicForm.get('password')?.dirty || clinicForm.get('password')?.touched)
                }"
                      />

                      <!-- Password visibility toggle button -->
                      <button
                        type="button"
                        class="show-password"
                        (click)="togglePasswordVisibility()"
                        style="
                              position: absolute;
                              right: 25px;
                              top: 48px;
                              transform: translateY(-50%);
                              padding: 0;
                              border: none;
                              background: none;
                              color: #6c757d;
                              cursor: pointer;
                              "
                      >
                        <i [ngClass]="passwordVisible ? 'bi bi-eye-fill' : 'bi bi-eye-slash-fill'"></i>
                      </button>

                      <!-- Error messages for validation -->
                      <div class="px-1" style="font-weight: 500;" *ngIf="clinicForm.get('password')?.invalid && (clinicForm.get('password')?.dirty || clinicForm.get('password')?.touched)">
                        <small class="text-danger" *ngIf="clinicForm.get('password')?.errors?.['required']">
                          Password is required.
                        </small>
                        <small class="text-danger" *ngIf="clinicForm.get('password')?.errors?.['minlength'] || clinicForm.get('password')?.errors?.['pattern']">
                          Password must be at least 8 characters, including an uppercase letter, lowercase letter, digit, and special character.
                        </small>
                      </div>
                    </div>

                    <div class="col-12 col-xl-6 mt-3 mt-xl-0">
                      <label for="repassword" class="input-label">Re-enter Password</label>
                      <input
                        type="password"
                        id="repassword"
                        name="repassword"
                        formControlName="rePassword"
                      />
                      <div
                      class="px-1" style="font-weight: 500;"
                        *ngIf="
                          clinicForm.get('rePassword')?.invalid &&
                          (clinicForm.get('rePassword')?.dirty ||
                            clinicForm.get('rePassword')?.touched)
                        "
                      >
                        <small
                          class="text-danger"
                          *ngIf="clinicForm.get('rePassword')?.errors?.['required']"
                        >
                          Please re-enter the password.
                        </small>
                      </div>
                      <small
                        class="text-danger"
                        *ngIf="clinicForm.errors?.['mismatch'] && clinicForm.get('rePassword')?.dirty"
                        >Password do not match.</small
                      >
                    </div>
                  </div>

                  <app-primary-action-button buttonType="submit" class="d-grid mt-4" [buttonHeight]="45" buttonUI="primary" buttonText="Register" #RegisterButton />
                  <button type="submit" #RegisterButton class="register-button d-none">Register</button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
