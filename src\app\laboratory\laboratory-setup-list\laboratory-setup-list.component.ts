import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LaboratoryService } from '../laboratory.service';
import { LaboratorySetup } from '../laboratory';

@Component({
  selector: 'app-laboratory-setup-list',
  templateUrl: './laboratory-setup-list.component.html',
  styleUrls: ['./laboratory-setup-list.component.css']
})
export class LaboratorySetupListComponent implements OnInit {
  searchTerm: string = '';
  currentPage: number = 1;
  itemsPerPage: number = 10;
  laboratorySetups: LaboratorySetup[] = [];
  visiblePages: number[] = [];
  paginatedLaboratorySetups: LaboratorySetup[] = [];
  totalPages: number = 0;
  showAll: boolean = false;

  constructor(private laboratoryService: LaboratoryService, private router: Router) {}

  ngOnInit(): void {
    const userIdString = localStorage.getItem('userid');
    const userId = userIdString ? parseInt(userIdString, 10) : null;
    if (userId !== null) {
      this.loadLaboratorySetups(userId);
    } else {
      console.error('User ID is not available in localStorage.');
    }
  }

  loadLaboratorySetups(userId: number): void {
    this.laboratoryService.getLaboratorySetupByUserId(userId).subscribe(
      (data: LaboratorySetup[]) => {
        console.log('Received setups:', data);
        this.laboratorySetups = data;
        this.updatePagination();
        this.updatePaginatedItems();
      },
      (error) => {
        console.error('Error fetching laboratory setups:', error);
      }
    );
  }

  filteredLaboratorySetups(): LaboratorySetup[] {
    return this.laboratorySetups.filter(setup =>
      setup.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      setup.laboratoryCategoryId.laboratoryCategoryName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      setup.laboratorySubCategoryId.laboratorySubCategoryName.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  updatePagination(): void {
    this.totalPages = Math.ceil(this.laboratorySetups.length / this.itemsPerPage);
    this.visiblePages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  updatePaginatedItems(): void {
    if (this.showAll) {
      this.paginatedLaboratorySetups = this.laboratorySetups; 
    } else {
      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;
      this.paginatedLaboratorySetups = this.laboratorySetups.slice(startIndex, endIndex);
    }
  }

  goToPage(page: number): void {
    if (page > 0 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePaginatedItems();
    }
  }

  toggleSidebar(): void {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
    }
  }

  navigateAddLaboratorySetup(): void {
    this.router.navigate(['/laboratory/add-service']);
  }

  toggleShowAll(): void {
    this.showAll = !this.showAll;  
    this.updatePaginatedItems();   
  }
}
