import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment'; // Import environment config
import { ClinicServices, Schedule } from './schedule';


@Injectable({
  providedIn: 'root',
})
export class ScheduleService {
  private readonly baseURL = environment.apiUrl; // Backend URL

  constructor(private http: HttpClient) {}

  // Get the authentication token from localStorage
  getAuthToken(): string | null {
    return window.localStorage.getItem('auth_token');
  }

  // Generic method for making HTTP requests
  request(
    method: string,
    url: string,
    data: any = {},
    params?: any,
    responseType: 'json' = 'json' // Default to JSON response type
  ): Observable<any> {
    let headers = new HttpHeaders();

    // Set Authorization header if auth token exists
    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    // Prepare request options
    const options: any = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
      observe: 'body' as const, // Ensure we are only observing the response body
      responseType: responseType, // Set the response type, usually 'json'
    };

    // Execute appropriate HTTP method
    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  // Method to get all schedules
  getSchedules(): Observable<Schedule[]> {
    return this.request('GET', '/scheduleList', {});
  }

  // Method to get a specific schedule by ID
  getScheduleById(id: number): Observable<Schedule> {
    return this.request('GET', `/getScheduleById/${id}`, {});
  }

  // Method to save a new schedule
  saveSchedule(schedule: Schedule): Observable<Schedule> {
    return this.request('POST', '/saveSchedule', schedule);
  }

  // Method to update a schedule by ID
  updateSchedule(scheduleId: number, schedule: Schedule): Observable<Schedule> {
    return this.request('PUT',`/updateSchedule/${scheduleId}`, { ...schedule });
  }

  // Method to delete a schedule by ID
  deleteSchedule(scheduleId: number): Observable<any> {
    return this.request('DELETE', `/deleteSchedule/${scheduleId}`);
  }

  getClinicIdByUserId(userid: number): Observable<number> {
    return this.request('GET', `/clinicId?userId=${userid}`, {});
  }

   // New Methods for Clinic Services

  // Method to save a new clinic service
  saveClinicService(clinicService: ClinicServices): Observable<ClinicServices> {
    return this.request('POST', '/clinic-services/save', clinicService);
  }

  // Method to update a clinic service by ID
  updateClinicService(clinicServiceId: number, clinicService: ClinicServices): Observable<ClinicServices> {
    return this.request('PUT', `/clinic-services/update/${clinicServiceId}`, clinicService);
  }

  // Method to delete a clinic service by ID
  deleteClinicService(clinicServiceId: number): Observable<any> {
    return this.request('DELETE', `/clinic-services/delete/${clinicServiceId}`);
  }

  // Method to get all clinic services
  getAllClinicServices(): Observable<ClinicServices[]> {
    return this.request('GET', 'clinic-services/all', {});
  }

  // Method to get a specific clinic service by ID
  getClinicServiceById(clinicServiceId: number): Observable<ClinicServices> {
    return this.request('GET', `/clinic-services/${clinicServiceId}`, {});
  }

  getClinicServicesByClinicId(clinicId: number): Observable<ClinicServices[]> {
    return this.request('GET', `/clinic-services/by-clinic/${clinicId}`, {});
    
}

}
