.content-wrapper {
  display: flex;
  flex-direction: row;
  padding-bottom: 40px;
}

.services-page {
  max-width: 100%;
  margin: 0 auto;
  margin-top: -3.8%;
}

.item-box {
  border: 1px solid #ddd;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.027);
  background-color: #fff;
}


.padding {
  padding: 15px;
}

.item-header {
  display: flex;
  border-radius: 10px 10px 0px 0px;
  justify-content: space-between;
  align-items: center;
  padding: 6px;
  margin-bottom: 10px;
  color: #ffffff;
  background: linear-gradient(to left, #B93426, #FB751E);
}

.item-title {
  font-weight: 600;
  margin-left: 10px;
  font-size: 15px;
}

.item-contact {
  font-size: 15px;
  font-weight: 500;
  text-align: left;
  margin-right: 10px;
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.item-details {
  display: flex;
  flex-direction: column;
  padding-right: 5px;
}

.category-name {
  font-size: 15px;
  font-weight: 600;
}

.sub-category-name {
  color: gray;
  font-size: 13px;
  font-weight: 400;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-button {
  background: linear-gradient(90deg, #B93426, #FB751E);
  color: #fff;
  padding: 5px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
}

.details-button {
  font-style: italic;
  color: #FB751E;
  font-size: 13px;
  background: none;
  border: none;
  cursor: pointer;
}

.actived {
  color: #00C820;
}

.inactived {
  color: #FF0000;
}

.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(29, 8, 0, 0.838);
  z-index: 9999;
}

.modal-content {
  width: 50%;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
}

.details-table {
  width: 100%;
  border-collapse: collapse;
}

.details-table td {
  padding: 8px;
  border: 1px solid #ddd;
}

.details-table td:first-child {
  font-size: 13px;
  font-weight: 500;
  color: rgb(100, 100, 100);
}

.details-table td:last-child {
  font-size: 14px;
}


h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

input:focus {
  box-shadow: none;
}

.search-input {
  height: 40px;
  outline: none;
  box-shadow: none;
  border-radius: 5px;
  border: solid 1px rgb(200, 200, 200);
  padding-inline: 15px;
  position: absolute;
  right: 0px;
  font-size: 15px;
}

.search-input::placeholder {
  color: rgb(100, 100, 100);
  font-size: 13px;
}

.search-input:hover {
  border-color: #fb751e;
}
.search-input:focus {
  border-color: #fb751e;
}

.custom-select{
  border: 1px solid rgb(230,230,230);
  border-radius: 5px;
  height: 45px;
  background-color: transparent;
  font-size: 14px;
  outline: none;
}

.card-base{
  padding-top: 25px;
}

.card-base:nth-child(even){
  padding-left: 25px;
}
.card-base:nth-child(odd){
  padding-right: 25px;
}

@media screen and (max-width: 992px) {
  .card-base:nth-child(even){
    padding-inline:15px;
  }
  .card-base:nth-child(odd){
    padding-inline:15px;
  }
}
