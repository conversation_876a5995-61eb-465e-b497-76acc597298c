import { Appointments } from './appointments';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Customer } from './customer';


@Injectable({
  providedIn: 'root'
})
export class AppointmentsService {

  private readonly baseURL = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAuthToken(): string | null {
    return window.localStorage.getItem('auth_token');
  }

  request(
    method: string,
    url: string,
    data: any,
    params?: any
  ): Observable<any> {
    let headers = new HttpHeaders();

    if (this.getAuthToken() !== null) {
      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());
    }

    const options = {
      headers: headers,
      params: new HttpParams({ fromObject: params }),
    };

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get(this.baseURL + url, options);
      case 'POST':
        return this.http.post(this.baseURL + url, data, options);
      case 'PUT':
        return this.http.put(this.baseURL + url, data, options);
      case 'DELETE':
        return this.http.delete(this.baseURL + url, options);
      // Add more HTTP methods as needed
      default:
        throw new Error('Unsupported HTTP method');
    }
  }

  //appointments

  saveAppointments(appointments: Appointments): Observable<any> {
    return this.request('POST', '/saveAppointments', appointments);
  }

  appointmentsList(): Observable<Appointments[]> {
    return this.request('GET', '/appointmentsList', {});
  }

  getAppointmentsById(id: number): Observable<Appointments> {
    return this.request('GET', '/getAppointmentsById/{id}', {});
  }

  updateAppointments(id: number, appointments: Appointments): Observable<object> {
    return this.request('PUT', '/updateAppointments/{id}', appointments);
  }

  deleteAppointments(id: number): Observable<any> {
    return this.request('DELETE', '/deleteAppointments/{id}', {});
  }

  //customer
  saveCustomer(customer:Customer): Observable<any>{
    return this.request('POST', '/saveCustomer' ,customer);
  }

  findClinics(fromDate: string, fromTime: string, nearestCity: string, preferredService: number): Observable<any> {
    const params = new HttpParams()
      .set('date', fromDate)
      .set('time', fromTime)
      .set('city', nearestCity)
      .set('serviceId', preferredService.toString());

    return this.http.get(`${this.baseURL}/getClinicForAppointment`, { params });
  }



}

