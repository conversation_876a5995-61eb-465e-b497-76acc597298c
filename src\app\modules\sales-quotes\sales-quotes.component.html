<div class="sales-quotes-container">
    <div class="header-search-container">
        <h2>Sales Quotes</h2>
        <div class="search-container">
            <input type="text" placeholder="Search">
            <button class="search-button">
          <i class="fa fa-search"></i>
        </button>
        </div>
    </div>
    <hr>
    <br>
    <div class="table-container">

        <table>
            <thead>
                <tr>
                    <th>Supplier Name</th>
                    <th>Address</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let quote of quotes">
                    <td><input type="checkbox"> {{ quote.supplierName }}</td>
                    <td>{{ quote.address }}</td>
                    <td><button class="view-quote-button">View Quote</button></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>