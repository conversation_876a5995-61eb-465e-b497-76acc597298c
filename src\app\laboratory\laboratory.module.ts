import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LaboratoryRoutingModule } from './laboratory-routing.module';
import { LaboratoryDashboardComponent } from './laboratory-dashboard/laboratory-dashboard.component';
import { LaboratoryLayoutComponent } from './laboratory-layout/laboratory-layout.component';
import { LaboratoryNavbarComponent } from './components/laboratory-navbar/laboratory-navbar.component';
import { LaboratorySidebarComponent } from './components/laboratory-sidebar/laboratory-sidebar.component';
import { LaboratorySetupListComponent } from './laboratory-setup-list/laboratory-setup-list.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AddLaboratoryServicesComponent } from './add-laboratory-services/add-laboratory-services.component';
import { OrderDetailsPopupComponent } from './components/order-details-popup/order-details-popup.component';
import { RejectPopupComponent } from './components/reject-popup/reject-popup.component';
import { RejectReasonPopupComponent } from './components/reject-reason-popup/reject-reason-popup.component';
import { RejectionReasonPopupComponent } from './components/rejection-reason-popup/rejection-reason-popup.component';
import { AcceptPopupComponent } from './components/accept-popup/accept-popup.component';
import { LaboratoryClinicOrderListComponent } from './laboratory-clinic-order-list/laboratory-clinic-order-list.component';
import { AuthModule } from '../auth/auth.module';

@NgModule({
  declarations: [
    LaboratoryDashboardComponent,
    LaboratoryLayoutComponent,
    LaboratoryNavbarComponent,
    LaboratorySidebarComponent,
    LaboratorySetupListComponent,
    AddLaboratoryServicesComponent,
    OrderDetailsPopupComponent,
    RejectPopupComponent,
    RejectReasonPopupComponent,
    RejectionReasonPopupComponent,
    AcceptPopupComponent,
    LaboratoryClinicOrderListComponent
  ],
  imports: [
    CommonModule,
    LaboratoryRoutingModule,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class LaboratoryModule {}
