import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-order-details-popup',
  templateUrl: './order-details-popup.component.html',
  styleUrls: ['./order-details-popup.component.css']
})
export class OrderDetailsPopupComponent {
  @Input() order: any;
  @Output() closePopup = new EventEmitter<void>();

  close() {
    this.closePopup.emit();
  }
  orders = [
    {
      orderID: 1,
      patientName: 'ravindra gunasekara',
      address: 'MyDent Dental Clinic,Colombo 12',
      contact: '0112 700 700',
      service: 'Dental Crowns',
      description: 'porcelains and ceramics',
      qty: 1,
      price: 'LKR. 52 000'
    }
  ];

  showAcceptPopup = false;
  selectedOrder: any;

  // Method to open the Accept popup
  openAcceptPopup(order: any) {
    this.selectedOrder = order; // Set the order you want to pass to the popup
    this.showAcceptPopup = true; // Show the popup
  }

  // Method to close the Accept popup
  closeAcceptPopup() {
    this.showAcceptPopup = false; // Hide the popup
  }

  // Handle the order acceptance action
  handleOrderAccepted(order: any) {
    console.log('Order accepted:', order);
    // Implement further actions (e.g., update the order status)
  }
}
