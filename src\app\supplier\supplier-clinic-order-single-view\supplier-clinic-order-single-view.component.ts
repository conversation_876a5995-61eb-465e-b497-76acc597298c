import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  QuoteDetails,
  SupplierOrderDetails,
  SupplierOrderHeader,
} from '../supplier';
import { SupplierService } from '../supplier.service';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';

@Component({
  selector: 'app-supplier-clinic-order-single-view',
  templateUrl: './supplier-clinic-order-single-view.component.html',
  styleUrls: ['./supplier-clinic-order-single-view.component.css'],
})
export class SupplierClinicOrderSingleViewComponent implements OnInit {
  orderForm: FormGroup;
  submitted = false;
  supplierOrderHeaderId: number = 0;
  supplierOrderDetailsList: SupplierOrderDetails[] = [];
  supplierOrderHeader?: SupplierOrderHeader;
  formattedDate: string = 'dd - mm - yyyy  |  hh : mm : ss';
  orderStatus: string = 'Unavailable';
  testQty: number = 0;
  constructor(
    private supplierServices: SupplierService,
    private activatedRoute: ActivatedRoute,
    private formBuilder: FormBuilder
  ) {
    this.orderForm = this.formBuilder.group({});
  }

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe((params) => {
      try {
        this.supplierOrderHeaderId = Number(params['id']);
        if (this.supplierOrderHeaderId) {
          this.supplierServices
            .getOrderDetailsBySupplierId(this.supplierOrderHeaderId)
            .subscribe((response) => {
              if (response) {
                this.supplierOrderDetailsList = response;
                this.supplierOrderHeader = this.supplierOrderDetailsList[0]?.supplierOrderHeader;
                this.formattedDate = this.formatDateTime(this.supplierOrderHeader?.createdDateTime?.toString() || '');
                this.orderStatus = this.getOrderStatusDetails(this.supplierOrderHeader?.orderStatus?.toString() || 'Unavailable');
                if (this.supplierOrderDetailsList && this.supplierOrderDetailsList.length > 0) {
                  this.initializeFormControls();
                }
              }
            });
        }
      } catch (error) {
        console.log(error);
      }
    });
  }

  transformResponseToMap(response: any): Map<number, QuoteDetails> {
    const resultMap = new Map<number, QuoteDetails>();
    Object.entries(response).forEach(([key, value]) => {
      const match = key.match(/-(\d+)$/);

      if (match) {
        const orderDetailId = parseInt(match[1], 10);
        const fieldType = key.split('-')[0];

        if (!resultMap.has(orderDetailId)) {
          resultMap.set(orderDetailId, {
            orderDetailId: 0,
            acceptedinventoryQty: 0,
            inventorySellingPrice: 0,
            requestedinventoryQty: 0,
          });
        }

        const entry = resultMap.get(orderDetailId);
        if (entry) {
          entry.orderDetailId = orderDetailId;
          if (fieldType === 'qty') {
            entry.acceptedinventoryQty = Number(value);
          } else if (fieldType === 'price') {
            entry.inventorySellingPrice = Number(value);
          }
        }
      }
    });
    return resultMap;
  }

  private initializeFormControls(): void {
    this.supplierOrderDetailsList.forEach((detail) => {
      const itemId = detail.supplierOrderDetailsId;

      // Add qty control (read-only)
      this.orderForm.addControl(
        `qty-${itemId}`,
        this.formBuilder.control(detail.requestedItemQty, [])
      );

      // Add price control (with validation)
      this.orderForm.addControl(
        `price-${itemId}`,
        this.formBuilder.control(detail.supplierInventory.price, [
          this.priceValidator(detail.supplierInventory.price),
        ])
      );
    });
  }

  priceValidator(maxPrice: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (value !== null && (value <= 0 || value > maxPrice)) {
        return { invalidPrice: true };
      }
      return null;
    };
  }

  onSubmit(): void {
    this.submitted = true;
    if (this.orderForm.invalid) {
      console.log('Form is invalid!');
      return;
    }
    console.table(Array.from(this.transformResponseToMap(this.orderForm.value).values()));
  }



  private formatDateTime(dateTime: string): string {
    const [year, month, day, hour, minute, second] = dateTime
      .split(',')
      .map(Number);
    const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(
      day
    ).padStart(2, '0')}`;
    const formattedTime = `${String(hour).padStart(2, '0')} : ${String(
      minute
    ).padStart(2, '0')} : ${String(second).padStart(2, '0')}`;
    return `${formattedDate}&nbsp;&nbsp;&nbsp; | &nbsp;&nbsp;&nbsp;${formattedTime}`;
  }

  getOrderStatusDetails(orderStatus: string) {
    switch (orderStatus) {
      case 'CLINIC_CREATED':
        return 'Received';
      case 'CLINIC_REJECTED':
        return 'Cancelled By Clinic';
      case 'SUPPLIER_APPROVED':
        return 'Approved By You';
      case 'SUPPLIER_COMPLETED':
        return 'Completed';
      case 'SUPPLIER_PROCESSING':
        return 'Ongoing';
      case 'SUPPLIER_REJECTED':
        return 'Rejected By You';
      default:
        return 'Unknown Status';
    }
  }

  handleClick(){
    alert('Clicked')
  }
}
