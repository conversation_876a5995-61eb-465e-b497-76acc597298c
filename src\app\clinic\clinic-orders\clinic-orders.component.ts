import { Component, OnInit, ViewChild } from '@angular/core';
import { ClinicSideBarComponent } from '../components/clinic-side-bar/clinic-side-bar.component';

interface Orders {
  orderID: string;
  date: string;
  exDate: string;
  laboratory: string;
  value:string;
  status: string;
}
@Component({
  selector: 'app-clinic-orders',
  templateUrl: './clinic-orders.component.html',
  styleUrls: ['./clinic-orders.component.css']
})

export class ClinicOrdersComponent implements OnInit {
  @ViewChild(ClinicSideBarComponent) sideBar!: ClinicSideBarComponent;

  toggleSidebar() {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
    }
  }

  orders: Orders[] = [
    { orderID: '001', date: '01-05-2024 ', exDate: '2024/04/02', laboratory: 'Asia Laboratory', value:'Rs.12000', status: 'Pending' },
    { orderID: '002', date: '01-05-2024 ', exDate: '2024/04/02', laboratory: 'Medi Laboratory', value: 'Rs.12600', status: 'Completed' },
    { orderID: '003', date: '01-05-2024 ', exDate: '2024/04/02', laboratory: 'Asia Laboratory', value: 'Rs.12800', status: 'Completed' },
    { orderID: '004', date: '01-05-2024 ', exDate: '2024/04/02', laboratory: 'Medi Laboratory', value: 'Rs.18000', status: 'Pending' },
    { orderID: '005', date: '01-05-2024 ', exDate: '2024/04/02', laboratory: 'Asia Laboratory', value: 'Rs.17000', status: 'Completed' },
    { orderID: '006', date: '01-05-2024 ', exDate: '2024/04/02', laboratory: 'Medi Laboratory', value: 'Rs.18500', status: 'Pending' },
    { orderID: '007', date: '01-05-2024 ', exDate: '2024/04/02', laboratory: 'Asia Laboratory', value: 'Rs.15700', status: 'Completed' },

  ];

  searchTerm: string = '';
  showModal: boolean = false;
  newPurchasingDashboard: Orders = { orderID: '', date: '', exDate: '', laboratory: '',value: '', status: 'Pending' };


  filteredorders() {
    return this.orders.filter(orders =>
      orders.orderID.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }


  showAll = false;


  currentPage: number = 1;
  visiblePages: number[] = [];
  maxVisiblePages: number = 4; // Show only 4 pages at a time

  ngOnInit(): void {
    this.updateVisiblePages();
  }

  goToPage(page: number): void {
    if (page >= 1) { // No upper limit on page number
      this.currentPage = page;
      this.updateVisiblePages();
    }
  }

  updateVisiblePages(): void {
    let startPage = Math.max(this.currentPage - Math.floor(this.maxVisiblePages / 2), 1);
    let endPage = startPage + this.maxVisiblePages - 1;

    this.visiblePages = Array.from({ length: this.maxVisiblePages }, (_, i) => startPage + i);
  }

  showPopup = false;
  selectedOrder: any = null;

  openPopup(order: any) {
    this.selectedOrder = order;
    this.showPopup = true;
  }

  closePopup() {
    this.showPopup = false;
    this.selectedOrder = null;
  }

}
