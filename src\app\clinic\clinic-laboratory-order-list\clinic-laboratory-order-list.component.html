<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12">
        <!-- Header -->
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-4">
                <h3 class="fs-5 m-0 p-0" style="font-weight: 600">
                  Laboratory Orders
                </h3>
                <p class="text-black-50 m-0 p-0" style="font-size: 12px">
                  View all Laboratory Orders
                </p>
              </div>
              <div class="col-8 position-relative text-end">
                <input class="search-input position-relative" [(ngModel)]="searchTerm" type="text" placeholder="Search From Here">&nbsp;&nbsp;&nbsp;
                <app-primary-action-button class="" buttonText="New Lab Order" buttonUI="secondary" buttonType="button" (buttonClicked)="goToOrderCreatePage()"></app-primary-action-button>
              </div>
            </div>
            <div class="row my-4">
              <hr class="border-secondary" />
            </div>
          </div>
        </div>
        <!-- Header -->

        <!-- Body -->
         <div class="row">
          <div class="col-12">
            <div class="row gy-3">
              <div class="col-12 text-end mb-3 d-flex justify-content-end">
                <input type="checkbox" [(ngModel)]="showAll" class="custom-check-box my-auto" id="">&nbsp;&nbsp;<label class="text-black-50 my-auto" style="font-size: 13px;font-weight: 500;">Show All</label>
              </div>
              <!-- table-header -->
              <div class="col-12 p-3 py-2 card-table-header" style="border-radius: 5px;">
                <div class="row card-table-header my-1">
                  <div class="col-6 my-auto text-start">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Laboratory Name & Address - Status</h6>
                  </div>
                  <div class="col-4 my-auto text-center" style="border-inline: 1px solid  white;">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Requested & Expected Date</h6>
                  </div>
                  <div class="col-2 text-center my-auto">
                    <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Actions</h6>
                  </div>
                </div>
              </div>
              <!-- table-header -->

              <!-- table-body -->
              <div class="col-12 p-3" *ngFor="let order of showAll ? filteredClinicLaboratoryOrders:paginatedClinicLaboratoryOrders"  style="border: 1px solid rgb(230,230,230); background-color: rgb(254, 254, 254); border-radius: 5px;">
                <div class="row">
                  <div class="col-6 my-auto">
                    <div class="d-flex">
                      <!-- <p class="alert px-2 my-auto py-1 me-3 border-1" style="border-radius: 5px; font-size: 11px; font-weight: 500; width: 100px;height: 25px; text-align: center; border-color: #ffcba9;color: #fb751e; background-color: #fff3e9;">ORD-{{order.laboratoryOrderId < 10 ? "0"+order.laboratoryOrderId:order.laboratoryOrderId}}</p> -->
                      <p class="alert px-1 my-auto py-0 pt-1 me-3 border-1" style="border-radius: 5px; font-size: 12px; font-weight: 600; color: #fb751e;line-height: 9px;">ORD-{{order.laboratoryOrderId < 10 ? "0"+order.laboratoryOrderId:order.laboratoryOrderId}}</p>
                      <div class="mt-2 mt-lg-0 text-center d-none" style="width: 150px;" ngSwitch="orderHeader.orderStatus.toString()">
                        <p *ngSwitchCase="'CLINIC_CREATED'" class="alert py-1 my-auto alert-info"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px;">
                          Created
                        </p>

                        <p *ngSwitchCase="'SUPPLIER_APPROVED'" class="alert py-1 my-auto alert-warning"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Supplier Approved
                        </p>

                        <p *ngSwitchCase="'SUPPLIER_REJECTED'" class="alert py-1 my-auto alert-danger"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Rejected
                        </p>

                        <p *ngSwitchCase="'CLINIC_REJECTED'" class="alert py-1 my-auto alert-danger"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Cancelled
                        </p>

                        <p *ngSwitchCase="'SUPPLIER_COMPLETED'" class="alert py-1 my-auto alert-success"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Completed
                        </p>

                        <p *ngSwitchCase="'SUPPLIER_PROCESSING'" class="alert py-1 my-auto alert-warning"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Pending for Quote
                        </p>

                        <p *ngSwitchDefault class="alert py-1 my-auto alert-secondary"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px;">
                          <!-- {{orderHeader.orderStatus.toString()}} -->
                           No Status
                        </p>
                      </div>
                    </div>
                    <div class="d-grid d-lg-flex px-1 my-auto">
                      <h6 class="my-auto pe-0 pe-lg-3" style="font-weight: 500; font-size: 14px;">{{order.laboratorySetupId.laboratoryId.name +" - "+order.laboratorySetupId.laboratoryId.address }}</h6>
                      <div class="mt-2 mt-lg-0 text-center" style="width: 100px;" [ngSwitch]="order.status.toString()">
                        <p *ngSwitchCase="'CLINIC_CREATED'" class="alert py-1 my-auto alert-info"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px;">
                          Created
                        </p>

                        <p *ngSwitchCase="'Pending'" class="alert py-1 my-auto alert-warning px-2"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Pending
                        </p>

                        <p *ngSwitchCase="'SUPPLIER_REJECTED'" class="alert py-1 my-auto alert-danger"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Rejected
                        </p>

                        <p *ngSwitchCase="'CLINIC_REJECTED'" class="alert py-1 my-auto alert-danger"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Cancelled
                        </p>

                        <p *ngSwitchCase="'SUPPLIER_COMPLETED'" class="alert py-1 my-auto alert-success"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Completed
                        </p>

                        <p *ngSwitchCase="'SUPPLIER_PROCESSING'" class="alert py-1 my-auto alert-warning"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; ">
                          Pending for Quote
                        </p>

                        <p *ngSwitchDefault class="alert py-1 my-auto alert-secondary"
                              style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px;">
                          <!-- {{orderHeader.orderStatus.toString()}} -->
                           No Status
                        </p>
                      </div>
                    </div>
                    <!-- <h6 class="my-auto pe-0 pe-xl-3" style="font-weight: 500; font-size: 14px;">{{orderHeader.supplier.name +" - "+orderHeader.supplier.address}}</h6>&nbsp;&nbsp;&nbsp; -->

                  </div>
                  <div class="col-4 my-auto text-center" style="border-inline: 1px solid  rgb(230,230,230);">
                    <p class="text-balck-50 my-auto" style="font-size: 13px;font-weight: 500;">
                      {{ order.laboratoryOrderDate }} &nbsp;&nbsp;&nbsp;&nbsp;
                      <i class="bi bi-arrow-right" style="color: rgb(168, 168, 168);"></i> &nbsp;&nbsp;&nbsp;&nbsp;
                      {{ order.expectedDate }}
                    </p>
                  </div>
                  <div class="col-2 text-center my-auto">
                    <label class="view-odrer-button my-auto me-2" title="View Order"><i class="bi bi-eye"></i></label>
                  </div>
                </div>
              </div>
              <!-- table-body -->

              <!-- Pagination -->
              <div class="col-12">
                <div *ngIf="filteredClinicLaboratoryOrders.length > itemsPerPage && !showAll" class="row mt-4 position-relative">
                  <div class="col-12 d-flex justify-content-end g-0">
                    <button (click)="goToPage(currentPage - 1)" [disabled]="currentPage === 1" class="alert bg-light me-2 border-secondary-subtle" style="font-size: 13px; padding: 10px 15px;">
                      <i class="bi-chevron-left"></i>
                    </button>
                    <div *ngFor="let page of visiblePages" (click)="goToPage(page)"
                        [class.active]="currentPage === page"
                        class="alert pagination-button fw-bold me-2"
                        style="font-size: 13px; padding: 10px 15px;">
                      {{ page }}
                    </div>
                    <button (click)="goToPage(currentPage +1)" [disabled]="currentPage === totalPages" class="alert bg-light border-secondary-subtle" style="font-size: 13px; padding: 10px 15px;">
                      <i class="bi-chevron-right"></i>
                    </button>
                  </div>
                </div>
              </div>
              <!-- Pagination -->

            </div>
          </div>
         </div>
        <!-- Body -->
      </div>
    </div>
  </div>
</div>

