<div class="container mt-5">
    <div id="notificationHeader" class="mb-4">
      <h2 style="font-weight: bold; text-align: left;">Notifications</h2>
    </div>
    <div class="container">
      <div class="row" style="margin-bottom: 10px;">
        <div class="col-6 d-flex">
          <button id="filterButton" class="filterButton">
            <i class="fas fa-filter"></i> Filters
          </button>
        </div>
        <div class="col-6 d-flex justify-content-end">
          <button id="markAllReadButton" class="mark_all_read" (click)="markAllAsRead()">
            <i class="fas fa-check-double"></i> Mark all as read
          </button>
          <button id="deleteSelectedButton" class="delete" (click)="deleteSelectedNotifications()" [disabled]="selectedNotifications.size === 0">
            <i class="fas fa-trash"></i> Delete Selected
          </button>
        </div>
      </div>
    </div>

  <div id="notificationList">
    <!-- Today Notifications -->
    <div class="notification-header">
      <hr class="line">
    <h5 class="mb-3 header-text" style="text-align: center; margin: 10px;">Today</h5>
      <hr class="line">
    </div>
    <div *ngIf="getTodayNotifications().length > 0; else noTodayNotifications">
      <div *ngFor="let notification of getTodayNotifications()"
           class="card mb-3"
           [ngClass]="{'unread': !notification.isRead}">
        <div class="card-body d-flex align-items-center" style="margin-left: 10px;">
          <div class="form-check me-3">
            <input class="form-check-input" type="checkbox" [id]="'notification-' + notification.id"
                   (change)="toggleNotificationSelection(notification.id)"
                   [checked]="selectedNotifications.has(notification.id)"
                   style="border: 3px solid #ff6600; background-color: #daa17c;">
          </div>
          <div class="profile-icon me-3">
            <i class="fas fa-user"></i>
          </div>
          <div class="notification-content" (click)="openNotificationDetails(notification)">
            <h6 class="card-title">{{ notification.subject }}</h6>
            <p class="card-text text-truncate">{{ notification.message }}</p>
            <span class="notification-date">{{ notification.date | date:'dd MMM yyyy • HH:mm a' }}</span>
          </div>
        </div>
      </div>
    </div>
    <ng-template #noTodayNotifications>
      <p style="text-align: center;">No notifications for today.</p>
    </ng-template>

    <!-- Yesterday Notifications -->
    <div class="notification-header">
      <hr class="line">
    <h5 class="mb-3 mt-4 header-text" style="text-align: center; margin: 10px;">Yesterday</h5>
      <hr class="line">
    </div>
    <div *ngIf="getYesterdayNotifications().length > 0; else noYesterdayNotifications">
      <div *ngFor="let notification of getYesterdayNotifications()"
           class="card mb-3"
           [ngClass]="{'unread': !notification.isRead}">
        <div class="card-body d-flex align-items-center" style="margin-left: 10px;">
          <div class="form-check me-3">
            <input class="form-check-input" type="checkbox" [id]="'notification-' + notification.id"
                   (change)="toggleNotificationSelection(notification.id)"
                   [checked]="selectedNotifications.has(notification.id)"
                   style="border: 3px solid #ff6600; background-color: #daa17c;">
          </div>
          <div class="profile-icon me-3">
            <i class="fas fa-user"></i>
          </div>
          <div class="notification-content" (click)="openNotificationDetails(notification)">
            <h6 class="card-title">{{ notification.subject }}</h6>
            <p class="card-text text-truncate">{{ notification.message }}</p>
            <span class="notification-date">{{ notification.date | date:'dd MMM yyyy • HH:mm a' }}</span>
          </div>
        </div>
      </div>
    </div>
    <ng-template #noYesterdayNotifications>
      <p style="text-align: center;">No notifications for yesterday.</p>
    </ng-template>

    <!-- Previous Notifications -->
    <div class="notification-header">
      <hr class="line">
    <h5 class="mb-3 mt-4 header-text" style="text-align: center; margin: 10px;">Previous</h5>
      <hr class="line">
    </div>
    <div *ngIf="getPreviousNotifications().length > 0; else noPreviousNotifications">
      <div *ngFor="let notification of getPreviousNotifications()"
           class="card mb-3"
           [ngClass]="{'unread': !notification.isRead}">
        <div class="card-body d-flex align-items-center" style="margin-left: 10px;">
          <div class="form-check me-3">
            <input class="form-check-input" type="checkbox" [id]="'notification-' + notification.id"
                   (change)="toggleNotificationSelection(notification.id)"
                   [checked]="selectedNotifications.has(notification.id)"
                   style="border: 3px solid #ff6600; background-color: #daa17c;">
          </div>
          <div class="profile-icon me-3">
            <i class="fas fa-user"></i>
          </div>
          <div class="notification-content" (click)="openNotificationDetails(notification)">
            <h6 class="card-title">{{ notification.subject }}</h6>
            <p class="card-text text-truncate">{{ notification.message }}</p>
            <span class="notification-date">{{ notification.date | date:'dd MMM yyyy • HH:mm a' }}</span>
          </div>
        </div>
      </div>
    </div>
    <ng-template #noPreviousNotifications>
      <p style="text-align: center;">No previous notifications.</p>
    </ng-template>
  </div>


  <!-- Modal for notification details -->
  <div id="notificationModal" class="modal fade" [ngClass]="{'show': selectedNotification}" [style.display]="selectedNotification ? 'block' : 'none'" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 id="modalTitle" class="modal-title">{{ selectedNotification?.subject }}</h5>
          <button id="modalCloseButton" type="button" class="btn-close" (click)="closeNotificationDetails()"></button>
        </div>
        <div id="modalBody" class="modal-body">
          <p>{{ selectedNotification?.message }}</p>
          <small id="modalDate" class="text-muted">{{ selectedNotification?.date | date:'dd MMM yyyy • HH:mm a' }}</small>
        </div>
      </div>
    </div>
  </div>
