<app-default-navbar loggedUser="Hello Laboratory"/>
<div class="registration-page">
  <div class="registration-card">
    <h3>Laboratory Registration</h3>
    <button class="backtoselection-button" (click)="navigateUserSelection()">Selection Menu</button>
    <form [formGroup]="laboratoryForm" (ngSubmit)="onUserTempRegister()">
      <div class="form-row">
        <div class="form-group">
          <label for="lab-name">Laboratory Name</label>
          <input type="text" id="lab-name" name="lab-name" formControlName="laboratoryName" [(ngModel)]="userTemp.mainName"/>
          <div *ngIf="laboratoryForm.get('laboratoryName')?.invalid && (laboratoryForm.get('laboratoryName')?.dirty || laboratoryForm.get('laboratoryName')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('laboratoryName')?.errors?.['required']">Laboratory Name is required.</small>
            <small class="text-danger" *ngIf="laboratoryForm.get('laboratoryName')?.errors?.['pattern']">Laboratory Name can only contain letters, numbers, spaces, and ".()/,".</small>
          </div>
          <small class="text-danger" *ngIf="isLaboratoryRegistered">{{ laboratoryNameExistsMessage }}</small>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="address">Address</label>
          <textarea type="text" id="address" name="address" formControlName="address" [(ngModel)]="userTemp.address" rows="1"></textarea>
          <div *ngIf="laboratoryForm.get('address')?.invalid && (laboratoryForm.get('address')?.dirty || laboratoryForm.get('address')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('address')?.errors?.['required']">Address is required.</small>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" name="email" formControlName="email" [(ngModel)]="userTemp.userEmail" (ngModelChange)="updateEmail()"/>
          <div *ngIf="laboratoryForm.get('email')?.invalid && (laboratoryForm.get('email')?.dirty || laboratoryForm.get('email')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('email')?.errors?.['required']">Email is required.</small>
            <small class="text-danger" *ngIf="laboratoryForm.get('email')?.errors?.['email']">Invalid email format.</small>
          </div>
          <small class="text-danger" *ngIf="isEmailRegistered">{{ userEmailExistsMessage }}</small>
        </div>
      </div>

      <!-- <div class="form-row">
        <div class="form-group">
          <label for="district">District</label>
          <select name="district" id="district" formControlName="district" [(ngModel)]="laboratory.state" (change)="onDistrictChange($event)">
            <option *ngFor="let district of districts" [value]="district">{{ district }}</option>
          </select>
          <div *ngIf="laboratoryForm.get('district')?.invalid && (laboratoryForm.get('district')?.dirty || laboratoryForm.get('district')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('district')?.errors?.['required']">District is required.</small>
          </div>
        </div>

        <div class="form-group">
          <label for="city">City</label>
          <select name="city" id="city" formControlName="city" [(ngModel)]="laboratory.city">
            <option *ngFor="let city of cities" [value]="city">{{ city }}</option>
          </select>
          <div *ngIf="laboratoryForm.get('city')?.invalid && (laboratoryForm.get('city')?.dirty || laboratoryForm.get('city')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('city')?.errors?.['required']">City is required.</small>
          </div>
        </div>
      </div> -->

      <div class="form-row">
        <div class="form-group">
          <label for="contactNumber">Contact Number</label>
          <input type="text" id="contactNumber" name="contactNumber" formControlName="tele" [(ngModel)]="userTemp.contactNumber"/>
          <div *ngIf="laboratoryForm.get('tele')?.invalid && (laboratoryForm.get('tele')?.dirty || laboratoryForm.get('tele')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('tele')?.errors?.['required']">Contact Number is required.</small>
            <small class="text-danger" *ngIf="laboratoryForm.get('tele')?.errors?.['pattern']">Invalid Contact number.</small>
          </div>
        </div>

        <div class="form-group">
          <label for="contactPerson">Contact Person</label>
          <input type="text" id="contactPerson" name="contactPerson" formControlName="contactPerson" [(ngModel)]="userTemp.contactPerson"/>
          <div *ngIf="laboratoryForm.get('contactPerson')?.invalid && (laboratoryForm.get('contactPerson')?.dirty || laboratoryForm.get('contactPerson')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('contactPerson')?.errors?.['required']">Contact Person is required.</small>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" formControlName="password" [(ngModel)]="userTemp.userPassword"/>
          <div *ngIf="laboratoryForm.get('password')?.invalid && (laboratoryForm.get('password')?.dirty || laboratoryForm.get('password')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('password')?.errors?.['required']">Password is required.</small>
            <small class="text-danger" *ngIf="laboratoryForm.get('password')?.errors?.['minlength']">Password must be at least 8 characters long.</small><br>
            <small class="text-danger" *ngIf="laboratoryForm.get('password')?.errors?.['pattern']">Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.</small>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Re-enter Password</label>
          <input type="password" id="confirmPassword" name="confirmPassword" formControlName="confirmPassword"/>
          <div *ngIf="laboratoryForm.get('confirmPassword')?.invalid && (laboratoryForm.get('confirmPassword')?.dirty || laboratoryForm.get('confirmPassword')?.touched)">
            <small class="text-danger" *ngIf="laboratoryForm.get('confirmPassword')?.errors?.['required']">Please re-enter the password.</small>
          </div>
          <small class="text-danger" *ngIf="laboratoryForm.errors?.['mismatch'] && laboratoryForm.get('confirmPassword')?.dirty">Password do not match.</small>
        </div>
      </div>
      <button type="submit" #RegisterButton class="register-button">Register</button>
    </form>
  </div>

</div>
