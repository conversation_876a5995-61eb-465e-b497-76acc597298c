import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Doctor } from './doctor';
import { DoctorList } from '../clinic/doctor-list/doctorList';
import { HttpService } from '../http.service';

@Injectable({
  providedIn: 'root',
})
export class DoctorService {
  searchDoctorsByName(searchTerm: string) {
    throw new Error('Method not implemented.');
  }
  constructor(private httpService: HttpService) {}

  saveDoctor(doctor: Doctor): Observable<any> {
    return this.httpService.request('POST', '/saveDoctor', doctor);
  }

  getDoctorList(): Observable<DoctorList[]> {
    return this.httpService.request('GET', '/doctorList', {});
  }

  getDoctorById(id: number): Observable<Doctor> {
    return this.httpService.request('GET', `/getDoctorById/${id}`, {});
  }

  updateDoctor(id: number, doctor: Doctor): Observable<object> {
    return this.httpService.request('PUT', `/updateDoctor/${id}`, doctor);
  }

  deleteDoctor(id: number): Observable<any> {
    return this.httpService.request('DELETE', `/deleteDoctor/${id}`, {});
  }

  checkSlmcNumber(regNo: string): Observable<any> {
    const params = { regNo };
    return this.httpService.request('GET', `/checkSlmcNumber`, null, params);
  }

  getDoctorsByUserId(userId: string): Observable<any> {
    const url = `/api/doctors/${userId}`;
    return this.httpService.request('GET', url, null);
  }

  deleteClinicDoctor(doctorId: number): Observable<void> {
    const userId = localStorage.getItem('userid');
    const url = `/api/remove/${userId}/${doctorId}`;
    return this.httpService.request('DELETE', url, null);
  }

  assignDoctorToClinic(userId: string, doctorId: number): Observable<any> {
    const url = `/api/assignDoctor/${userId}/${doctorId}`;
    return this.httpService.request('POST', url, null);
  }

  getClinicListByUserId() : Observable<any> {
    const userId = localStorage.getItem('userid');
    if (userId != null) {
      return this.httpService.request('GET',`/getClinicsListByDoctorId/${userId}`, null);
    }else{
      return of(null);
    }
  }

  getMyAppointmentList(){
    const userId = localStorage.getItem('userid');
    if (userId != null) {
      return this.httpService.request('GET',`/getAppointmentListByDoctor_UserId/${userId}`, null);
    }else{
      return of(null);
    }
  }

}
