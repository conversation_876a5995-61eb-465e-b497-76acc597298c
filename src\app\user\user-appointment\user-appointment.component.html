<!-- <div class="container-fluid">
  <div class="row">
    <div class="col-3">
      <app-side-bar></app-side-bar>
    </div>
    <div class="col-9">
      <div class="row">
        <div class="col-12">
          <app-profile-header></app-profile-header>
        </div>
      </div>
      <div class="row">

        <div class="col-12">
          <app-appointment></app-appointment>
        </div>
      </div>
    </div>
  </div>
</div> -->

<div class="container mt-3">
  <!-- 1st Row -->
  <div class="row mb-3">
    <div class="col-md-6 d-flex flex-column">
      <div id="appointment-prompt" class="flex-grow-1 d-flex flex-column">
        <div class="row mb-2">
          <div class="col">
            <p class="text-left" id="add-appointment-text">Add Appointment in your schedule now</p>
          </div>
        </div>
        <div class="row mt-auto">
          <div class="col text-right d-flex flex-row-reverse">
            <button class="btn btn-primary" id="make-appointment-btn">
              <i class="fa fa-arrow-circle-right mr-5"></i> Make Appointment
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6 d-flex flex-column">
      <div id="reminder-card" class="flex-grow-1 d-flex flex-column">
        <div class="row mb-2">
          <div class="col-10">
            <p id="reminder-text">Reminder</p>
          </div>
          <div class="col-2">
            <i class="fas fa-bell fa-3x"></i>
          </div>
        </div>
        <div class="row mt-auto">
          <div class="col-2 text-center">
            <div id="reminder-day">Mon</div>
            <div id="reminder-date">24</div>
          </div>
          <div class="col-6">
            <p id="doctor-name">Dr Angelo Kevin</p>
          </div>
          <div class="col-4 text-center" id="appointment-time-box">
            <p id="appointment-time" class="pt-2">10:00 AM</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 2nd Row -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="px-4" id="current-appointments">
        <div class="row mb-2">
          <div class="col-md-6">
            <div class="row">
              <div class="col" id="statement-heading">Your Current Appointment</div>
            </div>
            <div class="row">
              <div class="col">Overview of your newly appointments</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="row">
              <div class="col text-right d-flex flex-row-reverse" id="icon-buttons">
                <i class="fas fa-ellipsis-h"></i>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col">
                <input type="text" class="form-control" placeholder="Search" id="search-input">
              </div>
            </div>
          </div>
        </div>
        <div class="row mb-2">
          <div class="col text-right">
            <button class="btn btn-danger mr-2"><i class="fas fa-trash"></i></button>&nbsp;
            <button class="btn btn-warning"><i class="fas fa-clock"></i></button>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="30%">Date</th>
                  <th width="55%">Appointment Details</th>
                  <th width="15%">Clinic Type</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>2024-06-25</td>
                  <td>
                    <div>Appointment #123</div>
                    <div>10:00 - 11:00 AM</div>
                    <button id="view-details-button" class="btn btn-sm mt-2">View Details</button>
                  </td>
                  <td>
                    General
                    <div class="mt-3">
                      <button
                        id="show-accept"
                        class="btn btn-warning btn-sm"
                        (click)="showAcceptanceButtons()"
                        *ngIf="!acceptanceButtonsVisible && !paymentConfirmed"
                        [disabled]="acceptanceButtonsDisabled || paymentConfirmed"
                      >
                        Pending Acceptance
                      </button>
                      <div *ngIf="acceptanceButtonsVisible && !paymentConfirmed">
                        <button id="show-accepted" class="btn btn-primary btn-sm mt-2 mr-2" disabled>Accepted</button>
                        <button id="show-Paynow" class="btn btn-warning btn-sm mt-2" (click)="showPaymentModal()">Pay Now</button>
                      </div>
                      <div *ngIf="paymentConfirmed">
                        <button id="show-confirmed" class="btn btn-success btn-sm mt-2">Appointment Confirmed</button>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- 3rd Row -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="px-4" id="previous-appointments">
        <div class="row mb-2">
          <div class="col-md-6">
            <div class="row">
              <div class="col"  id="statement-heading">Previously Completed Appointments</div>
            </div>
            <div class="row">
              <div class="col">Overview of your newly appointments</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="row">
              <div class="col text-right d-flex flex-row-reverse">
                <i class="fas fa-ellipsis-h"></i>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col">
                <input type="text" class="form-control" placeholder="Search" id="search-input">
              </div>
            </div>
          </div>
        </div>
        <div class="row mb-2">
          <div class="col text-right">
            <button class="btn btn-danger mr-2"><i class="fas fa-trash"></i></button>&nbsp;
            <button class="btn btn-warning"><i class="fas fa-clock"></i></button>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="80%">Appointment Number</th>
                  <th width="20%" class="mx-auto">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <div>Appointment #123</div>
                    <div><i class="fas fa-user"></i></div>
                  </td>
                  <td>
                    <div>General</div>
                    <button id="view-details-button" class="btn btn-outline-orange btn-sm mt-2 mr-2">View Details</button>
                    <button id="show-complete" class="btn btn-warning btn-sm mt-2">Complete</button>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div>Appointment #124</div>
                    <div><i class="fas fa-user"></i></div>
                  </td>
                  <td>
                    <div>General</div>
                    <button id="view-details-button" class="btn btn-outline-orange btn-sm mt-2 mr-2">View Details</button>
                    <button id="show-complete" class="btn btn-warning btn-sm mt-2">Complete</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Payment Modal -->
<div class="modal" tabindex="-1" id="paymentModal" #paymentModal>
  <div class="modal-dialog">
    <div class="modal-content" id="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modal-title">Payment</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="close-button" (click)="closeModal()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="card-body">
          <h5 class="card-title" id="card-title">Select Payment Method</h5>
          <button id="payment_btn">
            <img src="../../assets/images/Visa.png" alt="Visa" id="payment_image"></button>
          <button id="payment_btn">
            <img src="../../assets/images/logo-master-card-free-download-PNG.png" alt="MasterCard" id="payment_image"></button>
        </div>
        <form>
          <div class="form-group">
            <input type="text" class="form-control" id="cardHolderName" placeholder="Enter name">
            <label for="cardHolderName">Card Holder Name</label>
          </div>
          <div class="form-group">
            <input type="text" class="form-control" id="cardNumber" placeholder="XXXX - XXXX - XXXX">
            <label for="cardNumber">Card Number</label>
          </div>
          <div class="form-group">
            <div class="row">
              <div class="col-md-6">
                <input type="text" class="form-control" id="expiryDate" placeholder="MM/YY">
                <label for="expiryDate">Expiry Date</label>
              </div>
              <div class="col-md-6">
                <input type="text" class="form-control" id="cvn" placeholder="Enter CVN">
                <label for="cvn">CVN</label>
              </div>
            </div>
          </div>
          <input type="checkbox" id="save_method" name="save_method" value="save_method">
          <label for="save_method">&nbsp;&nbsp;Save my Details for future payment</label><br><br>
          <button type="button" class="btn btn-success" id="payment" (click)="confirmPayment()">Pay Now</button>
        </form>
      </div>
    </div>
  </div>
</div>



