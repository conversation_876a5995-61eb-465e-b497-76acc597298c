import { Component, OnInit } from '@angular/core';
import { LaboratoryService } from '../laboratory.service';
import { LaboratoryClinicOrder } from '../laboratory';
import { Router } from '@angular/router';

@Component({
  selector: 'app-laboratory-clinic-order-list',
  templateUrl: './laboratory-clinic-order-list.component.html',
  styleUrls: ['./laboratory-clinic-order-list.component.css'],
})
export class LaboratoryClinicOrderListComponent implements OnInit {
  // showModal: boolean = false;
  // maxVisiblePages: number = 2; 
  searchTerm: string = '';
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 0;
  visiblePages: number[] = [];
  showAll: boolean = false;
  showPopup: boolean = false;
  selectedOrder: any = null;
  laboratoryClinicOrders: LaboratoryClinicOrder[] = [];
  paginatedClinicLaboratoryOrders: LaboratoryClinicOrder[] = [];

  constructor(
    private laboratoryService: LaboratoryService, private router: Router
  ) {}

  // filteredOrders() {
  //   return this.laboratoryClinicOrders.filter(order =>
  //     order.laboratoryOrderId.toString().includes(this.searchTerm.toLowerCase())
  //   );
  // }

  filteredOrders() {
    const orders = this.showAll ? this.laboratoryClinicOrders : this.paginatedClinicLaboratoryOrders;
    console.log('Filtered orders:', orders); // Debug log
    return orders.filter(order =>
      order.laboratoryOrderId.toString().includes(this.searchTerm.toLowerCase())
    );
  }
  

  ngOnInit(): void {
    this.fetchOrders();
  }

  fetchOrders(): void {
    const user_id = Number(localStorage.getItem('userid'));
    console.log(user_id)
    
    this.laboratoryService.getClinicOrdersByLabUserId(user_id).subscribe(
      (data) => {
        console.log('Fetched orders:', data); // Debug log
        this.laboratoryClinicOrders = data;
        this.updatePagination();
        this.updatePaginatedItems();
      },
      (error) => {
        console.error('Error fetching laboratory orders:', error);
      }
    );
    
  }

  updatePagination(): void {
    this.totalPages = Math.ceil(this.laboratoryClinicOrders.length / this.itemsPerPage);
    this.visiblePages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  updatePaginatedItems(): void {
    if (this.showAll) {
      this.paginatedClinicLaboratoryOrders = this.laboratoryClinicOrders;
    } else {
      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;
      this.paginatedClinicLaboratoryOrders = this.laboratoryClinicOrders.slice(startIndex, endIndex);
    }
    console.log('Paginated items:', this.paginatedClinicLaboratoryOrders); // Debug log
  }
  

  goToPage(page: number): void {
    if (page > 0 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePaginatedItems();
    }
  }

  toggleShowAll(): void {
    this.showAll = !this.showAll;
    this.currentPage = 1;
    this.updatePaginatedItems();
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';

    const dateParts = dateString.split('/');
    if (dateParts.length !== 3) {
      console.error('Invalid date format: ', dateString);
      return '';
    }
    return `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
  }

  openPopup(order: LaboratoryClinicOrder) {
    this.selectedOrder = order;
    this.showPopup = true;
  }

  closePopup() {
    this.showPopup = false;
    this.selectedOrder = null;
  }
  navigateSendInvoice(): void {
    this.router.navigate(['/laboratory']);
  }
}
