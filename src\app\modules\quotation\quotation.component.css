/* 
.container {
    font-family: Arial, sans-serif;
    width: 65%;
    margin: 0 auto;
    height: calc(100vh - 120px);
    margin-left: 15%;
    background-color: #f9f9f9;
    overflow-y: auto;
    
} */

.container {
    font-family: Arial, sans-serif;
    width: 1000px;
    /* padding: 16px; */
    /* margin: 0 auto; */
    height: calc(100vh - 100px);
    margin-left: 15%;
    background-color: #f9f9f9;
    overflow-y: auto; 
}

.container h2 {
    font-size: 32px;
    font-weight: 600;
    margin-top: 10px;
}

.quotation-header {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    margin-bottom: 10px;
}

.btn {
    font-size: 16px;
    margin-left: 10px;
    padding: 5px 15px;
    border: 1px solid #FB751E;
    background-color: white;
    color: #FB751E;
    cursor: pointer;
    border-radius: 30px;
}

#emailButton {
    width: 153px;
    height: 35px;
}

#printButton {
    width: 153px;
    height: 35px;
}

.btn-outline-warning:hover {
    background-color: #FB751E;
    color: white;
}

.sales-quote {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.company-info {
    text-align: center;
    background: linear-gradient(90deg, #FFC107 0%, #25233A 100%);
    padding: 20px;
    border-radius: 5px;
    color: #ebdfdc;
}

.quote-details {
    margin-top: 20px;
}

.order-info {
    margin-bottom: 20px;
}

.quote-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.quote-table th, .quote-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.quote-table th {
    color: rgb(10, 9, 9);
}

.containers {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    background-color: #fff;
    padding: 20px;
    box-sizing: border-box;
}

.notes {
    background-color: #f8f7f7;
    padding: 20px;
    border-radius: 5px;
    flex: 1;
    margin-right: 20px;
}

.summary {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.row {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #e0e0e0;
}

.grand-total {
    font-weight: bold;
    font-size: 1.2em;
    border: none;
    padding-top: 20px;
}

.form-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.form-actions button {
    width: 153px;
    height: 35px;
    border-radius: 30px;
    border: none;
    font-family: Inter;
    margin-left: 85%;
    background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);
    color: #ebdfdc;
    cursor: pointer;
    margin-bottom: 20px;
}

.form-actions button:hover {
    background: linear-gradient(266.16deg, #FB751E 0.91%, #B93426 60.88%);
}


@media (max-width: 768px) {
    .quotation-header {
        justify-content: center;
    }

    .btn {
        margin: 10px 5px;
        padding: 10px 20px;
    }

    .containers {
        flex-direction: column;
    }

    .notes {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .quote-table th, .quote-table td {
        padding: 12px;
        font-size: 14px;
    }

    .form-actions button {
        margin-left: 0;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .btn {
        padding: 8px 15px;
        font-size: 14px;
    }

    .company-info {
        padding: 15px;
    }

    .sales-quote {
        padding: 15px;
    }

    .quote-details {
        margin-top: 15px;
    }

    .order-info p {
        margin-bottom: 10px;
    }

    .quote-table {
        display: block;
    }

    .quote-table thead, .quote-table tbody, .quote-table th, .quote-table td, .quote-table tr {
        display: block;
    }

    .quote-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .quote-table tr {
        margin-bottom: 10px;
    }

    .quote-table td {
        border: none;
        position: relative;
        padding-left: 50%;
        text-align: right;
    }

    .quote-table td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 50%;
        padding-left: 10px;
        font-weight: bold;
        text-align: left;
    }

    .notes {
        padding: 15px;
    }

    .summary {
        padding: 15px;
    }

    .row {
        padding: 8px 0;
    }

    .grand-total {
        font-size: 1em;
    }

    .form-actions {
        margin-top: 15px;
    }

    .form-actions button {
        padding: 8px 30px;
        font-size: 16px;
    }
}
.sidebar-container {
    height: calc(10vh - 50px);
    width: 10%;
    overflow-y: auto;
    
    display: block;
}



@media (max-width: 768px) {
    .sidebar-container,
    .main-content {
        width: 100%;
        display: block;
    }
}