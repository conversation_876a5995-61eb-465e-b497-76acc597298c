import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DefaultNavbarComponent } from './default-navbar/default-navbar.component';
import { PrimaryActionButtonComponent } from './primary-action-button/primary-action-button.component';
import { RouterModule } from '@angular/router';



@NgModule({
  declarations: [
    DefaultNavbarComponent,
    PrimaryActionButtonComponent
  ],
  imports: [
    CommonModule,
    RouterModule, // Import RouterModule in the feature module
  ],
  exports:[DefaultNavbarComponent,PrimaryActionButtonComponent]
})
export class CoreModule { }
