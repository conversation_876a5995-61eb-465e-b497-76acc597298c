<div class="row">
  <div class="col-12">
    <!-- Header -->
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div class="col-6">
            <h3 class="fs-5 m-0 p-0" style="font-weight: 600">
              Supplier Setup
            </h3>
            <p class="text-black-50 m-0 p-0" style="font-size: 12px">
              Add All supplier Items
            </p>
          </div>
          <div class="col-6 position-relative"></div>
        </div>
        <div class="row my-4">
          <hr class="border-secondary" />
        </div>
      </div>
    </div>
    <!-- Header -->

    <div class="row">
      <div class="col-12">
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-12">
                <form [formGroup]="supplierInventoryForm" class="mt-1 pe-2 pe-xl-0" (ngSubmit)="saveSupplierInventory()">
                  <!-- saveSupplierInventory() -->
                  <div class="row">
                    <div class="col-12 col-xl-6 pe-0 pe-xl-5">
                      <div class="row gy-4 px-0">
                        <div class="col-12 d-grid">
                          <p
                            class="p-0 m-0 mb-2"
                            style="font-weight: 500; font-size: 14px"
                          >
                            Select Category
                          </p>
                          <select
                            class="custom-select p-2"
                            [(ngModel)]="supplierInventoryData.category" formControlName="category" required
                          >
                            <option value="">Select Category</option>
                            <option
                              *ngFor="let item of supplierItemCategoryList"
                              [value]="item.name"
                            >
                              {{ item.name }}
                            </option>
                          </select>
                          <div *ngIf="supplierInventoryForm.get('category')?.invalid && (supplierInventoryForm.get('category')?.dirty || supplierInventoryForm.get('category')?.touched)" class="text-danger">
                            <small>Category is required.</small>
                          </div>
                        </div>
                        <div class="col-12 d-grid">
                          <p
                            class="p-0 m-0 mb-2"
                            style="font-weight: 500; font-size: 14px"
                          >
                            Select Sub Category
                          </p>
                          <select
                            class="custom-select p-2"
                            formControlName="subCategory"
                            [(ngModel)]="supplierInventoryData.subCategory" required
                          >
                            <option value="">Select Sub Category</option>
                            <option value="Sub Category 01">Autoclaves</option>
                            <option value="Ultrasonic Scalers">
                              Ultrasonic Scalers
                            </option>
                            <option value="Pulp testers">Pulp testers</option>
                            <option value="Implant Kit">Implant Kit</option>
                            <option value="Mouth Wash">Mouth Wash</option>
                          </select>
                          <div *ngIf="supplierInventoryForm.get('subCategory')?.invalid && (supplierInventoryForm.get('subCategory')?.dirty || supplierInventoryForm.get('subCategory')?.touched)" class="text-danger">
                            <small>Sub Category is required.</small>
                          </div>
                        </div>
                        <div class="col-12 d-grid">
                          <p
                            class="p-0 m-0 mb-2"
                            style="font-weight: 500; font-size: 14px"
                          >
                            Product Description
                          </p>
                          <textarea
                            class="p-2 custom-select"
                            rows="3"
                            cols="1"
                            style="height: auto"
                            type="text"
                            formControlName="description"
                            [(ngModel)]="supplierInventoryData.description"
                            required
                            placeholder="Enter item description..."
                          ></textarea>
                          <div *ngIf="supplierInventoryForm.get('description')?.touched && supplierInventoryForm.get('description')?.invalid" class="text-danger">
                            <small>Description is required.</small>
                          </div>
                        </div>
                        <div class="col-12">
                          <div class="row gx-4">
                            <div class="col-5 ">
                              <p
                                class="p-0 m-0 mb-2"
                                style="font-weight: 500; font-size: 14px"
                              >
                                Product Quantity
                              </p>
                              <input
                                class="p-2 custom-select w-100"
                                formControlName="quantity"
                                type="number"
                                required
                                [(ngModel)]="supplierInventoryData.quantity"
                                placeholder="Enter quantity"
                              />
                              <div *ngIf="supplierInventoryForm.get('quantity')?.touched && supplierInventoryForm.get('quantity')?.invalid" class="text-danger">
                                <small>Quantity is required it should be more than <strong>0</strong>.</small>
                              </div>
                            </div>
                            <div class="col-7">
                              <p
                                class="p-0 m-0 mb-2"
                                style="font-weight: 500; font-size: 14px"
                              >
                                Unit Price (Optional)
                              </p>
                              <input

                                class="p-2 custom-select w-100"
                                formControlName="price"
                                required
                                type="number"
                                min="0"
                                [(ngModel)]="supplierInventoryData.price"
                                placeholder="Enter price in LKR"
                              />
                              <div *ngIf="supplierInventoryForm.get('price')?.touched && supplierInventoryForm.get('price')?.invalid" class="text-danger">
                                <small>Price is required and it should be more than <strong>0</strong>.</small>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12 col-xl-6 ps-4 mt-5 mt-xl-0 ps-xl-5">
                      <div class="row mb-5">
                        <div class="col-12 gx-0">
                          <div class="float-start g-0">
                            <p
                              class="p-0 m-0"
                              style="font-weight: 500; font-size: 14px"
                            >
                              Product Images
                            </p>
                            <p
                              class="text-black-50 p-0 m-0"
                              style="font-size: 12px"
                            >
                              Upload only <strong>.jpg</strong> /
                              <strong>.png</strong> or <strong>.jpeg</strong>
                            </p>
                          </div>
                          <app-primary-action-button
                            class="float-end"
                            buttonUI="secondary"
                            buttonText="Add Images"
                            buttonType="button"
                            (click)="triggerFileInput()"
                          ></app-primary-action-button>

                          <!-- Hidden file input -->
                          <input
                            type="file"
                            #fileInput
                            accept="image/*"
                            multiple
                            (change)="onFileSelected($event)"
                            hidden
                          />

                        </div>
                      </div>
                      <div class="row text-center product-image-container">
                        <div class="col-12 my-auto" *ngIf="!uploadedImages.length">
                          <img class="mb-4" src="/assets/images/no-images.svg" style="height: 100px; width: 200px;" alt="">
                          <p class="p-0 m-0">No Images Submitted Yet</p>
                          <p class="p-0 m-0 text-black-50" style="font-size: 12px">
                            Add images to present the product's professional appearance
                          </p>
                        </div>
                        <div class="col-12 my-auto" *ngIf="uploadedImages.length">
                          <div class="row">
                            <div class="col-3" *ngFor="let image of uploadedImages; let i = index">
                              <div class="card mb-3">
                                <img [src]="image" class="card-img-top" alt="Uploaded Image">
                                <div class="card-body text-center">
                                  <button type="button" class="btn btn-sm btn-danger" (click)="removeImage(i)">
                                    Remove
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="row mt-5">
                        <div class="col g-0 pe-3">
                          <app-primary-action-button class="d-grid" buttonText="Schedule" buttonType="reset" buttonUI="secondary"></app-primary-action-button>
                        </div>
                        <div class="col g-0 ps-3">
                          <app-primary-action-button class="d-grid" buttonText="Add Product" buttonType="submit" buttonUI="primary"></app-primary-action-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
