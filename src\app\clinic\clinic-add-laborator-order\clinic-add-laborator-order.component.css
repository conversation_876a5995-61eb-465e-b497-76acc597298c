.container {
    padding: 20px;
}

.header-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#printButton {
    border: 1px solid #FB751E;
    width: 248px;
    height: 35px;
    border-radius: 50px;
    opacity: 0;
}

.header-row-hm {
    color: black;
    font-size: 32px;
    font-family: Inter;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.header-bottom-line {
    border-bottom: 1px solid #ccc;
    margin-top: 15px;
}

#header1 {
    height: 64px;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(90deg, #FB751E 49.4%, #DBB800 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: Inter, sans-serif;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 0;
}

.order-form-container {
    background-color: #FFFCFC;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 1000px;
    width: 100%; 
}

.top-section {
    display: flex;
    flex-wrap: wrap; 
    justify-content: space-between; 
    gap: 50px; 
    margin-bottom: 20px; 
}

.service-details,
.patient-details {
    flex: 1; 
    min-width: 200px;
}

.patient-details {
    display: flex;
    flex-wrap: wrap; 
}

.patient-details div {
    flex: 1 1 45%; 
    min-width: 250px; 
    margin-bottom: 15px; 
}

.patient-details input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    width: 100%; 
    transition: border-color 0.3s ease; 
}

.patient-details input:focus {
    border-color: #FB751E; 
    outline: none; 
}

.bottom-section {
    margin-top: 40px;
}

.form-row {
    display: flex;
    gap: 40px;
    margin-bottom: 10px;
    flex-wrap: wrap; 
}

.form-row select,
.form-row input,
.form-row textarea {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    width: 100%; 
}

.form-row textarea {
    padding: 20px;
}

.buttons {
    padding: 7px 20px;
    background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);
    color: #fff;
    border: none;
    border-radius: 18px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    margin-top: 10px;
    margin-left: auto; 
}

.buttons:hover {
    background-color: #e64a19;
    transform: translateY(-2px); 
}

.form-row > div {
    flex: 1;
    min-width: 200px;
}

label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
    font-weight: bold;
}

.size {
    width: 100%; 
}

@media (max-width: 768px) {
    .header-row-hm {
        font-size: 24px; 
    }

    .order-form-container {
        padding: 10px; 
    }

    .top-section {
        flex-direction: column; 
    }

    .form-row {
        flex-direction: column; 
    }

    .form-row > div {
        min-width: 100%; 
    }

    .buttons {
        width: 100%; 
        padding: 10px; 
    }
}
