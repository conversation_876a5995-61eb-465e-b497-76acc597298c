
.base-background{
  background: url('/assets/images/background.png') center center no-repeat;
  background-size: cover;
}
.bordered-container{
  border: 1px solid #fb751e;
  border-radius: 10px;

}

.form-container {
  background-color: white;
  padding-inline: 2px;
  border-radius: 25px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  /* max-width: 600px; */
  /* width: 1000lvh; */
  /* z-index: 1; */
  border: 1px solid #fb751e;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: auto;
  padding-inline: 10px;
  padding-top: 10px;
  padding-block: 20px;
}

h3 {
  font-family: "Inter";
  font-size: 30px;
  font-weight: 700;
  text-align: center;
  color: #fb751e;
  margin-bottom: 8.5px;
}

.form-row {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}

.form-row .form-group {
  flex: 1;
  margin-right: 10px;
}

.form-row .form-group:last-child {
  margin-right: 0;
}

.form-row label {
  font-family: "Inter";
  font-size: 16px;
  font-weight: 400;
  text-align: left;
  margin-bottom: 5px;
  color: #000000;
  display: block;
}

.form-group input {
  font-family: "Inter";
  font-weight: 400;
  font-size: 14px;
  color: #495057;
}

.form-group select {
  width: 100%;
  height: 33px;
  padding: 5px;
  font-size: 14px;
  color: #495057;
  border-radius: 4px;
  border: 1px solid #b3b3b3;
  background-image: linear-gradient(45deg, transparent 50%, #ff7a00 50%),
    linear-gradient(135deg, #ff7a00 50%, transparent 50%);
  background-position: calc(100% - 20px) center,
    calc(100% - 15px) center;
  background-size: 5px 5px, 5px 5px;
  background-repeat: no-repeat;
  appearance: none;
}

.header{
  color: transparent;
  background: linear-gradient(to right, #fb751e, #b93426);
  background-clip: text;
  box-shadow: none;
}

.form-group select:focus {
  outline: none;
  border-color: #ff7a00;
}

input[type="text"],
input[type="email"],
input[type="password"] {
  width: 100%;
  padding: 5px 10px !important;
  border: 1px solid rgb(220,220,220) !important;
  height: 40px !important;
  border-radius: 4px;
  background: transparent !important;
}

select{
  width: 100%;
  padding: 5px 10px !important;
  border: 1px solid rgb(220,220,220) !important;
  height: 40px !important;
  border-radius: 4px;
  background-color: white;
}

select:disabled{
  background: rgba(221, 221, 221, 0.226) !important;
  cursor: not-allowed;
}

.input-label{
  font-size: 14px !important;
  font-weight: 500 !important;
  color: rgb(100,100,100) !important;
  margin-bottom: 5px !important;
}

.base-form div.row{
  margin-bottom: 16px;
}


input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,select:not(:disabled):focus {
  outline: none;
  box-shadow: none;
  border-color: #ff7a00 !important;
}

textarea {
  font-family: "Inter";
  font-weight: 400;
  font-size: 14px;
  color: #495057;
  width: 100%;
  height: 40px;
  padding: 5px 10px;
  border: 1px solid rgb(220,220,220);;
  border-radius: 4px;
  /* resize: none; */
}

textarea:focus {
  outline: none;
  border-color: #ff7a00;
}

.register-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(to right, #fb751e, #333333);
  border: none;
  border-radius: 20px;
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
  margin-top: 30px;
}

.register-button:hover {
  background: linear-gradient(to left, #fb751e, #333333);
}

.backtoselection-button {
  width: 150px;
  height: 40px;
  background: none;
  border: 2px solid #fb751e;
  color: #fb751e;
  font-size: 16px;
  font-weight: bold;
  border-radius: 20px;
  cursor: pointer;
  /* position: absolute; */
  /* left: 0; */
  /* top: 107px; */
  /* margin-left: 5%; */
  justify-content: center;
  margin-bottom: 20px;
  margin-top: 20px;
}

@media (min-width: 768px) {
  .background-container {
    padding: 30px;
  }

  .form-container {
    background-color: white;
    padding-inline: 2px;
    border-radius: 25px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    /* max-width: 600px; */
    /* width: 1000lvh; */
    /* z-index: 1; */
    border: 1px solid #fb751e;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: auto;
    padding-inline: 20px;
    padding-top: 10px;
    padding-block: 20px;
  }
}

@media (min-width: 1024px) {
  .form-container {
    background-color: white;
    padding: 30px;
    border-radius: 25px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    /* max-width: 600px; */
    /* width: 1000lvh; */
    /* z-index: 1; */
    border: 1px solid #fb751e;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 620px;
    height: auto;
  }

  .background-container {
    padding: 30px;
  }

  .backtoselection-button {
    width: 150px;
    height: 40px;
    background: none;
    border: 2px solid #fb751e;
    color: #fb751e;
    font-size: 16px;
    font-weight: bold;
    border-radius: 20px;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 107px;
    margin-left: 5%;
  }
}
