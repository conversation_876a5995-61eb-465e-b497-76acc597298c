import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SupplierDashboardComponent } from './supplier-dashboard/supplier-dashboard.component';
import { SupplierLayoutComponent } from './supplier-layout/supplier-layout.component';
import { InventoryItemsComponent } from './supplier-inventory-items/supplier-inventory-items.component';
import { SupplierClinicOrdersComponent } from './supplier-clinic-orders/supplier-clinic-orders.component';
import { SupplierClinicOrderSingleViewComponent } from './supplier-clinic-order-single-view/supplier-clinic-order-single-view.component';
import { authGuard } from '../auth/auth.guard';

const routes: Routes = [
  {
    path:'',
    component:SupplierLayoutComponent,
    canActivateChild:[authGuard],
    children:[
      {path:'',component:SupplierDashboardComponent},
      {path:'dashboard',component:SupplierDashboardComponent},
      {path:'setup',component:InventoryItemsComponent},
      {path:'clinic-orders',component:SupplierClinicOrdersComponent},
      {path:'clinic-orders/single-clinic-order',component:SupplierClinicOrderSingleViewComponent},
      {path:'quotation',component:SupplierDashboardComponent},
      {path:'invoice',component:SupplierDashboardComponent},
      {path:'payment',component:SupplierDashboardComponent},
      {path:'**',redirectTo:''}
    ]
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SupplierRoutingModule { }
