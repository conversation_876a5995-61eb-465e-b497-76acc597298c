import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SalesquoteComponent } from './modules/salesquote/salesquote.component';

import { AppointmentPageComponent } from './modules/appointments/appointment-page/appointment-page.component';
import { AppointmentsComponent } from './modules/appointments/appointments/appointments.component';
import { AppointmentComponent } from './modules/appointments/create-appoinment/appointment.component';
import { ClinicRegistrationComponent } from './clinic/clinic-registration/clinic-registration.component';
import { UserAppointmentComponent } from './user/user-appointment/user-appointment.component';
import { UserNotificationComponent } from './user/user-notification/user-notification.component';
import { FutureDentistRegistrationComponent } from './modules/future-dentist-registration/future-dentist-registration.component';
import { InventoryItemsComponent } from './supplier/supplier-inventory-items/supplier-inventory-items.component';
import { LaboratoryRegistrationComponent } from './laboratory/laboratory-registration/laboratory-registration.component';
import { OrderRequestsComponent } from './modules/order-requests/order-requests.component';
import { OrdersComponent } from './modules/orders/orders.component';
import { QuotationComponent } from './modules/quotation/quotation.component';
import { SalesQuotesComponent } from './modules/sales-quotes/sales-quotes.component';
import { SalesquotesComponent } from './modules/salesquotes/salesquotes.component';
import { UserAppointmentDashboardComponent } from './user/user-appointment-dashboard/user-appointment-dashboard.component';
import { UserSalesQuotesComponent } from './user/user-sales-quotes/user-sales-quotes.component';

import { UserLoginComponent } from './user/user-login/user-login.component';
import { CreateOrderComponent } from './modules/create-order/create-order.component';
import { MyAppointmentsComponent } from './modules/my-appointments/my-appointments.component';
import { NotificationsComponent } from './modules/notifications/notifications.component';
import { PurchaseComponent } from './modules/purchase/purchase.component';
import { PurchasingDashboardComponent } from './modules/purchasing-dashboard/purchasing-dashboard.component';
import { ServicesComponent } from './modules/services/services.component';
import { UserSelectionComponent } from './user/user-selection/user-selection.component';
import { HomePageComponent } from './modules/home-page/home-page.component';
import { ClinicSideBarComponent } from './clinic/components/clinic-side-bar/clinic-side-bar.component';
import { ClinicDashboardComponent } from './clinic/clinic-dashboard/clinic-dashboard.component';
import { ClinicOrdersComponent } from './clinic/clinic-orders/clinic-orders.component';
import { SupplierRegistrationComponent } from './supplier/supplier-registration/supplier-registration.component';
import { DoctorRegistrationComponent } from './doctor/doctor-registration/doctor-registration.component';
import { ContactUsComponent } from './modules/contact-us/contact-us.component';
import { ViewsalesComponent } from './modules/viewsales/viewsales.component';
import { authGuard } from './auth/auth.guard';

const routes: Routes = [
  // { path: '', component: ClinicDashboardComponent },

  { path: 'appointment', component: AppointmentComponent },
  { path: 'user-appointment', component: UserAppointmentComponent },
  { path: 'appointments', component: AppointmentsComponent },
  { path: 'appointment-page', component: AppointmentPageComponent },
  { path: 'user-appointment-dashboard',component: UserAppointmentDashboardComponent,},
  { path: 'my-appointments', component: MyAppointmentsComponent },
  { path: 'notifications', component: NotificationsComponent },
  { path: 'user-notification', component: UserNotificationComponent },
  { path: 'purchase', component: PurchaseComponent },
  { path: 'purchasing-dashboard', component: PurchasingDashboardComponent },
  { path: 'salesquote', component: SalesquoteComponent },
  { path: 'sales-quotes', component: SalesQuotesComponent },
  { path: 'salesquotes', component: SalesquotesComponent },
  { path: 'viewsales', component: ViewsalesComponent },
  { path: 'user-sales-quotes', component: UserSalesQuotesComponent },
  { path: 'quotation', component: QuotationComponent },
  { path: 'inventory-items', component: InventoryItemsComponent },
  { path: 'orders', component: OrdersComponent },
  { path: 'order-requestss', component: OrderRequestsComponent },
  { path: 'create-order', component: CreateOrderComponent },
  { path: 'services', component: ServicesComponent },
  { path: 'clinic-side-bar', component: ClinicSideBarComponent },
  { path: 'clinic-dashboard', component: ClinicDashboardComponent },
  { path: 'clinic-order', component: ClinicOrdersComponent },

  // Regsitartion, Logins & Selections
  { path: 'doctor-registration', component: DoctorRegistrationComponent },
  { path: 'clinic-registration', component: ClinicRegistrationComponent },
  { path: 'supplier-registration', component: SupplierRegistrationComponent },
  { path: 'future-dentist-registration', component: FutureDentistRegistrationComponent },
  { path: 'laboratory-registration', component: LaboratoryRegistrationComponent },
  { path: 'user-login', component: UserLoginComponent },
  { path: 'user-selection', component: UserSelectionComponent },

  // Base Components
  { path: '', redirectTo: '/home-page', pathMatch: 'full' },
  { path: 'contact-us', component: ContactUsComponent },
  { path: 'home-page', component: HomePageComponent },


  {
    path: 'user',
    loadChildren: () =>
      import('./user/user.module').then((m) => m.UserModule),
  },

  {
    path: 'doctor',
    loadChildren: () => import('./doctor/doctor.module').then((m) => m.DoctorModule),
    canActivate:[authGuard]
  },

  {
    path: 'clinic',
    loadChildren: () =>
      import('./clinic/clinic.module').then((m) => m.ClinicModule),
    canActivate:[authGuard]

  },
  {
    path: 'laboratory',
    loadChildren: () =>
      import('./laboratory/laboratory.module').then((m) => m.LaboratoryModule),
    canActivate:[authGuard]

  },
  {
    path: 'supplier',
    loadChildren: () =>
      import('./supplier/supplier.module').then((m) => m.SupplierModule),
    canActivate:[authGuard]

  },
  {
    path: 'future-dentist',
    loadChildren: () =>
      import('./future-dentist/future-dentist.module').then((m) => m.FutureDentistModule),
    canActivate:[authGuard]

  },

  {
    path: 'customer',
    loadChildren: () =>
      import('./customer/customer.module').then((m) => m.CustomerModule),

  },
  {
    path: 'admin',
    loadChildren: () =>
      import('./admin/admin.module').then((am) => am.AdminModule),
    canActivate:[authGuard],

  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'top',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
