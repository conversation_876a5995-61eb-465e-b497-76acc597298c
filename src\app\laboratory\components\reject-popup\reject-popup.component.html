<div class="popup-overlay">
    <div class="popup-box">
        <!-- Existing content -->
        <div style="width: 100%; display: flex; justify-content: flex-end;">
            <button style="background: none; border: none; font-size: 20px; cursor: pointer;" (click)="onClose()">
                &#10005;
            </button>
        </div>
        <div style="padding: 20px; display: flex; flex-direction: column; align-content: center; width: 100%;">
            <div style="display: flex; flex-direction: row; width: 100%; justify-content: center; margin-bottom: 40px;">
                <img src="../../../assets/images/close.png" alt="" width="80px" height="80px" />
            </div>
            <div style="display: flex; flex-direction: row; width: 100%; justify-content: center; padding-top: 20px;">
                <div style="font-size: 25px; font-weight: 500; width: 80%; text-align: center;">
                    Do you want to send reasons for rejections to the clinic?
                </div>
            </div>
            <div
                style="display: flex; justify-content: center; gap: 60px; margin-top: 20px; flex-direction: row; margin-top: 80px;">
                <button (click)="onClose()"
                    style="width: 120px; padding-block: 5px; background: gray; color: black; border-radius: 30px;">
                    No
                </button>
                <button (click)="onConfirmReject()"
                    style="width: 120px; background: linear-gradient(to right, #FB751E , #B93426); color: white; border: none; border-radius: 30px;">
                    Yes
                </button>
            </div>
        </div>

        <!-- Conditionally render the new rejection reason popup -->
        <app-reject-reason-popup *ngIf="showRejectReasonPopup" (closePopup)="onReasonPopupClose()"
            (reasonSubmitted)="onReasonSubmitted($event)"></app-reject-reason-popup>
    </div>
</div>