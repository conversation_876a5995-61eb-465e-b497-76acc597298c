<div class="row">
  <div class="col-12">
    <!-- Header -->
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div class="col-6">
            <h3 class="fs-5 m-0 p-0" style="font-weight: 600">
              Supplier Orders
            </h3>
            <p class="text-black-50 m-0 p-0" style="font-size: 12px">
              View all Supplier Orders
            </p>
          </div>
          <div class="col-6 position-relative">
            <app-primary-action-button
              class="float-end"
              (buttonClicked)="goToCreateSupplierOrderView()"
              buttonText="New Order"
              buttonUI="secondary"
              buttonType="button"
            ></app-primary-action-button>
          </div>
        </div>
        <div class="row my-4">
          <hr class="border-secondary" />
        </div>
      </div>
    </div>
    <!-- Header -->

    <!-- Body -->
    <div class="row">
      <div class="col-12">
        <div
          class="row gy-3"
          *ngIf="filteredPaginatedData != null && filteredPaginatedData.length > 0"
        >
          <!-- table-header -->
          <div class="col-12 p-3 py-2 card-table-header" style="border-radius: 5px;">
            <div class="row card-table-header my-1">
              <div class="col-7 d-flex my-auto">
                <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Supplier Name & Address - Status</h6>
              </div>
              <div class="col-3 my-auto text-center" style="border-inline: 1px solid  white;">
                <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Date & Time</h6>
              </div>
              <div class="col-2 text-center my-auto">
                <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Actions</h6>
              </div>
            </div>
          </div>
          <!-- table-header -->

          <!-- table-body -->
          <div
            class="col-12 p-3"
            *ngFor="let orderHeader of filteredPaginatedData"
            style="border: 1px solid rgb(230,230,230); background-color: rgb(254, 254, 254); border-radius: 5px;"
          >
            <div class="row">
              <div class="col-7 d-grid d-lg-flex my-auto">
                <h6 class="my-auto pe-0 pe-xl-3" style="font-weight: 500; font-size: 14px;">
                  {{ orderHeader.supplier.name + " - " + orderHeader.supplier.address }}
                </h6>
                <div class="mt-2 mt-lg-0" [ngSwitch]="orderHeader.orderStatus.toString()">
                  <span
                    *ngSwitchCase="'CLINIC_CREATED'"
                    class="alert py-1 my-auto alert-info"
                    style="border-radius: 5px; font-size: 11px; font-weight: 500;"
                    >Created</span
                  >
                  <span
                    *ngSwitchCase="'SUPPLIER_APPROVED'"
                    class="alert py-1 my-auto alert-warning"
                    style="border-radius: 5px; font-size: 11px; font-weight: 500;"
                    >Supplier Approved</span
                  >
                  <span
                    *ngSwitchCase="'SUPPLIER_REJECTED'"
                    class="alert py-1 my-auto alert-danger"
                    style="border-radius: 5px; font-size: 11px; font-weight: 500;"
                    >Rejected</span
                  >
                  <span
                    *ngSwitchCase="'CLINIC_REJECTED'"
                    class="alert py-1 my-auto alert-danger"
                    style="border-radius: 5px; font-size: 11px; font-weight: 500;"
                    >Cancelled</span
                  >
                  <span
                    *ngSwitchCase="'SUPPLIER_COMPLETED'"
                    class="alert py-1 my-auto alert-success"
                    style="border-radius: 5px; font-size: 11px; font-weight: 500;"
                    >Completed</span
                  >
                  <span
                    *ngSwitchCase="'SUPPLIER_PROCESSING'"
                    class="alert py-1 my-auto alert-warning"
                    style="border-radius: 5px; font-size: 11px; font-weight: 500;"
                    >Pending for Quote</span
                  >
                  <span
                    *ngSwitchDefault
                    class="alert py-1 my-auto alert-secondary"
                    style="border-radius: 5px; font-size: 11px; font-weight: 500;"
                    >{{ orderHeader.orderStatus.toString() }}</span
                  >
                </div>
              </div>
              <div class="col-3 my-auto text-center" style="border-inline: 1px solid  rgb(230,230,230);">
                <p
                  class="text-balck-50"
                  style="font-size: 13px; font-weight: 500;"
                  [innerHTML]="orderHeader.createdDateTime"
                ></p>
              </div>
              <div class="col-2 text-center my-auto">
                <label class="view-odrer-button py-1 my-auto px-2 me-2" title="View Request">
                  <i class="bi bi-arrow-up-right-square"></i>
                </label>
                <label class="view-odrer-button py-1 my-auto px-2 me-2" title="Quotation">
                  <i class="bi bi-arrow-down-left-square"></i>
                </label>
                <label class="view-odrer-button py-1 my-auto px-2" title="Payment">
                  <i class="bi bi-currency-dollar"></i>
                </label>
              </div>
            </div>
          </div>
          <!-- table-body -->

          <!-- Pagination -->
          <div class="col-12">
            <div *ngIf="filteredPaginatedData.length > itemsPerPage" class="row mt-4 position-relative">
              <div class="col-12 d-flex justify-content-end g-0">
                <button
                  (click)="goToPage(currentPage - 1)"
                  [hidden]="currentPage === 1"
                  class="alert bg-light me-2 border-secondary-subtle"
                  style="font-size: 13px; padding: 10px 15px;"
                >
                  <i class="bi-chevron-left"></i>
                </button>
                <div
                  *ngFor="let page of visiblePages"
                  (click)="goToPage(page)"
                  [class.active]="currentPage === page"
                  class="alert pagination-button fw-bold me-2"
                  style="font-size: 13px; padding: 10px 15px;"
                >
                  {{ page }}
                </div>
                <button
                  (click)="goToPage(currentPage + 1)"
                  [hidden]="currentPage === totalPages"
                  class="alert bg-light border-secondary-subtle"
                  style="font-size: 13px; padding: 10px 15px;"
                >
                  <i class="bi-chevron-right"></i>
                </button>
              </div>
            </div>
          </div>
          <!-- Pagination -->
        </div>
      </div>
    </div>
    <!-- Body -->
  </div>
</div>
