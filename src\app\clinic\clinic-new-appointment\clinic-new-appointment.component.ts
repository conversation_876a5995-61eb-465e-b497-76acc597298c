import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Appointments } from 'src/app/modules/appointments/appointments';
import { AppointmentsService } from 'src/app/modules/appointments/appointments.service';
import { Clinic } from '../clinic';
import { Customer } from 'src/app/modules/appointments/customer';

@Component({
  selector: 'app-clinic-new-appointment',
  templateUrl: './clinic-new-appointment.component.html',
  styleUrls: ['./clinic-new-appointment.component.css']
})
export class ClinicNewAppointmentComponent {
  appointmentForm: FormGroup;
  defaultDate: string = new Date().toISOString().substr(0, 10);
  currentDate: string = this.getCurrentDate();
  appointment: Appointments = new Appointments();

  constructor(
    private fb: FormBuilder,
    private appointmentsService: AppointmentsService,
  ) {
    const today = new Date(); // Get today's date
    this.defaultDate = today.toISOString().substring(0, 10); // Format date as YYYY-MM-DD
  
    // Initialize form
    this.appointmentForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      telephone: ['', [Validators.required, Validators.pattern(/^\+?[0-9]{7,15}$/)]],
      preferredservice: [''],
      fromDate: [{ value: this.defaultDate, disabled: false }, Validators.required],
      fromTime: ['', Validators.required],
      toTime: ['', Validators.required]
    }, { });
  }

  ngOnInit(): void {
    // const clinicId = localStorage.getItem('clinicId');
    // console.log('Clinic ID:', clinicId);
  }

  private getCurrentDate(): string {
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!
    const yyyy = today.getFullYear();
    return `${yyyy}-${mm}-${dd}`;
  }

  cancelAppointment(): void {
    this.appointmentForm.reset();
  }

  onSubmit(): void {
    if (this.appointmentForm.valid) {
      const formValues = this.appointmentForm.value;

      // Get the logged-in clinic ID from localStorage
      const clinicId = localStorage.getItem('clinicId');

      if (!clinicId) {
        console.error('Clinic ID not found in localStorage.');
        return;
      }

      this.appointment = {
        ...this.appointment, // Spread the existing appointment properties
        firstName: formValues.firstName,
        lastName: formValues.lastName,
        email: formValues.email,
        telephone: formValues.telephone,
        preferredservice: formValues.preferredservice,
        fromDate: formValues.fromDate,
        fromTime: formValues.fromTime,
        toTime: formValues.toTime,
        clinics: new Clinic(),
        
        // address: formValues.address,
        // city: formValues.city,
        // state: formValues.state,
        // district: formValues.district,
        // nearestCity: formValues.nearestCity,
        // userName: formValues.username,
        // password: formValues.password,
        // toDate: formValues.toDate,
        // clinics: new Clinic(),
        // customer: new Customer() 
      };
      this.appointment.clinics.clinicId = Number(clinicId); // Set the clinic ID

      this.appointmentsService.saveAppointments(this.appointment).subscribe(
        (response) => {
          console.log('Appointment saved successfully:', response);
          alert('Appointment saved successfully!');
        },
        (error) => {
          console.error('Error saving appointment:', error);

          // const message = error?.error?.message;

          // if (error.status === 409) {
          //   alert('Selected time slot is already taken. Please choose another.');
          // } else if (error.status === 401) {
          //   alert('You are not authorized to make this request. Please log in again.');
          // } else {
          //   console.error('Unexpected error:', error);
          // }
        }
      );
    } else {
      console.warn('Form is invalid.');
    }
  }
}
