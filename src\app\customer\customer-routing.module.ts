import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CustomerDashboardComponent } from './customer-dashboard/customer-dashboard.component';
import { CustomerNewAppoinmentComponent } from './customer-new-appointment/customer-new-appointment.component';
import { CustomerLayoutComponent } from './customer-layout/customer-layout.component';

const routes: Routes = [
  {
    path:'',
    component: CustomerLayoutComponent,
    children:[
      {path:'dashboard', component: CustomerDashboardComponent },
      {path:'add-appointment', component: CustomerNewAppoinmentComponent }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomerRoutingModule {

}
