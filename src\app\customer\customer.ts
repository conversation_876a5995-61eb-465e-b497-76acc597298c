import { User } from "../user/user";
import { Clinic } from "../clinic/clinic";

export class Customer {
  customerId: number = 0;
  user: User = new User();
  firstName: string = "";
  lastName: string = "";
  address: string = "";
  city: string = "";
  state: string = "";
  country: string = "";
  telephone: string = "";
  email: string = "";
  registeredDate: string = "";
  latitude: string = "";
  longitude: string = "";
  appointments: Appointment[] = [];
}

export class Appointment {
  appointmentId: number = 0;
  firstName: string = "";
  lastName: string = "";
  address: string = "";
  city: string = "";
  state: string = "";
  district: string = "";
  telephone: string = "";
  email: string = "";
  preferredservice: string = "";
  nearestCity: string = "";
  fromDate: string = "";
  toDate: string = "";
  fromTime: string = "";
  toTime: string = "";
  userName: string = "";
  password: string = "";
  clinics: Clinic = new Clinic();
  customer: Customer = new Customer();
  status: String ="";
  }
