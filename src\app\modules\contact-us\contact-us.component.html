<app-default-navbar loggedUser="Test Contact" />
<div id="background">
    <div class="container">
      <div class="content">
        <!-- Left Side -->
        <div class="left-side">
          <div class="address details">
            <i class="fas fa-map-marker-alt"></i>
            <div class="topic">No. 45/6,</div>
            <div class="text-one">Nagahawaththa Rd</div>
            <div class="text-two">Maharagama</div>
          </div>
          <div class="phone details">
            <i class="fas fa-phone-alt"></i>
            <div class="topic">Phone</div>
            <div class="text-one">0772030521</div>
            <!-- <div class="text-two">+0096 3434 5678</div> -->
          </div>
          <div class="email details">
            <i class="fas fa-envelope"></i>
            <div class="topic">Email</div>
            <div class="text-one">info&#64;mydent.lk</div>
            <!-- <div class="text-two"><EMAIL></div> -->
          </div>
          <div class="Soical Media">
            <!-- Social Media Links as Buttons -->
            <a href="" target="_blank" class="social-btn">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="" target="_blank" class="social-btn">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="" target="_blank" class="social-btn">
              <i class="fab fa-instagram"></i>
            </a>
            <a href="" target="_blank" class="social-btn">
              <i class="fab fa-linkedin-in"></i>
            </a>
          </div>
        </div>

        <!-- Right Side -->
        <div class="right-side">
          <div class="topic-text">Instant Help</div>
          <p>Access the Instant Help Centre for step-by-step guides and solutions to both common and uncommon questions.</p>

          <form (ngSubmit)="onSubmit()">
            <div class="input-box">
              <input type="text" placeholder=" First name" [(ngModel)]="firstname" name="firstname" required />
            </div>
            <div class="input-box">
                <input type="text" placeholder=" Last name" [(ngModel)]="lastname" name="lastname" required />
              </div>
            <div class="input-box">
              <input type="email" placeholder=" your email" [(ngModel)]="email" name="email" required />
            </div>
            <div class="input-box">
                <input type="email" placeholder=" your Phone number" [(ngModel)]="phonenumber" name="phonenumber" />
              </div>
            <div class="input-box message-box">
              <label for="message">How Can I Help You?</label>
              <textarea placeholder="Tell us what you need help with" [(ngModel)]="message" name="message" required></textarea>
            </div>

            <div class="button">
              <input type="submit" value="Submit" />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
