<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12">
        <header>
          <h1 class="header-text px-1 pb-2" >My Assigned Clinics</h1>
          <hr class="my-4 header-break" />
        </header>
      </div>
      <div class="col-12">
        <div
          *ngIf="clinicList && clinicList.length > 0"
          class="row row-cols-1 row-cols-md-2 row-cols-lg-2 row-cols-xxl-3"
        >
          <div
            *ngFor="let clinic of clinicList"
            class="col doctor-clinic-card-root"
          >
            <div class="row doctor-clinic-card">
              <div class="col-12">
                <h2 class="doctor-clinic-card-head">{{ clinic.clinicName }}</h2>
                <p
                  class="text-black-50 mt-1 mb-4 doctor-clinic-card-body"
                  style="font-size: 14px"
                >
                  {{ clinic.clinicAddress + ", " + clinic.clinicCity }}
                </p>
                <button
                  class="doctor-clinic-card-action-button"
                  (click)="
                    viewAppoinmentList(clinic.clinicId, clinic.clinicName)
                  "
                >
                  Check Appoinments
                </button>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="clinicList == null || clinicList.length == 0" class="row">
          <div class="col-6 mx-auto no-clinics-assgined-root text-center" style="margin-top:100px ;">
            <h3 class="fw-bold">No clinics Assigned</h3>
            <p class="text-black-50 mt-2" style="font-size: 14px;">
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Vero
              minus velit necessitatibus assumenda quas est. Facilis sapiente
              expedita earum beatae?
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
