<div class="row">
    <div class="col-12">
        <div class="send-orders-container">
            <div class="header-row">
                <div class="header-row-h1">Orders</div>
                <div class="search-bar">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" placeholder="Search Order" [(ngModel)]="searchTerm" id="search-input" />
                </div>
            </div>

            <div class="header-bottom-line"></div><br />

            <div>
                <div (click)="navigateSendInvoice()" [ngStyle]="{display: 'flex', 'flex-direction': 'row', width: '100%', 'justify-content': 'flex-end', 'margin-bottom': '20px'}">
                    <button  [ngStyle]="{width: '250px', 'border-radius': '40px', background: 'linear-gradient(to right, #FB751E, #B93426)', color: 'white', border: 'none', padding: '10px 20px'}">
                        Send Invoice
                    </button>
                </div>
                
                <div>
                    <button (click)="toggleShowAll()" class="see-all-button">
                        {{ showAll ? 'Show Less <<' :  'See All >>' }}
                    </button>
                </div>
                
                <div *ngIf="showAll">
                    <table class="send-order-table">
                        <thead>
                            <tr>
                                <th width="10%">OrderID</th>
                                <th width="15%">Order Date</th>
                                <th width="15%">Expected Date</th>
                                <th width="15%">Clinic</th>
                                <th width="15%">Value</th>
                                <th width="30%">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let order of filteredOrders()">
                                <!-- <td><input type="checkbox" /> {{ order.laboratoryOrderId }}</td> -->
                                <td>{{ order.laboratoryOrderId }}</td>
                                <td>{{ formatDate(order.laboratoryOrderDate) }}</td>
                                <td>{{ order.expectedDate }}</td>
                                <td>{{ order.clinicId.name }}</td>
                                <td>{{ order.laboratorySetupId.price | currency: 'LKR ' }}</td>
                                <td>
                                    <div class="status-cell">
                                        <button [ngClass]="{
                                            'status pending': order.status === 'Pending',
                                            'status completed': order.status === 'Completed', 
                                            'status accepted': order.status === 'Accepted', 
                                            'status inprogress': order.status === 'InProgress',
                                            'status rejected': order.status === 'Rejected'
                                            }">
                                            {{ order.status }}
                                        </button>
                                        <button
                                            class="view-order"
                                            (click)="openPopup(order)">
                                            View Order
                                        </button> 
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="card-list">
                        <div class="order-card" *ngFor="let order of filteredOrders()">
                            <div class="card-header">
                                Order ID: {{ order.laboratoryOrderId }}
                            </div>
                            <div class="card-body">
                                <p><strong>Order Date:</strong> {{ formatDate(order.laboratoryOrderDate) }}</p>
                                <p><strong>Expected Date:</strong> {{ order.expectedDate }}</p>
                                <p><strong>Clinic:</strong> {{ order.clinicId.name }}</p>
                                <p><strong>Value:</strong> {{ order.laboratorySetupId.price | currency: 'LKR ' }}</p>
                            </div>
                            <div class="card-footer">
                                <button [ngClass]="{
                                    'status pending': order.status === 'Pending',
                                    'status completed': order.status === 'Completed',
                                    'status accepted': order.status === 'Accepted',
                                    'status inprogress': order.status === 'InProgress',
                                    'status rejected': order.status === 'Rejected',
                                    }">
                                    {{ order.status }}
                                </button>
                                <button
                                    class="view-order"
                                    (click)="openPopup(order)">
                                    View Order
                                </button> 
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="!showAll">
                    <table class="send-order-table">
                        <thead>
                            <tr>
                                <th width="10%">OrderID</th>
                                <th width="15%">Order Date</th>
                                <th width="15%">Expected Date</th>
                                <th width="15%">Clinic</th>
                                <th width="15%">Value</th>
                                <th width="30%">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let order of filteredOrders() | slice: (currentPage - 1) * itemsPerPage : currentPage * itemsPerPage">
                                <!-- <td><input type="checkbox" /> {{ order.laboratoryOrderId }}</td> -->
                                <td>{{ order.laboratoryOrderId }}</td>
                                <td>{{ formatDate(order.laboratoryOrderDate) }}</td>
                                <td>{{ order.expectedDate }}</td>
                                <td>{{ order.clinicId.name }}</td>
                                <td>{{ order.laboratorySetupId.price | currency: 'LKR ' }}</td>
                                <td>
                                    <div class="status-cell">
                                        <button [ngClass]="{
                                            'status pending': order.status === 'Pending',
                                            'status completed': order.status === 'Completed', 
                                            'status accepted': order.status === 'Accepted', 
                                            'status inprogress': order.status === 'InProgress',
                                            'status rejected': order.status === 'Rejected'
                                            }">
                                            {{ order.status }}
                                        </button>
                                        <button
                                            class="view-order"
                                            (click)="openPopup(order)">
                                            View Order
                                        </button> 
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="card-list">
                        <div class="order-card" *ngFor="let order of filteredOrders() | slice: (currentPage - 1) * itemsPerPage : currentPage * itemsPerPage">
                            <div class="card-header">
                                Order ID: {{ order.laboratoryOrderId }}
                            </div>
                            <div class="card-body">
                                <p><strong>Order Date:</strong> {{ formatDate(order.laboratoryOrderDate) }}</p>
                                <p><strong>Expected Date:</strong> {{ order.expectedDate }}</p>
                                <p><strong>Clinic:</strong> {{ order.clinicId.name }}</p>
                                <p><strong>Value:</strong> {{ order.laboratorySetupId.price | currency: 'LKR ' }}</p>
                            </div>
                            <div class="card-footer">
                                <button [ngClass]="{
                                    'status pending': order.status === 'Pending',
                                    'status completed': order.status === 'Completed',
                                    'status accepted': order.status === 'Accepted',
                                    'status inprogress': order.status === 'InProgress',
                                    'status rejected': order.status === 'Rejected',
                                    }">
                                    {{ order.status }}
                                </button>
                                <button
                                    class="view-order"
                                    (click)="openPopup(order)">
                                    View Order
                                </button> 
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Popup Component -->
                <app-order-details-popup *ngIf="showPopup" [order]="selectedOrder" (closePopup)="closePopup()"></app-order-details-popup>

                <div class="pagination-container" *ngIf="!showAll">
                    <ul class="pagination">
                        <li
                        class="page-item"
                        [class.disabled]="currentPage === 1"
                        (click)="goToPage(currentPage - 1)"
                        >
                        <a class="page-link" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                        </li>
                        <li
                        class="page-item"
                        *ngFor="let page of visiblePages"
                        [class.active]="page === currentPage"
                        (click)="goToPage(page)"
                        >
                        <a class="page-link">{{ page }}</a>
                        </li>
                        <li 
                        class="page-item" 
                        (click)="goToPage(currentPage + 1)"
                        >
                        <a class="page-link" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
