import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SupplierService } from '../supplier.service';
import { SupplierOrderHeader } from '../supplier';

@Component({
  selector: 'app-supplier-clinic-orders',
  templateUrl: './supplier-clinic-orders.component.html',
  styleUrls: ['./supplier-clinic-orders.component.css'],
})
export class SupplierClinicOrdersComponent implements OnInit {
  protected supplierOrderHeaderList: SupplierOrderHeader[] = [];
  protected filteredOrderHeaderList: SupplierOrderHeader[] = [];
  protected filteredPaginatedData: SupplierOrderHeader[] = [];
  protected searchTerm: string = '';
  protected currentPage: number = 1;
  protected itemsPerPage: number =3;
  protected totalPages: number = 1;

  constructor(
    private supplierServices: SupplierService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.onloadOrderList();
  }

  onloadOrderList() {
    const supplierId: number = Number(localStorage.getItem('userid'));
    if (supplierId) {
      this.supplierServices.getOrderRequestBySupplierId(supplierId).subscribe((response) => {
        if (response) {
          this.supplierOrderHeaderList = response.map((order: SupplierOrderHeader) => ({
            ...order,
            createdDateTime: this.formatDateTime(order.createdDateTime.toString())
          }));
          this.filteredOrderHeaderList = this.supplierOrderHeaderList;
          this.updatePagination();
        }
      });
    }
  }

  private formatDateTime(dateTime: string): string {
    const [year, month, day, hour, minute, second] = dateTime
      .split(',')
      .map(Number);
    const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(
      day
    ).padStart(2, '0')}`;
    const formattedTime = `${String(hour).padStart(2, '0')} : ${String(
      minute
    ).padStart(2, '0')} : ${String(second).padStart(2, '0')}`;
    return `${formattedDate}&nbsp;&nbsp;&nbsp; | &nbsp;&nbsp;&nbsp;${formattedTime}`;
  }

  filterOrders() {
    const searchTermLower = this.searchTerm.toLowerCase();

    this.filteredOrderHeaderList = this.supplierOrderHeaderList.filter(order =>
      order.clinic.name.toLowerCase().includes(searchTermLower) ||
      order.clinic.city.toLowerCase().includes(searchTermLower)
    );
    this.currentPage = 1;
    this.updatePagination();
  }

  public viewOrderDetails(supplierOrderHeaderId: number) {
    this.router.navigate(['supplier/clinic-orders/single-clinic-order'], {
      queryParams: { id: supplierOrderHeaderId },
    });
  }


  // Pagination methods
  updatePagination() {
    this.totalPages = Math.ceil(this.filteredOrderHeaderList.length / this.itemsPerPage);
    this.filteredPaginatedData = this.paginatedData();
  }

  paginatedData() {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredOrderHeaderList.slice(start, end);
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.filteredPaginatedData = this.paginatedData();
    }
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.filteredPaginatedData = this.paginatedData();
    }
  }
  public get pagesArray(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }
  goToPage(page: number) {
    this.currentPage = page;
    this.filteredPaginatedData = this.paginatedData();
  }
}
