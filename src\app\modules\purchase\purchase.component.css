body {
  font-family: "Inter", sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  border: none;
  padding: 0;
  box-shadow: none;
  background-color: transparent;
}

.title {
  font-size: 32px;
  margin: 0;
  font-weight: 600;
}

.cart-icon {
  position: relative;
  cursor: pointer;
  font-size: 25px;
}

.badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #ccc;
  color: #fff;
  border-radius: 50%;
  padding: 5px 8px;
  font-size: 12px;
}

.badge.has-items {
  background-color: #28a745;
}

.subheading {
  font-size: 24px;
  margin-bottom: 40px;
  font-weight: 600;
}

.subheading_2 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}

/* Filter section styles */
.filter-section {
  display: flex;
  gap: 20%;
  margin-bottom: 20px;
}

.custom-select {
  border-radius: 5px;
  border: 1px solid #D9D9D9;
  padding: 8px 12px;
}

.custom-input {
  border-radius: 20px;
  border: 1px solid #D9D9D9;
  padding: 8px 12px;
}

.search-container {
  width: 50%;
  position: relative;
  flex-grow: 1;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.custom-input {
  padding-left: 30px;
  width: 100%;
}

/* Section header styles */
.section-header {
  background: linear-gradient(to right, #f16406, #f16406, #DBB800);
  color: white;
  font-size: 20px;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 10px 10px 0 0;
  margin: 0;
}

/* Supplier section styles */
.supplier-section {
  border: 2px solid #ddd;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.supplier-table {
  max-height: 400px;
  overflow-y: auto;
}

.supplier-row {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}


.supplier-row:last-child {
  border-bottom: none;
}

.supplier-row.odd-row {
  background-color: #f1ddd1;
}

.visit-store-btn {
  color: #D85322;
  border-color: #D85322;
  border-radius: 20px;
  margin-top: 10px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: bold;
}

.visit-store-btn:hover {
  background-color: #D85322;
  color: white;
}

/* Items section styles */
.items-section {
  border: 1px solid #ddd;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.item-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 20px;
  padding: 20px;
  height: auto;
}

.item-card {
  border: 2px solid #bebebe;
  border-radius: 15px;
  padding: 15px;
  text-align: left;
  cursor: pointer;
  transition: box-shadow 0.3s ease;
}

.item-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.item-card img {
  max-width: 100%;
  height: auto;
  margin-bottom: 10px;
}

.item-name {
  margin-bottom: 5px;
}

.item-price {
  font-size: 20px;
  font-weight: bold;
  color: #666666;
  margin-bottom: 5px;
}

.item-stock {
  font-size: 10px;
  font-weight: 400;
}

.in-stock {
  color: #28a745;
}

.low-stock {
  color: #dc3545;
}

.add-to-cart-btn {
  width: 85px;
  height: 21px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  float: right;
  border-radius: 30px !important;
  border: none;
  background: linear-gradient(to right, #FB751E, #B93426);
}

.add-to-cart-btn:hover {
  background: linear-gradient(to right, #B93426, #FB751E);
}

/* Pagination styles */
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px 20px;
}

.btn-outline-secondary {
  border: none; /* Remove border */
  background-color: transparent; /* Remove background color */
  color: #B93426; /* Set text color */
  padding: 5px 10px;
  cursor: pointer;
  font-size: 25px;
  font-weight: bold;
}

.btn-outline-secondary:disabled {
  color: #B93426; /* Color when disabled */
  cursor: not-allowed; /* Change cursor to indicate it's disabled */
}

.page-number {
  display: inline-block;
  padding: 2px 10px;
  margin: 0 2px;
  background-color: #FFB07D3B; /* Background color for non-active page numbers */
  color: black; /* Text color */
  border: none; /* Remove border */
  cursor: pointer;
  border-radius: 4px; /* Optional: rounded corners */
}

.page-number.active {
  background-color: #FB751E; /* Background color for active page number */
  color: white; /* Text color for active page number */
}


/* Item details section styles */
.item-details-section {
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.item-details-content {
  margin: 20px 20px;
  display: flex;
  padding: 20px;
}

.item-details-info {
  flex: 1;
  padding-right: 20px;
}

.item-details-image {
  flex: 1;
}

.item-details-image img {
  max-width: 100%;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.request-now-btn{
  background: linear-gradient(to right, #FB751E, #B93426);
  border: none;
  padding: 5px 10px;
  border-radius: 30px;
  font-size: 15px;
}

.browse-more-btn {
  margin-top: 20px;
  font-style: italic;
  font-weight: bold;
  color: #B93426;
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}
.modal-dialog {
  position: relative;
  width: 90%;
  max-width: 600px;
  margin: 50px auto;
}
.modal-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.modal-header {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}
.modal-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px 10px;
  border-bottom: 1px solid #eee;
}
.modal-title {
  font-weight: bold;
  margin-top: 10px;
}
.modal-body {
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
}
.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
}
.cart-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff1ee;
  border-radius: 8px;
}
.cart-item-checkbox {
  margin-right: 10px;
}
.cart-item-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  margin-right: 10px;
}
.cart-item-details {
  flex-grow: 1;
}
.cart-item-price {
  font-weight: bold;
  color: #ff7e5f;
  margin-right: 10px;
}
.cart-item-quantity {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 20px;
  padding: 2px;
  border: 1px solid #ddd;
}
.btn-quantity {
  font-size: 15px;
  border: none;
  background: none;
  color: #ff7e5f;
  font-weight: bold;
}
.cart-item-quantity span {
  margin: 0 10px;
}
.btn-delete {
  color: #ff7e5f;
  background: none;
  border: none;
}
.add-item-btn {
  border-radius: 30px !important;
  color: #B93426;
  border-color: #B93426;
  font-weight: bold;
}
.add-item-btn:hover {
  background-color: #B93426;
  color: white;
  font-weight: bold;
}
.request-now-btn-model {
  border-radius: 30px !important;
  background: linear-gradient(to right, #FB751E, #B93426);
  border: none;
  color: white;
  font-weight: bold;
}
.request-now-btn-model:hover {
  background: linear-gradient(to right, #B93426, #FB751E);
  font-weight: bold;
}
