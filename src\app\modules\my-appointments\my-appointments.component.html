
<div class="container-fluid">
    <div class="row">
        <div class="sidebar-container">
        </div>
        <div class="main-content">
            <div class="container">
                <div class="header-row">
                    <div class="header-row-h1">
                        My Appointments
                    </div>
                    <!-- <button class="btn add-doctor-btn" (click)="openAddDoctorModal()">Add Doctor</button> -->
                    <div class="search-bar">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" placeholder="Search" [(ngModel)]="searchTerm" id="search-input" />
                    </div>
                </div>
                <div class="header-bottom-line"></div>
                <br>

                <div class="rectangle">
                    <table class="my-appointments-table">
                        <thead>
                            <tr>
                                <th> Patient Name</th>
                                <th> Clinic Address</th>
                                <th> Time</th>
                                <th> Date</th>
                                <th> App.No</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let MyAppointments of filteredMyAppointments()">

                                <td>{{ MyAppointments.patientName}}</td>
                                <td>{{ MyAppointments.clinicAddress }}</td>
                                <td>{{ MyAppointments.time}}</td>
                                <td>{{ MyAppointments.date}}</td>
                                <td>{{ MyAppointments.appNo}}</td>
                                <!-- <td></td> -->
                                <td class="action-cell">
                                    <button class="btn action-btn accept-btn">
                                        <i class="bi bi-check-circle"></i>
                                    </button>
                                    <button class="btn action-btn delete-btn">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                </td>
                                <!-- <td class="action-cell">
                                    <button class="btn action-btn edit-btn">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn action-btn delete-btn" (click)="deleteDoctor(doctor)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td> -->
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
