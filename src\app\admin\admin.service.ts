import { Injectable } from '@angular/core';
import { HttpService } from '../http.service';
import { Observable } from 'rxjs';
import { UserTemp, UserTempStatus, UserTempType } from '../auth/auth';

@Injectable({
  providedIn: 'root'
})
export class AdminService {

  constructor(private httpService:HttpService){}

  getAllUserTempList():Observable<UserTemp[] | null>{
    return this.httpService.request('GET','/admin/getAllUserTemps',{});
  }

  updateUserAsPermanant(userTempId:number):Observable<any>{
    const params = {userTempId}
    return this.httpService.request('GET','/admin/updateUserTempToUserPermanent',null,params,'text');
  }

  getUserTempStatusList(): UserTempStatus[] {
    return [
      UserTempStatus.ADMIN_APPROVED,
      UserTempStatus.ADMIN_REJECTED,
      UserTempStatus.CREATED,
      UserTempStatus.SENT_USER_VERIFICATION,
      UserTempStatus.USER_VERIFIED,
    ];
  }

  getUserTempTypeList(): UserTempType[] {
    return [
      UserTempType.CLINIC,
      UserTempType.DOCTOR,
      UserTempType.FUTURE_DENTIST,
      UserTempType.LABORATORY,
      UserTempType.SUPPLIER,
    ];
  }

}
