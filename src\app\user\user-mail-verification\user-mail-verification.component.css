html, body {
  height: 100%;
  margin: 0;
  font-family: 'Arial', sans-serif;
}

.page-background {
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: #f4f4f9;
  overflow: hidden;
  z-index: 1;
}

.verification-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  z-index: 10;
  position: relative;
  padding: 20px; /* Add padding for small screens */
}

.verification-content {
  background-color: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
  text-align: center;
}

h2 {
  margin-top: 20px;
  color: #ff6b00;
  font-weight: bold;
  margin-bottom: 30px;
  font-size: 24px; /* Make heading responsive */
}

.login-button, .retry-button {
  background-color: #ff6b00;
  border: #ff6b00 1px solid;
  border-radius: 15px;
  color: white;
  margin-top: 20px;
  padding: 10px 20px;
  font-size: 16px; /* Ensure button text scales */
  width: 100%; /* Make button responsive */
  max-width: 200px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-button:hover, .retry-button:hover {
  background-color: #e55a00;
}

.retry-button {
  background-color: #6c757d;
  border-color: #6c757d;
}

.retry-button:hover {
  background-color: #5a6268;
}

p {
  color: #333;
  font-size: 16px; /* Ensure text is responsive */
}

.loading-section {
  text-align: center;
  padding: 20px;
}

.loading-spinner {
  font-size: 24px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.verification-result {
  margin: 20px 0;
}

.success-message {
  color: #28a745;
  padding: 15px;
  border-radius: 8px;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.info-message {
  color: #17a2b8;
  padding: 15px;
  border-radius: 8px;
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
}

.error-message {
  color: #dc3545;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.success-message h3, .info-message h3, .error-message h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.success-message p, .info-message p, .error-message p {
  margin: 5px 0;
}

.rec1 {
  position: absolute;
  top: 0px;
  left: 740px;
  width: 900px;
  height: 1600px;
  background: #FB751E;
  z-index: 1;
  border-radius: 150px;
  transform: rotate(72deg);
}

.rec2 {
  position: absolute;
  top: 225px;
  left: -400px;
  width: 900px;
  height: 1800px;
  border: #FB751E 1px solid;
  z-index: 1;
  border-radius: 150px;
  transform: rotate(40deg);
}

/* Responsive Styles */
@media (max-width: 768px) {
  h2 {
    font-size: 20px; /* Scale heading for small screens */
    margin-bottom: 20px;
  }

  button {
    font-size: 14px; /* Smaller font for buttons on small screens */
    max-width: 100%; /* Full width buttons for small screens */
  }

  .rec1 {
    width: 600px; /* Adjust size for small screens */
    height: 1200px;
    left: 500px; /* Re-position to keep balance */
  }

  .rec2 {
    width: 600px;
    height: 1400px;
    left: -300px; /* Re-position to avoid overlap */
  }
}

@media (max-width: 480px) {
  h2 {
    font-size: 18px;
    margin-bottom: 15px;
  }

  button {
    font-size: 14px;
    padding: 8px 15px;
    max-width: 100%;
  }

  .rec1 {
    width: 500px;
    height: 1000px;
    left: 400px; /* Adjust for very small screens */
  }

  .rec2 {
    width: 500px;
    height: 1200px;
    left: -250px;
  }
}
