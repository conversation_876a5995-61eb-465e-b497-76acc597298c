/* .app-logo {
    height: 80px;
  }

  .app-header {
    background-color: #222;
    height: 150px;
    padding: 20px;
    color: white;
    text-align: center;
  }

  .app-title {
    font-size: 1.5em;
  } */

  /* Navbar styles */
  .logo{
    width: 150px;
    height: 40px;
  }
  .navbar {
    padding: 1% 5%;
  }

  .navbar-brand img {
    max-width: 100%;
    height: auto;
    max-height: 50px; /* Set a max height for the logo */
  }

  .nav-link {
    font-size: 1rem; /* Use rem for font sizes */
  }

  #login-btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-weight: bold;
    border: 2px solid #FE9652;
    background-color: #ffffff;
    color: #ff6f00;
    font-size: 1rem;
  }

  #login-btn:hover {
    background-image: linear-gradient(90deg, #FB751E 0%, #B93426 100%);
    color: #ffffff;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .navbar {
      padding: 1% 2%;
    }

    .nav-link {
      font-size: 0.875rem;
    }

    #login-btn {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }

    .navbar-brand img {
      max-height: 40px;
    }
  }

  @media (max-width: 576px) {
    .navbar {
      padding: 1% 1%;
    }

    .nav-link {
      font-size: 0.75rem;
    }

    #login-btn {
      padding: 0.5rem 0.75rem;
      font-size: 0.75rem;
    }

    .navbar-brand img {
      max-height: 30px;
    }
  }
