.sidebar-container {
    height: calc(100vh - 80px);
    width: 20%;
    overflow-y: auto;
    display: inline-block;
}

.main-content {
    height: calc(100vh - 80px);
    width: 80%;
    overflow-y: auto;
    padding: 20px;
    display: inline-block;
}

@media (max-width: 768px) {
    .sidebar-container,
    .main-content {
        width: 100%;
        height: auto;
        display: block;
    }
}

.header-row {
    font-weight: bold;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.header-row-h1 {
    color: black;
    font-size: 24px;
    font-weight: 600;
    line-height: 38.73px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 15px;
    width: 100%;
}

.add-service {
    font-size: 24px;
    color: #000;
}

.rectangle {
    width: 100%;
    max-width: 1090px;
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 3px 11.9px -1px #00000040;
    padding: 40px;
}

label {
    font-size: 16px;
    font-weight: 500;
    text-align: left; 
}

select, textarea, input {
    width: 100%; 
    padding: 10px; 
    font-size: 16px; 
    color: #495057;
    border-radius: 4px; 
    border: 1px solid #b3b3b3; 
    box-sizing: border-box; 
}

textarea:focus, input:focus {
    outline: none;
    border-color: #ff7a00;
}

.btn-secondary, .btn-primary {
    font-weight: 600;
    width: 100%; 
    max-width: 167px;
    height: 35px;
    border-radius: 18px;
}

.btn-secondary {
    border: 1px solid #FB751E;
    color: #FB751E;
    background: #FFFFFF;
}

.btn-primary {
    border: none;
    color: #FFFFFF;
    background: linear-gradient(to right, #FB751E, #B93426);
}

.row {
    width: 100%;
}

.d-flex {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap; 
    justify-content: flex-start; 
    gap: 10px;
}

.d-flex .btn {
    width: 100%; 
}

@media (max-width: 768px) {
    .d-flex {
        flex-direction: column; 
        align-items: stretch;
    }

    .btn-secondary, .btn-primary {
        width: 100%;
        max-width: none;
    }
}
