/* Base styles for desktop view */


/* General container styling */



h1 {
  color: black;
}

.show{
  display: block;
}

/* Summary section styles */

.appointments-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.appointment-status {
  padding: 20px;
  border-radius: 10px;
  color: white;
  flex: 1;
  margin: 0 10px;
  text-align: center;
}

.appointment-status.pending {
  background: linear-gradient(180deg, #CCAE0F 0%, #C89000 100%);
  margin-left: 0;
}

.appointment-status.confirmed {
  background: linear-gradient(180deg, #0075FF 0%, #25233A 100%);
  margin-right: 0;
}

.pending-appointments-notification {
  margin-bottom: 20px;
  color: #E15524;
}

.pending-appointments-notification-middle {
  margin-bottom: 20px;
  margin-top: 20px;
  color: #E15524;
}


.pending-appointments-list h6 {
  background-color: rgba(251, 117, 30, 0.88);
  color: white;
  padding: 10px;
  border-radius: 5px;
}


/* Appointment request styles */

.appointment-request {
  display: flex;
  justify-content: space-between;
  background-color: #F5F5F5;
  padding: 10px;
  gap: 20px;
  margin-bottom: 10px;
  border-radius: 5px;
  border-left: 3px solid #0075FF;
}

.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}


.appointment-details{
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  
}

.appointment-info span:first-child {
  font-weight: bold;
  display: flex;
  align-items: center;
}

.appointment-info span:first-child::before {
  content: ' ';
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.appointment-info div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.appointment-actions {
  display: flex;
  align-items: center;

}

.appointment-actions button {
  padding: 5px 10px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #0075FF12;
  color: #0075FF;
  border-radius: 7px;
  border: 1px solid #0075FF;
  margin-right: 10px;
}

.reject-btn {
  background-color: #B934261F;
  color: #B93426;
  border-radius: 7px;
  border: 1px solid #B93426;
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1050;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}
@media (max-width: 200px) {
  .popup-overlay {
    display: none;
  }
}


.modal.show {
  display: block;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - (.5rem * 2));
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, .2);
  border-radius: .3rem;
  outline: 0;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: .3rem;
  border-top-right-radius: .3rem;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.btn-close {
  padding: 0.75rem 1.25rem;
  color: #fff;
  background-color: #6c757d;
  border: none;
  cursor: pointer;
}


/* Mobile responsive styles */

@media (max-width: 800px) {
  
  .appointment-details-mobile{
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding-left: 10px;
  }
  
  .appointment-info-mobile{
    display: flex;
    flex-direction: column;
    gap: 15px;
    
  }
 
  .appointment-request{
    display: flex;
    flex-direction: column;
  }
  .accept-reject-buttons{
    display: flex;
    flex-wrap: wrap;
  }
}

@media (min-width: 801px) {
  .appointment-info-mobile{
    display: none;
  }
  .appointment-dashboard-container {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .appointments-summary {
      flex-direction: column;
      align-items: center;
  }
  .appointment-status {
      margin: 0;
      width: 100%;
  }
  .appointment-status.pending {
      margin-top: 5px;
      /* Adds spacing on mobile */
  }
  
  .appointment-actions {
      flex-direction: column;
      align-items: flex-start;
  }
  .appointment-actions button {
      width: 100%;
      margin-bottom: 5px;
  }
  .pending-appointments-notification{
    display: none;
  }
  .appointment-status.confirmed{
    margin-bottom: 20px;
  }
}

@media (min-width: 769px) {
  .pending-appointments-notification-middle{
    display: none;
  }
}

@media (max-width: 800px) {
  .appointment-list{
    padding: 10px;
  }
  
}

@media (max-width: 480px) {
  .appointment-dashboard-container {
      padding: 10px;
  }
  .appointment-status {
      padding: 15px;
      font-size: 14px;
  }
  .pending-appointments-list h6 {
      font-size: 16px;
      padding: 8px;
  }
  .appointment-request {
      padding: 8px;
  }
  .appointment-actions button {
      font-size: 14px;
      padding: 8px;
  }
  .modal-dialog-centered {
      min-height: auto;
  }
  .modal-content {
      border-radius: 0;
  }
}
