import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FutureDentistLayoutComponent } from './future-dentist-layout/future-dentist-layout.component';
import { FutureDentistDashboardComponent } from './future-dentist-dashboard/future-dentist-dashboard.component';
import { authGuard } from '../auth/auth.guard';

const routes: Routes = [
  {
    path:'',
    component:FutureDentistLayoutComponent,
    canActivateChild:[authGuard],
    children:[
      {path:'',component:FutureDentistDashboardComponent},
      {path:'**',redirectTo:''}
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FutureDentistRoutingModule { }
