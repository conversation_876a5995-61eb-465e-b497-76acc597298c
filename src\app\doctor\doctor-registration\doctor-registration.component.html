<app-default-navbar loggedUser="Hello Doctor" />
<div class="background-container">
  <div class="form-container">
    <h3>Doctor Registration</h3>
    <button class="backtoselection-button" (click)="navigateUserSelection()">
      Selection Menu
    </button>
    <form [formGroup]="doctorForm" (ngSubmit)="onUserTempRegister()">
      <div class="form-row">
        <div class="form-group">
          <label for="title">Title</label>
          <select
            id="title"
            name="title"
            formControlName="title"
            [(ngModel)]="userTemp.userTitle"
            style="width: 262px"
          >
            <option value="Prof">Prof.</option>
            <option value="Dr">Dr.</option>
          </select>
          <div
            *ngIf="
              doctorForm.get('title')?.invalid &&
              (doctorForm.get('title')?.dirty ||
                doctorForm.get('title')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('title')?.errors?.['required']"
              >Title is required.</small
            >
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            formControlName="firstName"
            [(ngModel)]="userTemp.mainName"
            (ngModelChange)="setFirstName()"
          />
          <div
            *ngIf="
              doctorForm.get('firstName')?.invalid &&
              (doctorForm.get('firstName')?.dirty ||
                doctorForm.get('firstName')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('firstName')?.errors?.['required']"
              >First Name is required.</small
            >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('firstName')?.errors?.['pattern']"
              >First Name can only contain letters.</small
            >
          </div>
        </div>

        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            formControlName="lastName"
            [(ngModel)]="userTemp.additionalName"
            (ngModelChange)="setLastName()"
          />
          <div
            *ngIf="
              doctorForm.get('lastName')?.invalid &&
              (doctorForm.get('lastName')?.dirty ||
                doctorForm.get('lastName')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('lastName')?.errors?.['required']"
              >Last Name is required.</small
            >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('lastName')?.errors?.['pattern']"
              >Last Name can only contain letters.</small
            >
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="regNo">SLMC Reg. Number</label>
          <input
            type="text"
            id="regNo"
            name="regNo"
            formControlName="regNo"
            [(ngModel)]="userTemp.registrationNumber"
          />
          <div
            *ngIf="
              doctorForm.get('regNo')?.invalid &&
              (doctorForm.get('regNo')?.dirty ||
                doctorForm.get('regNo')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('regNo')?.errors?.['required']"
              >SLMC Register Number is required.</small
            >
          </div>
          <small class="text-danger" *ngIf="isSLMCRegistered">{{
            slmcNumberExistsMessage
          }}</small>
        </div>

        <div class="form-group">
          <label for="telephone">Contact Number</label>
          <input
            type="text"
            id="telephone"
            name="telephone"
            formControlName="telephone"
            [(ngModel)]="userTemp.contactNumber"
          />
          <div
            *ngIf="
              doctorForm.get('telephone')?.invalid &&
              (doctorForm.get('telephone')?.dirty ||
                doctorForm.get('telephone')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('telephone')?.errors?.['required']"
              >Contact Number is required.</small
            >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('telephone')?.errors?.['pattern']"
              >Invalid Contact number.</small
            >
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="email">Email Address</label>
          <input
            type="email"
            id="email"
            name="email"
            formControlName="email"
            [(ngModel)]="userTemp.userEmail"
            (ngModelChange)="updateEmail()"
          />
          <div
            *ngIf="
              doctorForm.get('email')?.invalid &&
              (doctorForm.get('email')?.dirty ||
                doctorForm.get('email')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('email')?.errors?.['required']"
              >Email is required.</small
            >
          </div>
          <small class="text-danger" *ngIf="isEmailRegistered">{{
            userEmailExistsMessage
          }}</small>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="password">Password</label>
          <input
            type="password"
            id="password"
            name="password"
            formControlName="password"
            [(ngModel)]="userTemp.userPassword"
          />
          <div
            *ngIf="
              doctorForm.get('password')?.invalid &&
              (doctorForm.get('password')?.dirty ||
                doctorForm.get('password')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('password')?.errors?.['required']"
              >Password is required.</small
            >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('password')?.errors?.['minlength']"
              >Password must be at least 8 characters long.</small
            ><br />
            <small
              class="text-danger"
              *ngIf="doctorForm.get('password')?.errors?.['pattern']"
              >Password must contain at least one uppercase letter, one
              lowercase letter, one digit, and one special character.</small
            >
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Re-enter Password</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            formControlName="confirmPassword"
          />
          <div
            *ngIf="
              doctorForm.get('confirmPassword')?.invalid &&
              (doctorForm.get('confirmPassword')?.dirty ||
                doctorForm.get('confirmPassword')?.touched)
            "
          >
            <small
              class="text-danger"
              *ngIf="doctorForm.get('confirmPassword')?.errors?.['required']"
              >Please re-enter the password.</small
            >
          </div>
          <small
            class="text-danger"
            *ngIf="doctorForm.errors?.['mismatch'] && doctorForm.get('confirmPassword')?.dirty"
            >Password do not match.</small
          >
        </div>
      </div>
      <button #RegisterButton type="submit" class="register-button">Register</button>
    </form>
  </div>
</div>
