import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DoctorService } from 'src/app/doctor/doctor.service';
import { ScheduleService } from './service/schedule.service';
import { ClinicServices, Schedule } from './service/schedule';
import Swal from 'sweetalert2';
// Day mapping constant to map day abbreviations to full names
const DAY_MAPPING: { [key: string]: string } = {
  'Su': 'Sunday',
  'M': 'Monday',
  'TU': 'Tuesday',
  'W': 'Wednesday',
  'TH': 'Thursday',
  'F': 'Friday',
  'Sa': 'Saturday'
};

// Reverse Day mapping to map full names to abbreviations
const REVERSE_DAY_MAPPING: { [key: string]: string } = {
  'Sunday': 'Su',
  'Monday': 'M',
  'Tuesday': 'TU',
  'Wednesday': 'W',
  'Thursday': 'TH',
  'Friday': 'F',
  'Saturday': 'Sa'
};

interface Doctor {
  doctorId: number;
  firstName: string;
  regNo: string;
}

@Component({
  selector: 'app-clinic-setup-clinic',
  templateUrl: './clinic-setup-clinic.component.html',
  styleUrls: ['./clinic-setup-clinic.component.css']
})
export class ClinicSetupClinicComponent implements OnInit{


  dates = ['Su', 'M', 'TU', 'W', 'TH', 'F', 'Sa']; // Days of the week
  disabledDays: string[] = [];
  selectedDay: string = ''; // Track the selected day
  fromTime: string = ''; // Time input for "from"
  toTime: string = ''; // Time input for "to"
  selectedValue1: string = 'AM'; // Track AM/PM for 'from' time
  selectedValue2: string = 'AM'; // Track AM/PM for 'to' time
  showHolidayDateInput: boolean = false; // Toggle holiday date input visibility
  isClosed: boolean = false; // Status for "Mark as Closed"
  holidayDate: boolean = false; // Store the selected holiday date
  isUpdateMode: boolean = false;
  schedules: Schedule[] = []; // Array to store fetched schedules
  clinicId: number = 0; // Initialize to a default value
  editingScheduleId: number | null = null;
  doctors: Doctor[] = []; // Array to store fetched doctors

  constructor(
    private scheduleService: ScheduleService,
    private router: Router,
    private doctorService: DoctorService
  ) {}
ngOnInit(): void {
    const userIdString = localStorage.getItem('userid'); // Retrieve userId from localStorage
    if (userIdString) {
      const userId = parseInt(userIdString, 10);
      this.fetchAndStoreClinicId(userId); // Fetch and store clinicId
      console.log('Component initialized. Fetching schedules...');
      this.loadClinicId(); // Load clinicId from localStorage
      this.getSchedules(); // Fetch schedules when component initializes
      this.getDoctors(); // Fetch doctors when the component initializes
      this.loadExistingServices(); // Load existing services on component initialization
    } else {
      alert('User ID not found. Please log in or select a clinic.');
      this.router.navigate(['/login']); // Redirect to login if userId is not found
    }
  }

  // Fetch and store clinicId from the backend
  fetchAndStoreClinicId(userid: number): void {
    this.scheduleService.getClinicIdByUserId(userid).subscribe(
      (clinicId) => {
        console.log('Received clinicId:', clinicId); // Debugging line
        if (clinicId) {
          localStorage.setItem('clinicId', clinicId.toString());
          console.log('Clinic ID stored in localStorage:', clinicId);
        } else {
          this.handleError('No clinic found for the provided user ID');
        }
      },
      (error) => {
        this.handleError('Error fetching clinic ID: ' + error);
      }
    );
  }

  // Load the clinicId from localStorage
  loadClinicId(): void {
    const clinicIdString = localStorage.getItem('clinicId'); // Retrieve the clinicId from localStorage
    if (clinicIdString) {
      this.clinicId = parseInt(clinicIdString, 10); // Convert it to a number
      console.log('Loaded Clinic ID from localStorage:', this.clinicId); // Log the loaded clinicId
    } else {
      console.log('No Clinic ID found in localStorage.'); // Log if no clinicId was found
    }
  }

  // Fetch all doctors from the backend
  getDoctors(): void {
    const userId = localStorage.getItem('userid'); // Retrieve userId from localStorage
    if (userId) {
      console.log('Fetching doctors for userId:', userId);
      this.doctorService.getDoctorsByUserId(userId).subscribe(
        (data: Doctor[]) => {
          this.doctors = data; // Populate the doctors array
          console.log('Doctors fetched successfully:', data);
        },
        (error) => {
          console.error('Error fetching doctors:', error);
        }
      );
    } else {
      console.warn('No userId found in localStorage.');
    }
  }

  redirectToDoctorList(): void {
    this.router.navigate(['./list-doctor']);
  }

  // Toggle the sidebar visibility
  toggleSidebar() {
    const sidebarElement = document.getElementById('sidebar');
    if (sidebarElement) {
      sidebarElement.classList.toggle('open');
      console.log(
        'Sidebar toggled:',
        sidebarElement.classList.contains('open') ? 'opened' : 'closed'
      );
    } else {
      console.error('Sidebar element not found.');
    }
  }

  // Function to select a day and keep the button "pressed"
  selectDay(day: string): void {
    if (this.disabledDays.includes(day) && (!this.isUpdateMode || this.selectedDay !== day)) {
      alert(`${this.getFullDayName(day)} is already scheduled.`);
      return;
    }
    this.selectedDay = this.selectedDay === day ? '' : day;
  }

  // Toggle between AM/PM for both From and To time
  toggleAmPm(timeField: string) {
    if (timeField === 'from') {
      this.selectedValue1 = this.selectedValue1 === 'AM' ? 'PM' : 'AM';
      console.log('From time AM/PM toggled to:', this.selectedValue1);
    } else {
      this.selectedValue2 = this.selectedValue2 === 'AM' ? 'PM' : 'AM';
      console.log('To time AM/PM toggled to:', this.selectedValue2);
    }
  }

 // Validate the form before saving
 validateForm(): boolean {
  console.log('Selected Day:', this.selectedDay);
  console.log('From Time:', this.fromTime);
  console.log('To Time:', this.toTime);
  console.log('Clinic ID:', this.clinicId);
  console.log('Closed:', this.isClosed);

  if ( !this.fromTime || !this.toTime || !this.clinicId) {
    if (this.holidayDate == true && this.selectedDay) {
      return true; // Assuming no time validation is needed for holidays/closed days
    }
    if(this.isClosed == true){
      return true;
    }
    alert('Please select a day and provide valid times.');
    return false;
  }
  

  return true;
}


  // Get all schedules from the backend
  getSchedules(): void {
    this.scheduleService.getSchedules().subscribe(
      (data: Schedule[]) => {
        const storedUserId = localStorage.getItem('clinicId'); // Use userId
        if (storedUserId) {
          const userIdNumber = Number(storedUserId);
          this.schedules = data.filter(
            (schedule) => schedule.clinics?.clinicId === userIdNumber
          );

          // Convert full day names in schedules to abbreviations for comparison
          this.disabledDays = this.schedules.map(
            (schedule) => REVERSE_DAY_MAPPING[schedule.date]
          );
        }
      },
      (error) => {
        console.error('Error fetching schedules:', error);
      }
    );
  }

  // Save the selected schedule
saveSchedule(): void {
  // Validate the form inputs before proceeding
  if (!this.validateForm()) return;

  // Retrieve clinicId from localStorage
  const clinicIdString = localStorage.getItem('clinicId');
  const clinicId = clinicIdString ? parseInt(clinicIdString, 10) : null;

  // Handle the case where clinicId is not found or invalid
  if (clinicId === null) {
    this.handleError('Clinic ID not found or invalid');
    return;
  }

  const fullDayName = this.getFullDayName(this.selectedDay);

  const schedule: Schedule = {
    clinics: { clinicId: clinicId }, // Use clinicId here
    date: fullDayName,
    fromTime: this.fromTime, // Save in 24-hour format
    toTime: this.toTime,     // Save in 24-hour format
    // isClosed: this.isClosed,
    holidayDate: this.holidayDate || this.isClosed,
    scheduleId: this.editingScheduleId
  };

  // Check if we're in update mode
  if (this.isUpdateMode && this.editingScheduleId !== null) {
    this.scheduleService.updateSchedule(this.editingScheduleId, schedule).subscribe(
      (updatedSchedule: Schedule) => {
        // Update the schedules list by removing the old schedule and adding the updated one
        this.schedules = this.schedules.filter(s => s.scheduleId !== this.editingScheduleId);
        this.schedules.push(updatedSchedule);
        this.disabledDays.push(this.selectedDay);
        this.getSchedules(); // Refresh the schedules list
        this.resetForm(); // Reset the form inputs
      },
      (error) => this.handleError(error) // Handle any errors
    );
  } else {
    // In create mode, save a new schedule
    this.scheduleService.saveSchedule(schedule).subscribe(
      (data: Schedule) => {
        this.disabledDays.push(this.selectedDay);
        this.getSchedules(); // Refresh the schedules list
        this.resetForm(); // Reset the form inputs
      },
      (error) => this.handleError(error) // Handle any errors
    );
  }
}

  // Update a specific schedule
  updateSchedule(schedule: Schedule): void {
    this.isUpdateMode = true;
    this.editingScheduleId = schedule.scheduleId || null;

    this.selectedDay = REVERSE_DAY_MAPPING[schedule.date];
    this.disabledDays = this.dates.filter((date) => date !== this.selectedDay);
    this.fromTime = schedule.fromTime.split(' ')[0];
    this.selectedValue1 = schedule.fromTime.split(' ')[1];
    this.toTime = schedule.toTime.split(' ')[0];
    this.selectedValue2 = schedule.toTime.split(' ')[1];
  }

  deleteClinicDoctor(doctorId: number): void {
    // Use SweetAlert2 for confirmation dialog
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        // Call the deleteDoctor method with doctorId
        this.doctorService.deleteClinicDoctor(doctorId).subscribe(
          () => {
            // Remove the doctor from the list after successful deletion
            this.doctors = this.doctors.filter(
              (doctor) => doctor.doctorId !== doctorId
            );
            console.log(`Doctor with ID ${doctorId} deleted successfully.`);
            Swal.fire('Deleted!', 'Doctor has been deleted.', 'success');
          },
          (error) => {
            console.log(error)
            console.error(`Error deleting doctor with ID ${doctorId}.`, error);
            Swal.fire(
              'Error!',
              'There was an error deleting the doctor. Please try again.',
              'error'
            );
          }
        );
      }
    });
  }

  // Delete a schedule
  deleteSchedule(scheduleId: number | undefined): void {
    if (!scheduleId) {
      console.error('Invalid scheduleId:', scheduleId);
      return;
    }

    if (!confirm('Are you sure you want to delete this schedule?')) {
      return;
    }

    this.scheduleService.deleteSchedule(scheduleId).subscribe(
      () => {
        console.log(`Schedule with ID: ${scheduleId} deleted successfully.`);
        this.getSchedules();
      },
      (error) => {
        console.error(`Error deleting schedule with ID: ${scheduleId}`, error);
        alert(
          'An error occurred while deleting the schedule. Please try again.'
        );
      }
    );
  }

  // Helper method to map the selected day abbreviation to the full day name
  getFullDayName(day: string): string {
    return DAY_MAPPING[day] || day;
  }

  // Reset the form after saving or updating a schedule
  resetForm(): void {
    console.log('Resetting form...');
    this.selectedDay = '';
    this.fromTime = '';
    this.toTime = '';
    this.selectedValue1 = 'AM';
    this.selectedValue2 = 'AM';
    this.isClosed = false;
    this.holidayDate = false;
  }

  // General error handler
  handleError(error: any): void {
    console.error('Error occurred:', error);
    alert('An error occurred. Please try again.');
  }
  selectedServiceId: number | null = null; // Bind to ngModel
  existingServices: any[] = []; // To store existing services

  loadExistingServices(): void {
    this.clinicId = Number(localStorage.getItem('clinicId')); // Retrieve clinicId from local storage
    this.scheduleService.getClinicServicesByClinicId(this.clinicId).subscribe(
        (services) => {
            this.existingServices = services; // Store the existing services
        },
        (error) => {
            console.error('Error loading existing services', error);
            // Handle error response, e.g., show an error message
        }
    );
}

  saveService(): void {
    if (this.selectedServiceId !== null && this.clinicId !== null) {
        // Debugging logs
        console.log('Selected Service ID:', this.selectedServiceId);
        console.log('Existing Services:', this.existingServices);

        // Check if the service is already added
        const isServiceAlreadyAdded = this.existingServices.some(service =>
            service.services.clinicServiceCategoryId === Number(this.selectedServiceId) // Access the service ID correctly
        );

        console.log('Is Service Already Added:', isServiceAlreadyAdded); // Debugging log

        if (isServiceAlreadyAdded) {
            Swal.fire({
                icon: 'warning',
                title: 'Service Already Added',
                text: 'This service has already been added for the selected clinic.',
            });
            return; // Exit the function early
        }

        // Create the clinic service object
        const clinicService: ClinicServices = {
            clinics: { clinicId: this.clinicId },
            services: { clinicServiceCategoryId: this.selectedServiceId } // Ensure this matches your interface
        };

        // Save the new clinic service
        this.scheduleService.saveClinicService(clinicService).subscribe(
            (response) => {
                console.log('Service saved successfully', response);
                this.loadExistingServices(); // Reload existing services to update the list
                Swal.fire({
                    icon: 'success',
                    title: 'Service Added',
                    text: 'The service has been successfully added.',
                });
            },
            (error) => {
                console.error('Error saving service', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error Saving Service',
                    text: 'There was an error saving the service. Please try again.',
                });
            }
        );
    } else {
        console.warn('Selected service or clinic ID is missing');
        Swal.fire({
            icon: 'info',
            title: 'Missing Information',
            text: 'Please select a service before saving.',
        });
    }
  }




  deleteService(clinicServiceId: number | null): void {
    if (clinicServiceId !== null) {
        this.scheduleService.deleteClinicService(clinicServiceId).subscribe(
            (response) => {
                console.log('Service deleted successfully', response);
                // Reload existing services to update the list
                this.loadExistingServices();
                Swal.fire({
                    icon: 'success',
                    title: 'Service Deleted',
                    text: 'The service has been successfully deleted.',
                });
            },
            (error) => {
                console.error('Error deleting service', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error Deleting Service',
                    text: 'There was an error deleting the service. Please try again.',
                });
            }
        );
    } else {
        console.warn('Service ID is missing');
        Swal.fire({
            icon: 'info',
            title: 'Missing Information',
            text: 'Unable to delete the service. Please try again.',
        });
    }
  }

}
