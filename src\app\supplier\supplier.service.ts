import { Injectable } from '@angular/core';
import { Supplier, SupplierInventory } from './supplier';
import { Observable } from 'rxjs';
import { HttpService } from '../http.service';

@Injectable({
  providedIn: 'root',
})
export class SupplierService {
  constructor(private httpService: HttpService) {}

  saveSupplierInventory(formData: FormData): Observable<any> {
    return this.httpService.request('POST', '/saveSupplierInventoryItem', formData);
  }


  getSupplierByUserId(userId: number): Observable<Supplier> {
    return this.httpService.request('GET', `/getSupplierByUserId/${userId}`, {});
  }

  saveSupplier(supplier: Supplier): Observable<any> {
    return this.httpService.request('POST', '/saveSupplier', supplier);
  }

  getAllSuppliers(): Observable<Supplier[]> {
    return this.httpService.request('GET', '/supplierList', {});
  }

  getSupplierById(id: number): Observable<Supplier> {
    return this.httpService.request('GET', `/getSupplierById/${id}`, {});
  }

  updateSupplier(id: number, supplier: Supplier): Observable<object> {
    return this.httpService.request('PUT', `/updateSupplier/${id}`, supplier);
  }

  deleteSupplier(id: number): Observable<any> {
    return this.httpService.request('DELETE', `/deleteSupplier/${id}`, {});
  }

  supplierNameExists(supplierName: string): Observable<any> {
    const params = { supplierName };
    return this.httpService.request('GET', `/check-Supplier`, null, params);
  }

  // Supplier-Clinic APIs
  getOrderRequestBySupplierId(supplierId:number) {
    return this.httpService.request('GET',`/getClinicOrdersHeadersFromSupplierId/${supplierId}`,null)
  }

  getOrderDetailsBySupplierId(headerId:number) {
    return this.httpService.request('GET',`/getClinicOrdersDetailsFromSupplierId/${headerId}`,null)
  }
}
