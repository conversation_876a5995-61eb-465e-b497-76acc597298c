import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Laboratory, LaboratoryCategory, LaboratorySetup, LaboratorySubCategory } from '../laboratory';
import { Router } from '@angular/router';
import { LaboratoryService } from '../laboratory.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-laboratory-services',
  templateUrl: './add-laboratory-services.component.html',
  styleUrls: ['./add-laboratory-services.component.css']
})
export class AddLaboratoryServicesComponent implements OnInit {
  searchTerm: string = '';
  laboratorySetup: LaboratorySetup = new LaboratorySetup();
  laboratorySetupForm: FormGroup;
  laboratoryCategories: LaboratoryCategory[] = [];
  laboratorySubCategories: LaboratorySubCategory[] = [];
  isLoading: boolean = false; // Loading state variable

  constructor(
    private fb: FormBuilder,
    private laboratoryService: LaboratoryService,
    private router: Router,
  ) {
    this.laboratorySetupForm = this.fb.group({
      category: [null, Validators.required],
      subCategory: [null, Validators.required],
      description: ['', Validators.required],
      price: ['', [Validators.required, Validators.pattern('^\\d+(\\.\\d{1,2})?$')]],
    });
  }

  ngOnInit(): void {
    this.loadLaboratoryCategories();
  }

  loadLaboratoryCategories(): void {
    this.laboratoryService.getLaboratoryCategoriesList().subscribe(
      (data: LaboratoryCategory[]) => {
        console.log('Received categories:', data);
        this.laboratoryCategories = data;
      },
      (error) => {
        console.error('Error fetching laboratory categories:', error);
      }
    );
  }

  onCategoryChange(): void {
    const selectedCategory = this.laboratorySetupForm.get('category')?.value;
    if (selectedCategory) {
        this.getLaboratorySubCategories(selectedCategory.laboratoryCategoryId);
    } else {
        this.laboratorySubCategories = [];
    }
  }

  getLaboratorySubCategories(categoryId: number | null): void {
    if (categoryId) {
      this.laboratoryService.getLaboratorySubCategoriesList(categoryId).subscribe(
        (data: LaboratorySubCategory[]) => {
          this.laboratorySubCategories = data;
        },
        (error) => {
          console.error('Error fetching subcategories:', error);
        }
      );
    } else {
      this.laboratorySubCategories = [];
    }
  }

  saveLaboratoryService(): void {
    if (this.laboratorySetupForm.valid) {
      this.isLoading = true; 
  
      this.laboratorySetup.laboratoryCategoryId = this.laboratorySetupForm.get('category')?.value;
      this.laboratorySetup.laboratorySubCategoryId = this.laboratorySetupForm.get('subCategory')?.value;
      this.laboratorySetup.description = this.laboratorySetupForm.get('description')?.value;
      this.laboratorySetup.price = this.laboratorySetupForm.get('price')?.value;
      this.laboratorySetup.status = "Active";
      const userIdString = localStorage.getItem('userid');
      const userId = userIdString ? parseInt(userIdString, 10) : null;
  
      if (userId !== null) {
        this.laboratoryService.saveLaboratorySetup(userId, this.laboratorySetup).subscribe(
          (response) => {
            console.log('Laboratory setup saved successfully:', response);
            this.isLoading = false; 
  
            Swal.fire({
              title: 'Success!',
              html: '<div style="color: #ff7a00;"><i class="fas fa-check-circle fa-3x"></i></div><p style="margin-top: 20px;">Laboratory setup saved successfully.</p>',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ff7a00'
            }).then(() => {

              this.laboratorySetupForm.reset();
            });
  
            this.router.navigate(['/add-laboratory-services']);
          },
          (error) => {
            console.error('Failed to save laboratory setup:', error);
            Swal.fire({
              title: 'Error!',
              html: '<i class="fas fa-exclamation-triangle" style="color: #B93426; font-size: 48px;"></i><p style="margin-top: 20px;">There was and error with saving laboratory setup. Please try again.</p>', 
              confirmButtonColor: '#B93426' 
            });
            this.isLoading = false; 
          }
        );
      } else {
        console.error('User ID is not available in localStorage.');
        this.isLoading = false; 
      }
    } else {
      console.log('Form is invalid');
      Swal.fire({
        title: 'Error!',
        html: '<i class="fas fa-exclamation-triangle" style="color: #B93426; font-size: 48px;"></i><p style="margin-top: 20px;">There was and error with saving laboratory setup. Please try again.</p>', 
        confirmButtonColor: '#B93426' 
      });
    }
  }
  
}
