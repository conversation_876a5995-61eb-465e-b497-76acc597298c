import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { LaboratorySetup } from '../../laboratory/laboratory';
import { ClinicLaboratoryOrder } from '../clinic';
import { ClinicService } from '../clinic.service';
import Swal from 'sweetalert2';

function toothNumberValidator(control: AbstractControl): ValidationErrors | null {
  const value = control.value;
  if (value !== null && value !== '') {
    const numberValue = Number(value);
    if (isNaN(numberValue) || numberValue <= 0 || numberValue > 32) {
      return { invalidToothNumber: true };
    }
  }
  return null;
}

function contactNumberValidator(control: AbstractControl): ValidationErrors | null {
  const value = control.value;
  if (value !== null && value !== '') {
    const length = value.length;
    if (length < 10) {
      return { tooShort: true };
    } else if (length > 10) {
      return { tooLong: true };
    }
  }
  return null;
}

function ageValidator(control: AbstractControl): ValidationErrors | null {
  const value = control.value;
  if (value) {
    const today = new Date();
    const birthDate = new Date(value);

    // Calculate the age based on year difference
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();
    const dayDifference = today.getDate() - birthDate.getDate();

    // Adjust age if the birth date hasn't occurred yet this year
    if (monthDifference < 0 || (monthDifference === 0 && dayDifference < 0)) {
      age--;
    }

    // Log for debugging
    console.log("Today : " + today.toLocaleDateString());
    console.log("Birthday : " + birthDate.toLocaleDateString());
    console.log("Age : " + age);

    // Define age limit, e.g., 1 year
    if (age < 1) {
      return { tooYoung: true };
    }
  }
  return null;
}

@Component({
  selector: 'app-clinic-add-laborator-order',
  templateUrl: './clinic-add-laborator-order.component.html',
  styleUrls: ['./clinic-add-laborator-order.component.css']
})

export class ClinicAddLaboratorOrderComponent implements OnInit {
  orderForm: FormGroup;
  isSubmitting: boolean = false;
  laboratorySetup: any;
  minDate: string;

  constructor(
    private formBuilder: FormBuilder,
    private clinicService: ClinicService
  ) {

    const today = new Date();
    const currentHours = today.getHours();
    const currentMinutes = today.getMinutes();

    if (currentHours < 12 || (currentHours === 12 && currentMinutes === 0)) {
      this.minDate = today.toISOString().split('T')[0];
    } else {
      today.setDate(today.getDate() + 1);
      this.minDate = today.toISOString().split('T')[0];
    }

    this.orderForm = this.formBuilder.group({
      patientName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      dateOfBirth: ['', [Validators.required, ageValidator]],
      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]*$/), contactNumberValidator]],
      expectedDate: ['', Validators.required],
      toothNumber: ['', [Validators.required, Validators.pattern(/^[0-9]*$/), toothNumberValidator]],
      occlusion: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      toothSurface: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      marginType: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      typeOfCrown: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      crownDesign: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      shade: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      materialSpecifications: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]*$/)]],
      additionalInstructions: ['', [Validators.required, Validators.pattern(/^[a-zA-Z0-9\s]*$/)]],
      laboratorySetupId: null,
      status: 'Pending'
    });
  }

  ngOnInit(): void {
    const laboratorySetup = localStorage.getItem('laboratorySetup');
    if (laboratorySetup) {
      const parsedSetup: LaboratorySetup = JSON.parse(laboratorySetup);
      this.laboratorySetup = parsedSetup;
      this.orderForm.patchValue({ laboratorySetupId: parsedSetup });
    } else {
      console.log('No Laboratory Setup found in local storage');
    }
  }

  saveLaboratoryOrder(): void {
    if (this.isSubmitting) {
      return;
    }

    const userId = Number(localStorage.getItem('userid'));

    if (this.orderForm.valid) {
        this.isSubmitting = true;
        const orderData: ClinicLaboratoryOrder = { ...this.orderForm.value };
        this.clinicService.saveLaboratoryOrder(orderData, userId).subscribe(
            response => {
                this.isSubmitting = false;

                Swal.fire({
                  title: 'Success!',
                  html: '<div style="color: #ff7a00;"><i class="fas fa-check-circle fa-3x"></i></div><p style="margin-top: 20px;">Order saved successfully.</p>',
                  confirmButtonText: 'OK',
                  confirmButtonColor: '#ff7a00'
                });

                this.orderForm.reset();
            },
            error => {
                this.isSubmitting = false;

                Swal.fire({
                  title: 'Error!',
                  html: '<i class="fas fa-exclamation-triangle" style="color: #B93426; font-size: 48px;"></i><p style="margin-top: 20px;">Something went wrong.</p>',
                  confirmButtonColor: '#B93426'
                });


                console.error('Error saving order', error);
            }
        );
    } else {
        console.log('Form is not valid');
        this.orderForm.markAllAsTouched();
    }
  }
}
