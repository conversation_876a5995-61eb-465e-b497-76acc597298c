
name: Build & Deploy Angular to Tom<PERSON>

on:
  push:
    branches:
      - main   # or your deployment branch

jobs:
  build-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm install

      - name: Build Angular App
        run: npm run build

      - name: Package as WAR
        run: |
          mkdir -p target/mydent
          cp -r dist/mydent-frontend/* target/mydent/
          cd target && jar -cvf mydent.war mydent

      - name: Deploy to Tom<PERSON>
        uses: appleboy/scp-action@v0.1.7
        with:
          host: *************
          username: administrator 
          password: K5wMJ#wB0q
          port: 22
          source: target/mydent.war
          target: C:\deployee

