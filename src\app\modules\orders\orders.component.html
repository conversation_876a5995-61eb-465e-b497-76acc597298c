<div class="container-fluid">
    <div class="row">
        <div class="sidebar-container">
        </div>
        <div class="main-content">
            <div class="send-orders-container">
                <div class="header-row">
                    <div class="header-row-h1">
                        Orders
                    </div>

                    <div class="search-bar">
                        <i class="bi bi-search search-icon"></i>
                      <input type="text" placeholder="Search Order" [(ngModel)]="searchTerm" id="search-input" />
                    </div>
                </div>
                <div class="header-bottom-line"></div>
                <br>
                <div>
                    <div class="send-orders-list-row-new">
                        Your Orders
                    </div>
                    <div>

                      <button (click)="showAll = !showAll" class="see-all-button">See All></button>
                    </div>
                </div>
                <table class="send-order-table">
                    <thead>
                        <tr>
                            <th width="15%"> OrderID</th>
                            <th width="15%"> Date</th>
                            <th width="15%"> Time</th>
                            <th width="30%"> Laboratory</th>
                            <th width="15%"> Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let orders of filteredorders()">
                            <td>
                                <input type="checkbox"> {{ orders.orderID }}
                            </td>
                            <td>{{ orders.date}}</td>
                            <td>{{ orders.time }}</td>
                            <td>{{ orders.laboratory}}</td>
                            <td class="status-cell">
                                <button [ngClass]="{'status pending': orders.status === 'Pending', 'status completed': orders.status === 'Completed','status progress': orders.status === 'Progress','status cancelled': orders.status === 'Cancelled'}">{{orders.status }}</button>
                              <img src="assets/images/3dot.png" class="dot">
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="pagination-container">
                    <ul class="pagination">
                      <li class="page-item" [class.disabled]="currentPage === 1" (click)="goToPage(currentPage - 1)">
                        <a class="page-link" aria-label="Previous">
                          <span aria-hidden="true">&laquo;</span>
                        </a>
                      </li>
                      <li class="page-item" *ngFor="let page of visiblePages" [class.active]="page === currentPage" (click)="goToPage(page)">
                        <a class="page-link">{{ page }}</a>
                      </li>
                      <li class="page-item" (click)="goToPage(currentPage + 1)">
                        <a class="page-link" aria-label="Next">
                          <span aria-hidden="true">&raquo;</span>
                        </a>
                      </li>
                    </ul>
                  </div>
            </div>
        </div>
    </div>
</div>
