.button-base {
  border-radius: 5px;
  padding-inline: 30px;
  height: 40px;
  font-size: 14px;
  outline: none;
  box-shadow: none;
  border: 1px solid transparent;
  font-weight: 500;
}

.button-base:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  cursor: not-allowed;
  opacity: 0.6;
}

.primary-button{
  background: linear-gradient(to right, #fb751e, #b93426) !important;
  color: white;
}

.primary-button:disabled{
  background: #d3d3d3 !important;
  color: #3a3a3a;
  cursor: not-allowed;
  opacity: 0.6;
}

.secondary-button{
  border:1px solid  #fb751e;
  background: white !important;
  color: #fb751e !important;
  transition: background .2s cubic-bezier(0.6, 0.04, 0.98, 0.335);
}

.secondary-button:disabled{
  border-color: #d3d3d3;
  color: #3a3a3a !important;
  cursor: not-allowed;
  opacity: 0.6;
}

.secondary-button:not(:disabled):hover{
  background: linear-gradient(to right, #fb751e, #b93426) !important;
  color: white !important;
  border-color: transparent;
}
