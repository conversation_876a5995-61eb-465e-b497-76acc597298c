body {
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    margin: 0;
    padding: 0;
}

.container {
    width: 100%;
    margin: 0 auto;

    margin-left: 25%;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

header h1 {
    margin: 0;
    padding: 10;
}

.search-add {
    display: flex;
    align-items: center;
}

.search-add input {
    padding: 5px;
    margin-right: 10px;
}

.add-button {
    background-color: #ff7f50;
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
}

main {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.order-requests h2 {
    margin-top: 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0px;
}

thead th {
    background: #FE9652;
    border: black;
    text-align: left;
    padding: 10px;
}

tbody tr:nth-child(even) {
    background: #FFB07D1F;

}

tbody td {
    padding: 10px;
}

.status {
    padding: 5px 10px;
    border-radius: 5px;
}



.view-request {
    background-color: #ff7f50;
    color: white;
    border: none;
    padding: 5px 10px;
    cursor: pointer;
}



.page {
    background-color: #ddd;
    border: none;
    padding: 10px 15px;
    margin: 0 5px;
    cursor: pointer;
}

.page:hover {
    background-color: #ccc;
}
.send-orders-container {
    padding: 20px;
}

.header-row,
.send-orders-list-row {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}



.see-all-button {
    width: 114px;
    height: 0px;
    margin-right: 0px;
    margin-left: 1090px;
    font-family: Inter;
    font-size: 15px;
    font-weight: 400;
    line-height: 18.15px;
    text-align: left;
    color: #D85322;
    background: none;
    border: #ddd;
}

.header-row-h1 {
    width: 274px;
    height: 39px;
    top: 149px;
    left: 403px;
    gap: 0px;
    opacity: 0px;
    font-family: Inter;
    font-size: 32px;
    font-weight: 600;
    line-height: 38.73px;
    text-align: left;
    color: #000;
}

.header-bottom-line {
    border-bottom: 2px solid #ccc;
    margin-top: 10px;
}

.search-bar {
    position: relative;
    margin: 20px 0;
    width: 40%;

}

#search-input {
    padding: 10px;
    width: 100%;
    padding-left: 30px;
    border: 1px solid #ccc;
    border-radius: 30px;
    border-width: 30%;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

#printButton {
  font-size: 16px;
  border: 1px solid #FB751E;
  color: #FB751E;
  width: 30%;
  height: 35px;
  gap: 0px;
  border-radius: 50px ;
  border: 1px 0px 0px 0px;
  opacity: 0px;

}


.view-request-button {
margin-left: 5%;
    border: 1px solid #073EFF;
  background-color: #ffffff;
  opacity: 90%;
    color: #073EFF;
    text-align: center;
    text-decoration: none;
    display: inline-block;
  font-weight: bold;
  width: 60%;
    font-size: 16px;
    border-radius: 18px;
    transition: background-color 0.3s, color 0.3s;
}

.view-request-button:hover {
    background-color: #F39C9C;
    color: #FFFFFF;
}

.sidebar-container {
    height: calc(6vh - 60px);
    width: 10%;
    overflow-y: auto;

    display: block;
}

.dot{
  margin-left: 90%;
  margin-top: -25%;
}

@media (max-width: 768px) {
    .sidebar-container,
    .main-content {
        width: 100%;
        display: block;
    }
}
















