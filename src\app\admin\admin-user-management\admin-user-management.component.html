<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12">
        <!-- Header -->
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-5">
                <h3 class="fs-5 m-0 p-0" style="font-weight: 600">
                  User Management
                </h3>
                <p class="text-black-50 m-0 p-0" style="font-size: 12px">
                  Manage user profiles, roles, and permissions.
                </p>
              </div>
              <div class="col-7 position-relative d-flex justify-content-end">
                <div class="input-group search-input position-relative px-0" style="width: max-content;">
                  <span class="input-group-text bg-transparent border-0" style="border-right: 1px solid rgb(210,210,210) !important;"><i class="bi bi-funnel-fill text-orange"></i></span>
                  <select class="form-select custom-select border-0 position-relative" [(ngModel)]="selectTerm" >
                    <option selected value="0">All</option>
                    <option selected value="USER_VERIFIED">Pending</option>
                    <option selected value="ADMIN_APPROVED">Approved</option>
                    <option selected value="ADMIN_REJECTED">Rejected</option>
                    <option selected value="SENT_USER_VERIFICATION">Waiting</option>
                    <option selected value="CREATED">Unsuccessfull</option>
                  </select>
                </div>
                <!-- Input for Search Term -->
                <input
                    class="search-input position-relative ms-3"
                    [(ngModel)]="searchTerm"
                    type="text"
                    placeholder="Search From Here">
              </div>
            </div>
            <div class="row mt-4">
              <hr class="border-secondary" />
            </div>

            <div class="row my-2 mb-4">
              <div class="col-2 d-flex justify-content-start">
                <div class="d-flex px-2 py-1 cb-base">
                  <input type="checkbox" [(ngModel)]="listingOrderAsc" class="custom-check-box">
                  <p style="font-size: 13px; font-weight: 500;" class="ms-2">Accending Order</p>
                </div>
              </div>
              <div [formGroup]="filterForm" class="col-10 d-flex justify-content-end pe-0">
                <div class="d-flex px-2 py-1 cb-base">
                  <input type="checkbox" formControlName="doctor" class="custom-check-box">
                  <p style="font-size: 13px; font-weight: 500;" class="ms-2">Doctor</p>
                </div>
                <div class="d-flex px-2 py-1 cb-base">
                  <input type="checkbox" formControlName="clinic" class="custom-check-box">
                  <p style="font-size: 13px; font-weight: 500;" class="ms-2">Clinic</p>
                </div>
                <div class="d-flex px-2 py-1 cb-base">
                  <input type="checkbox" formControlName="supplier" class="custom-check-box">
                  <p style="font-size: 13px; font-weight: 500;" class="ms-2">Supplier</p>
                </div>
                <div class="d-flex px-2 py-1 cb-base">
                  <input type="checkbox" formControlName="laboratory" class="custom-check-box">
                  <p style="font-size: 13px; font-weight: 500;" class="ms-2">Laboratory</p>
                </div>
                <div class="d-flex px-2 py-1 cb-base">
                  <input type="checkbox" formControlName="futureDentist" class="custom-check-box">
                  <p style="font-size: 13px; font-weight: 500;" class="ms-2">Future Dentist</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Body -->
        <div class="row gy-3">
          <!-- table-header -->
          <div class="col-12 p-3 py-2 card-table-header" style="border-radius: 5px;">
            <div class="row card-table-header my-1">
              <div class="col-6 my-auto text-start">
                <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">User Details - Status</h6>
              </div>
              <div class="col-3 my-auto text-center" style="border-inline: 1px solid  white;">
                <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">Contact</h6>
              </div>
              <div class="col-2 my-auto text-center" style="border-inline: 1px solid  white;">
                <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;">User Type</h6>
              </div>
              <div class="col-1 text-center my-auto">
                <h6 class="my-auto text-white" style="font-weight: 500; font-size: 14px;"></h6>
              </div>
            </div>
          </div>
          <!-- table-header -->

          <!-- table-body -->
          <div class="col-12 p-3" *ngFor="let userTemp of filteredUserTempList" style="border: 1px solid rgb(230,230,230); background-color: rgb(254, 254, 254); border-radius: 5px;">
            <div class="row">
              <div class="col-6 my-auto position-relative">
                <div class="d-grid px-1 my-auto">
                 <div class="d-grid d-lg-flex">
                  <h6 class="my-auto pe-0 pe-lg-3" style="font-weight: 500; font-size: 14px;">{{userTemp.userTitle?userTemp.userTitle+'.':''}} {{userTemp.mainName}} {{userTemp.additionalName? userTemp.additionalName:''}}  <strong>{{userTemp.registrationNumber?" - "+userTemp.registrationNumber:''}}</strong></h6>
                  <div class="text-center my-auto position-absolute" style="width: 100px;right: 20px; top: 50%;transform:translate(0%,-50%) ;" [ngSwitch]="userTemp.userTempStatus?.toString()">
                    <p *ngSwitchCase="'CREATED'" class="alert my-auto alert-info"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important">
                      Created
                    </p>

                    <p *ngSwitchCase="'SENT_USER_VERIFICATION'" class="alert my-auto alert-warning px-2"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      Email Sent
                    </p>

                    <p *ngSwitchCase="'USER_VERIFIED'" class="alert my-auto alert-warning"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      User Verified
                    </p>

                    <p *ngSwitchCase="'ADMIN_REJECTED'" class="alert my-auto alert-danger"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      Rejected
                    </p>

                    <p *ngSwitchCase="'ADMIN_APPROVED'" class="alert my-auto alert-success"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      Approved
                    </p>
                    <p *ngSwitchDefault class="alert my-auto alert-secondary"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important">
                      {{userTemp.userTempStatus?.toString()}}
                       No Status
                    </p>
                  </div>
                 </div>
                  <div class="d-grid">
                    <p class="text-black-50" style="font-size: 13px;">
                      {{ userTemp.address ? userTemp.address:'' }}{{ userTemp.city ?", "+userTemp.city: '' }}
                      {{ userTemp.district ? ", "+userTemp.district: '' }}{{ userTemp.state ? ", "+userTemp.state: '' }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-3 my-auto text-center gx-0 d-grid" style="border-left: 1px solid rgb(230,230,230); ">
                <p style="font-size: 13px;font-weight: 500;"  class="px-5 text-black-50 my-auto"><strong class="text-dark">{{userTemp.contactNumber}}</strong></p>
                <p *ngIf="userTemp.contactPerson" style="font-size: 13px;font-weight: 300;">&nbsp;&nbsp; (&nbsp; {{userTemp.contactPerson}} <em class="text-orange fst-normal" style="font-size: 12px;font-weight: 400;">{{userTemp.contactPersonDesignation?": "+userTemp.contactPersonDesignation:''}}</em> &nbsp;)</p>
              </div>
              <div class="col-2 my-auto justify-content-center d-grid" style="border-inline: 1px solid  rgb(230,230,230);">
                <p class="text-balck-50 my-auto text-capitalize alert alert-secondary px-2 py-1" style="font-size: 12px;font-weight: 500; width: fit-content;"> {{userTemp.userTempType?.toString()}} </p>
              </div>
              <div class="col-1 text-center my-auto">
                <label class="view-odrer-button my-auto me-2" style="cursor: pointer;" (click)="viewDetails(userTemp)" #OpenModalButton  title="View User"><i class="bi bi-eye"></i></label>
              </div>
            </div>
          </div>
          <!-- table-body -->

          <div class="col-12 d-none">
            <!-- Pagination -->
            <div *ngIf="filteredUserTempList.length > itemsPerPage" class="row mt-4 position-relative">
              <div class="col-12 d-flex justify-content-start g-0">
                <button (click)="goToPage(currentPage - 1)" [hidden]="currentPage === 1" class="alert bg-light me-2 border-secondary-subtle" style="font-size: 13px; padding: 10px 15px;">
                  <i class="bi-chevron-left"></i>
                </button>
                <div *ngFor="let page of visiblePages" (click)="goToPage(page)"
                    [class.active]="currentPage === page"
                    class="alert pagination-button fw-bold me-2"
                    style="font-size: 13px; padding: 10px 15px;">
                  {{ page }}
                </div>
                <button (click)="goToPage(currentPage +1)" [hidden]="currentPage === totalPages" class="alert bg-light border-secondary-subtle" style="font-size: 13px; padding: 10px 15px;">
                  <i class="bi-chevron-right"></i>
                </button>
              </div>
            </div>
            <!-- Pagination -->
          </div>

        </div>
        <!-- Body -->

        <!-- UserTemp details modal -->
        <div class="modal fade show" #exampleModal >
          <div class="modal-dialog modal-md modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
              <div class="modal-header" style="padding: 20px 30px; border-color: rgb(240,240,240);">
                <div class="d-grid">
                  <h1 class="modal-title" style="font-size: 15px; font-weight: 600;" id="exampleModalLabel">User Information</h1>
                  <p class="text-black-50" style="font-size: 13px;">A brief overview of the user's profile.</p>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body" style="padding: 20px 30px;">
                <table class="user-details-table">
                  <tr>
                    <td>Name</td>
                    <td>{{selectedUserTemp?.userTitle??''}} {{selectedUserTemp?.mainName??''}} {{selectedUserTemp?.additionalName??''}}</td>
                  </tr>
                  <tr *ngIf="selectedUserTemp?.registrationNumber">
                    <td>Registration No</td>
                    <td>{{selectedUserTemp?.registrationNumber??''}}</td>
                  </tr>
                  <tr *ngIf="selectedUserTemp?.address">
                    <td>Address</td>
                    <td>{{selectedUserTemp?.address??''}}{{ selectedUserTemp?.city ?", "+selectedUserTemp?.city: '' }}
                      {{ selectedUserTemp?.district ? ", "+selectedUserTemp?.district: '' }}{{ selectedUserTemp?.state ? ", "+selectedUserTemp?.state: '' }}</td>
                  </tr>
                  <tr>
                    <td>Contact</td>
                    <td class="p-0">
                      <p>{{selectedUserTemp?.contactNumber}}</p>
                      <p *ngIf="selectedUserTemp?.contactPerson">{{selectedUserTemp?.contactPerson}}</p>
                      <p *ngIf="selectedUserTemp?.contactPersonDesignation">{{selectedUserTemp?.contactPersonDesignation}}</p>
                    </td>
                  </tr>
                  <br>
                  <tr>
                    <td>Status</td>
                    <td [ngSwitch]="selectedUserTemp?.userTempStatus">
                      <label *ngSwitchCase="'CREATED'" class="alert my-auto alert-info"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important">
                      Created
                    </label>

                    <label *ngSwitchCase="'SENT_USER_VERIFICATION'" class="alert my-auto alert-warning px-2"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      Verfication Email Sent
                    </label>

                    <label *ngSwitchCase="'USER_VERIFIED'" class="alert my-auto alert-warning"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      User Verified
                    </label>

                    <label *ngSwitchCase="'ADMIN_REJECTED'" class="alert my-auto alert-danger"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      Rejected
                    </label>

                    <label *ngSwitchCase="'ADMIN_APPROVED'" class="alert my-auto alert-success"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important ">
                      Approved
                    </label>
                    <label *ngSwitchDefault class="alert my-auto alert-secondary"
                          style="border-radius: 5px; font-size: 11px; font-weight: 500;height: 25px; padding-block: 3px !important">
                      {{selectedUserTemp?.userTempStatus?.toString()}}
                       No Status
                    </label>
                    </td>
                  </tr>
                </table>
              </div>
              <div class="modal-footer" style="padding: 20px 30px;border-color: rgb(240,240,240);">
                <app-primary-action-button buttonType="button" buttonUI="secondary" buttonText="Close" data-bs-dismiss="modal" />
                <app-primary-action-button buttonType="button" (buttonClicked)="saveAsPermanantUser(selectedUserTemp?.userTempId)" class="ms-3" *ngIf="selectedUserTemp?.userTempStatus?.toString() == 'USER_VERIFIED' && selectedUserTemp?.userTempId" buttonUI="primary" style="border: 0;" buttonText="Approve" />
              </div>
            </div>
          </div>
        </div>
        <!-- UserTemp details modal -->
      </div>
    </div>
  </div>
</div>
