.sidebar {
  height: 100%;
  width: 20%; /* Adjust width as needed */
  position: fixed;
  top: 80px; /* Adjust based on header height */
  left: 0;
  background-color: #f8f9fa;
  padding-top: 20px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: black; /* Set text color to black */
  text-decoration: none; /* Remove underline from links */
}

.nav-link i {
  margin-right: 10px;
}

.logout {
  margin-top: auto;
  padding: 10px 15px;
}

.logout span {
  color: red; /* Set logout text color to red */
}

.nav-link span {
  margin-left: 8px; /* Adjust gap between icon and text */
  font-size: 18px; /* Set font size */
  font-weight: bold; /* Set font weight to bold */
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    top: 0;
    left: -100%;
    transition: left 0.3s;
  }
  .sidebar.open {
    left: 0;
  }
}
