<div class="row">
  <div class="col-12">
    <div class="row">
      <div class="col-12">
        <!-- Header -->
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-6 d-flex">
                <i class="bi bi-arrow-left" style="cursor: pointer;" (click)="backToOrderListView()"></i>
                <div class="ms-3">
                  <h3 class="fs-5 m-0 p-0" style="font-weight: 600">
                    Create Laboratory Service
                  </h3>
                  <p class="text-black-50 m-0 p-0" style="font-size: 12px">
                    Create Laboratory Service According to the Needs
                  </p>
                </div>
              </div>
              <div class="col-6 position-relative text-end">
                <input class="search-input position-relative" [(ngModel)]="searchTerm" (input)="applyFilters()" type="text" placeholder="Search From Here">
              </div>
            </div>
            <div class="row my-4">
              <hr class="border-secondary" />
            </div>
          </div>
        </div>
        <!-- Header -->

        <!-- Body -->
         <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-12">
                <form [formGroup]="filterForm">
                  <div class="row gx-4 gy-4 gy-lg-0">
                    <div class="col-12 col-lg-6 d-grid">
                      <p class="text-black-50 mb-2" style="font-size: 13px; font-weight: 500;">Select Laboratory Category</p>
                      <select id="category" class="custom-select px-2" name="category" formControlName="category"
                          (change)="onCategoryChange($event)">
                          <option value="-1">Select Category</option>
                          <option *ngFor="let laboratoryCategory of laboratoryCategories"
                              [value]="laboratoryCategory.laboratoryCategoryId">
                              {{ laboratoryCategory.laboratoryCategoryName }}
                          </option>
                      </select>
                    </div>
                    <div class="col-12 col-lg-6 d-grid">
                      <p class="text-black-50 mb-2" style="font-size: 13px; font-weight: 500;">Select Laboratory Sub Category</p>
                      <select id="subCategory" class="custom-select px-2" name="subCategory" formControlName="subCategory"
                          (change)="onSubCategoryChange($event)">
                          <option value="-1">Select Sub Category</option>
                          <option *ngFor="let laboratorySubCategory of laboratorySubCategories"
                              [value]="laboratorySubCategory.laboratorySubCategoryId">
                              {{ laboratorySubCategory.laboratorySubCategoryName }}
                          </option>
                      </select>
                    </div>
                  </div>
                </form>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="row row-cols-1 row-cols-lg-2 px-2">
                  <div class="col card-base" *ngFor="let item of paginatedItems">
                    <div class="row">
                      <div class="col-12 item-box g-0">
                        <div class="item-header">
                          <span class="item-title">{{item.laboratoryId.name}}</span>
                          <span class="item-contact">{{item.laboratoryId.tele}}</span>
                        </div>
                        <div class="padding pt-1 pb-3">
                          <div class="item-content">
                              <div class="item-details p-0">
                                  <p class="category-name">{{item.laboratoryCategoryId.laboratoryCategoryName}}</p>
                                  <p class="sub-category-name">
                                      {{item.laboratorySubCategoryId.laboratorySubCategoryName}}</p>
                              </div>
                              <div class="item-details text-end">
                                  <h3 style="font-weight: 600; font-size: 15px;">LKR {{item.price | number: '1.2-2'}}</h3>
                                  <p style="font-size: 13px;" [ngClass]="{
                                          'status actived': item.status === 'Active',
                                          'status inactived': item.status === 'Inactive',
                                      }">
                                      {{ item.status }}
                                  </p>
                              </div>
                          </div>
                          <div class="item-footer mt-4">
                              <button class="order-button" (click)="navigateToOrder(item)">Order Now</button>
                              <button class="details-button" (click)="viewMoreDetails(item)">
                                  View More Details
                              </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
         </div>
        <!-- Body -->

        <div class="modal" *ngIf="selectedItem">
          <div class="modal-content">
              <div class="selected-item-container">
                  <div class="selected-item-header float-end">
                      <p class="close" (click)="clearSelectedItem()">&times;</p>
                  </div>
                  <div class="row p-2">
                      <div class="selected-item-content my-3 col-12">
                          <div class="selected-item-details row">
                            <div class="col-12 g-0 py-4">
                              <table class="details-table" style="width: 100%;border-collapse: collapse;">
                                <tr>
                                  <td>Laboratory name</td>
                                  <td>{{selectedItem.laboratoryId.name}}</td>
                                </tr>
                                <tr>
                                  <td>Address</td>
                                  <td>{{selectedItem.laboratoryId.address}}</td>
                                </tr>
                                <tr>
                                  <td>City</td>
                                  <td>{{selectedItem.laboratoryId.city}}</td>
                                </tr>
                                <tr>
                                  <td>State & Country</td>
                                  <td>{{selectedItem.laboratoryId.state +" - "+selectedItem.laboratoryId.country}}</td>
                                </tr>
                                <tr>
                                  <td>Registered Date</td>
                                  <td>{{selectedItem.laboratoryId.registeredDate}}</td>
                                </tr>
                                <tr>
                                  <td>Contact Person</td>
                                  <td>{{selectedItem.laboratoryId.contactPerson}}</td>
                                </tr>
                                <tr>
                                  <td>Email & Contact Number</td>
                                  <td>{{selectedItem.laboratoryId.email +" - "+selectedItem.laboratoryId.tele}}</td>
                                </tr>
                                <br><br>
                                <tr>
                                  <td>Category Name</td>
                                  <td>{{selectedItem.laboratoryCategoryId.laboratoryCategoryName}}</td>
                                </tr>
                                <tr>
                                  <td>SubCategory Name</td>
                                  <td>{{selectedItem.laboratorySubCategoryId.laboratorySubCategoryName}}</td>
                                </tr>
                                <tr>
                                  <td>Price</td>
                                  <td>LKR {{ selectedItem.price | number: '1.2-2' }}</td>
                                </tr>
                                <tr>
                                  <td>Availability</td>
                                  <td [ngClass]="{
                                    'status actived': selectedItem.status === 'Active',
                                    'status inactived': selectedItem.status === 'Inactive'
                                }">{{ selectedItem.status }}</td>
                                </tr>
                              </table>
                            </div>
                          </div>
                      </div>
                      <div class="selected-item-footer px-0">
                        <app-primary-action-button buttonText="Order Now" buttonUI="primary" buttonType="button" (buttonClicked)="navigateToOrder(selectedItem)" class="me-3"/>
                        <app-primary-action-button buttonText="Back To Services" buttonUI="secondary" buttonType="button" (buttonClicked)="clearSelectedItem()"/>
                      </div>
                  </div>
              </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
