import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LaboratoryDashboardComponent } from './laboratory-dashboard/laboratory-dashboard.component';
import { LaboratoryLayoutComponent } from './laboratory-layout/laboratory-layout.component';
import { AddLaboratoryServicesComponent } from './add-laboratory-services/add-laboratory-services.component';
import { LaboratoryInvoiceComponent } from './laboratory-invoice/laboratory-invoice.component';
import { LaboratorySetupListComponent } from './laboratory-setup-list/laboratory-setup-list.component';
import { LaboratoryClinicOrderListComponent } from './laboratory-clinic-order-list/laboratory-clinic-order-list.component';
import { authGuard } from '../auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: LaboratoryLayoutComponent,
    canActivateChild:[authGuard],
    children: [
      { path: '', component: LaboratoryDashboardComponent },
      { path: 'dashboard', component: LaboratoryDashboardComponent },
      { path: 'add-service', component: AddLaboratoryServicesComponent },
      { path: 'invoice', component: LaboratoryInvoiceComponent },
      { path: 'payment', component: LaboratoryInvoiceComponent },
      { path: 'setup', component: LaboratorySetupListComponent },
      { path: 'invoice-list', component: LaboratoryInvoiceComponent },
      { path: 'setup-list', component: LaboratorySetupListComponent },
      { path: 'all-orders', component: LaboratoryClinicOrderListComponent },
      { path: '**', redirectTo: '' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LaboratoryRoutingModule {}
