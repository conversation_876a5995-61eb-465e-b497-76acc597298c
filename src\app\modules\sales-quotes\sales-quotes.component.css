/* Container for the entire component */

.sales-quotes-container {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}


/* Header and search container */

.header-search-container {
    display: flex;
    justify-content: space-between;
    /* Adjust alignment as needed */
    align-items: center;
    margin-bottom: 20px;
}

.header-search-container h2 {
    margin: 0;
    color: #333;
}

.search-container {
    display: flex;
}

.search-container input[type="text"] {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px 0 0 4px;
    outline: none;
}

.search-container .search-button {
    padding: 10px;
    border: 1px solid #ccc;
    border-left: none;
    border-radius: 0 4px 4px 0;
    background-color: rgba(216, 83, 34, 1);
    color: white;
    cursor: pointer;
}

.search-container .search-button i {
    font-size: 16px;
}


/* Table container */

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

th,
td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: white;
    color: #333;
}

tr:hover {
    background-color: #f1f1f1;
}

.view-quote-button {
    padding: 10px 20px;
    border: 1px solid rgba(216, 83, 34, 1);
    background-color: white;
    color: rgba(216, 83, 34, 1);
    ;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
}

.view-quote-button:hover {
    background-color: rgba(216, 83, 34, 1);
    ;
    color: white;
}


/* Media queries for responsiveness */

@media (max-width: 768px) {
    .header-search-container {
        flex-direction: column;
        align-items: stretch;
    }
    .search-container {
        margin-top: 10px;
        width: 100%;
    }
    .search-container input[type="text"],
    .search-container .search-button {
        width: 100%;
        margin: 0;
        border-radius: 4px;
    }
    table {
        display: block;
    }
    thead {
        display: none;
        background: rgba(255, 176, 125, 0.22);
    }
    tbody,
    tr,
    td {
        display: block;
        width: 100%;
    }
    tr {
        margin-bottom: 15px;
    }
    td {
        text-align: right;
        padding-left: 50%;
        position: relative;
    }
    td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 50%;
        padding-left: 15px;
        font-weight: bold;
        text-align: left;
    }
}