import { Component, Input, OnInit } from '@angular/core';
import { ClinicAppointment } from '../clinic';
import { ClinicService } from '../clinic.service';
import Swal from 'sweetalert2';
import { MatDialog } from '@angular/material/dialog';
import { AppointmentDetailsDialogComponent } from '../clinic-dashboard/appointment-popup/appointment-details-dialog/appointment-details-dialog.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-clinic-dashboard',
  templateUrl: './clinic-dashboard.component.html',
  styleUrls: ['./clinic-dashboard.component.css']
})

export class ClinicDashboardComponent implements OnInit {

  @Input() status: any;

  appointments: any = null;

  selectedClinicAppointment: ClinicAppointment | null = null;
  selectedClinicAppointments: Set<number> = new Set();

  constructor(
    private clinicService: ClinicService, 
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAppointments();
  }

  // Fetch appointments based on clinicId from local storage
  loadAppointments() {
    const companyIdString = localStorage.getItem('userid'); // Retrieve clinicId from localStorage
    console.log('Company ID (string):', companyIdString);

    if (companyIdString) {
      const companyId = Number(companyIdString); // Convert the string to a number
      console.log('Company ID (number):', companyId);
      this.clinicService.getAppointmentsByClinicId(companyId).subscribe(
        (data) => {
          console.log('Appointments data:', data); // Add this to check the response
          this.appointments = data;
        },
        (error: any) => {
          console.error('Error fetching appointments:', error);
        }
      );
      
    } else {
      console.error('Company ID not found in local storage');
    }
  }


  updateStatus(appointmentId: number, status: String): void {
    if (appointmentId === undefined) {
        console.error('Appointment ID is undefined');
        return; // Early exit to avoid making an invalid HTTP call
    }

    this.clinicService.updateAppointmentStatus(appointmentId, status).subscribe(
        () => {
            console.log('Status updated successfully');
        },
        error => console.error(error)
    );
}


acceptAppointment(appointment: any) {
  if (!appointment || !appointment.appointmentId) {
    console.error("Appointment is undefined or missing appointmentId.");
    return;
  }

  // Open the appointment details in a dialog
  const dialogRef = this.dialog.open(AppointmentDetailsDialogComponent, {
    width: '400px',
    data: appointment
  });

  // Handle the result after closing the dialog
  dialogRef.afterClosed().subscribe(result => {
    if (result === 'accept') {
      Swal.fire({
        title: 'Are you sure?',
        text: "Do you want to accept this appointment?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#00C820',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, accept it!'
      }).then((result) => {
        if (result.isConfirmed) {
          this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'Accepted').subscribe(
            (response) => {
              console.log('Appointment accepted:', response);
              appointment.status = 'Accepted';  
              this.loadAppointments(); 
              Swal.fire('Accepted!', 'The appointment has been accepted.', 'success');
            },
            (error) => {
              console.log('Error accepting appointment:', error);
            }
          );
        }
      });
      
    } else if (result === 'reject') {
      // Reject the appointment
      Swal.fire({
        title: 'Are you sure?',
        text: "Do you want to reject this appointment?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#B93426',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, reject it!'
      }).then((result) => {
        if (result.isConfirmed) {
          this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'Rejected').subscribe(
            (response) => {
              console.log('Appointment rejected:', response);
              appointment.status = 'Rejected';  
              this.loadAppointments(); 
              Swal.fire('Rejected!', 'The appointment has been rejected.', 'success');
            },
            (error) => {
              console.log('Error rejecting appointment:', error);
            }
          );
        }
      });
    }
  });
}


rejectAppointment(appointment: any) {
  if (!appointment || !appointment.appointmentId) {
    console.error("Appointment is undefined or missing appointmentId.");
    return;
  }

  Swal.fire({
    title: 'Are you sure?',
    text: "Do you want to reject this appointment?",
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#B93426',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Yes, reject it!'
  }).then((result) => {
    if (result.isConfirmed) {
      this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'Rejected').subscribe(
        (response) => {
          console.log('Appointment rejected:', response);
          appointment.status = 'Rejected';  
          this.loadAppointments(); 
          Swal.fire('Rejected!', 'The appointment has been rejected.', 'success');
        },
        (error) => {
          console.log('Error rejecting appointment:', error);
        }
      );
    }
  });
}



  confirmAppointment(appointment: ClinicAppointment, event: Event): void {
    event.stopPropagation(); // Prevents the event from bubbling up to the parent elements
    appointment.isConfirmed = true;
    // Add logic to handle the confirmation of the appointment
  }



  openAppointmentDetails(appointment: ClinicAppointment): void {
    this.selectedClinicAppointment = appointment;
  }

  closeAppointmentDetails(): void {
    this.selectedClinicAppointment = null;
  }

  toggleAppointmentSelection(id: number): void {
    if (this.selectedClinicAppointments.has(id)) {
      this.selectedClinicAppointments.delete(id);
    } else {
      this.selectedClinicAppointments.add(id);
    }
  }

  deleteSelectedAppointments(): void {
    this.appointments = this.appointments.filter((a: { id: number; }) => !this.selectedClinicAppointments.has(a.id));
    this.selectedClinicAppointments.clear();
  }

  getPendingAppointmentsCount(): number {
    return this.appointments.filter((a: { status: string }) => a.status === 'Pending').length;
  }

  getConfirmedAppointmentsCount(): number {
    return this.appointments.filter((a: { status: string }) => a.status === 'Accepted').length;
  }

  goToCreateNewAppointment(){
    this.router.navigate(['clinic/new-appointment']);
  }
}
